### Authenticate User
POST {{sso-url}}
Content-Type: application/x-www-form-urlencoded

client_id={{client-id}}&username=sample_app_user&password=secret&grant_type=password

> {%
    client.global.set("auth_token", response.body.access_token);
  %}

### Authenticate Admin
POST {{sso-url}}
Content-Type: application/x-www-form-urlencoded

client_id={{client-id}}&username=sample_app_admin&password=secret&grant_type=password

> {%
    client.global.set("auth_token", response.body.access_token);
  %}

### Get All Aircraft
GET {{api-url}}/api/aircrafts
Content-Type: application/json
Authorization: Bearer {{auth_token}}

### Create new Aircraft
POST {{api-url}}/api/aircrafts
Content-Type: application/json
Authorization: Bearer {{auth_token}}

{
  "registration": "D-AIMA",
  "manufacturer": "Airbus",
  "aircraftStatus": "ACTIVE",
  "manufacturingDate": "1991-08-21T13:00:00+01:00",
  "engineCount": 4,
  "ownerId": "8bb9cad1-038e-46e3-ae26-b515d08a78b8"
}

### Delete Aircraft
DELETE {{api-url}}/api/aircrafts/1
Content-Type: application/json
Authorization: Bearer {{auth_token}}

### Get Aircraft by ID
GET {{api-url}}/api/aircrafts/1
Content-Type: application/json
Authorization: Bearer {{auth_token}}

### Update Aircraft
PATCH {{api-url}}/api/aircrafts/1
Content-Type: application/json
Authorization: Bearer {{auth_token}}

{
  "registration": "D-KMMK"
}

### Search Aircraft
GET {{api-url}}/api/aircrafts/search?query=Airbus&ownerUUID=8bb9cad1-038e-46e3-ae26-b515d08a78b8&aircraftStatus=ACTIVE&fromDate=1900-01-01T00:00:00%2B01:00&toDate=2100-12-31T23:59:59%2B01:00
Content-Type: application/json
Authorization: Bearer {{auth_token}}

### Health Check
GET http://127.0.0.1:8090/status/health

### LivenessProbe Check
GET http://127.0.0.1:8090/status/health/liveness

### ReadinessProbe Check
GET http://127.0.0.1:8090/status/health/readiness

### Prometheus Check
GET http://127.0.0.1:8090/status/prometheus

### Get User List
GET {{api-url}}/api/users
Content-Type: application/json
Authorization: Bearer {{auth_token}}

### Find User By Id
GET {{api-url}}/api/users/8bb9cad1-038e-46e3-ae26-b515d08a78b8
Content-Type: application/json
Authorization: Bearer {{auth_token}}

### Get Current User
GET {{api-url}}/api/users/current-user
Content-Type: application/json
Authorization: Bearer {{auth_token}}

### Get Current User's roles
GET {{api-url}}/api/users/current-user/roles
Content-Type: application/json
Authorization: Bearer {{auth_token}}

###
