#!groovy

pipeline {
  agent {
    label 'java17'
  }

  options {
    buildDiscarder(logRotator(artifactDaysToKeepStr: '', artifactNumToKeepStr: '', daysToKeepStr: '', numToKeepStr: '10'))
    disableConcurrentBuilds()
  }

  environment {
    APP_NAME = "coca-backend-service"
    OC_PROJECT = "t-core-calc-nonprod"
    HELM_CHART_DIR = "cicd/helm"
    DEPLOYMENT_STAGE = "develop"
    DEPLOYMENT_CONFIG = "${APP_NAME}-${DEPLOYMENT_STAGE}"
    BRANCH = "${env.GIT_BRANCH.replace('/', '-')}"
  }

  stages {
    stage("Build") {
      steps {
        script {
          sh "mvn clean install -P coverage"
        }
      }
    }

    stage("Sonar scan") {
      steps {
        script {
          scanSonar('appID': '2938')
        }
      }
    }

    stage("Snyk scan") {
      steps {
        script {
          scanSnyk('appID': '2938', 'scope': 'backend', 'pathToTarget': './pom.xml', 'scanAuto': true, 'scanBranches': ['develop', 'release', 'master'])
        }
      }
    }

    stage("Deploy") {
      steps {
        script {
          loginToOCP(cluster: 'azureTI', namespace: '${OC_PROJECT}', token: 'OpenShift4Token')
          
          sh """
            helm upgrade --install ${DEPLOYMENT_CONFIG} ${HELM_CHART_DIR} \
              -f ${HELM_CHART_DIR}/values.yaml \
              -f ${HELM_CHART_DIR}/values.${DEPLOYMENT_STAGE}.yaml \
              --atomic \
              --dependency-update

            oc start-build ${APP_NAME} --from-dir=target --follow --wait

            oc tag ${OC_PROJECT}/${APP_NAME}:latest ${OC_PROJECT}/${APP_NAME}:${BRANCH}
          """

          isManuallyTriggered = !currentBuild.getBuildCauses('hudson.model.Cause$UserIdCause').isEmpty()

          if (env.GIT_BRANCH == 'develop' || isManuallyTriggered) {
            sh """
              oc tag ${OC_PROJECT}/${APP_NAME}:latest ${OC_PROJECT}/${APP_NAME}:${DEPLOYMENT_STAGE}

              oc rollout status deploymentconfig ${DEPLOYMENT_CONFIG} --watch=true --timeout=5m
            """
          }
        }
      }
    }
  }

  post {
    always {
      script {
        currentBuild.result = currentBuild.result ?: 'SUCCESS'
        notifyBitbucket()
      }
    }
    cleanup { 
      cleanWs()
    }
  }
}
