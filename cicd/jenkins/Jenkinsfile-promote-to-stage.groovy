#!groovy

pipeline {
  agent {
    label 'java17'
  }

  options {
    buildDiscarder(logRotator(artifactDaysToKeepStr: '', artifactNumToKeepStr: '', daysToKeepStr: '', numToKeepStr: '10'))
    disableConcurrentBuilds()
  }

  environment {
    APP_NAME = "coca-backend-service"
    OC_PROJECT = "t-core-calc-nonprod"
    HELM_CHART_DIR = "cicd/helm"
    DEPLOYMENT_STAGE = "stage"
    DEPLOYMENT_CONFIG = "${APP_NAME}-${DEPLOYMENT_STAGE}"
    BRANCH = "${env.GIT_BRANCH.replace('/', '-')}"
  }

  stages {
    stage("Deploy") {
      steps {
        script {
          loginToOCP(cluster: 'azureTI', namespace: '${OC_PROJECT}', token: 'OpenShift4Token')

          sh """
            helm upgrade --install ${DEPLOYMENT_CONFIG} ${HELM_CHART_DIR} \
              -f ${HELM_CHART_DIR}/values.yaml \
              -f ${HELM_CHART_DIR}/values.${DEPLOYMENT_STAGE}.yaml \
              --reuse-values \
              --atomic \
              --dependency-update

            oc tag ${OC_PROJECT}/${APP_NAME}:${BRANCH} ${OC_PROJECT}/${APP_NAME}:${DEPLOYMENT_STAGE}

            oc rollout status deploymentconfig ${DEPLOYMENT_CONFIG} --watch=true --timeout=5m
      	  """
        }
      }
    }
  }

  post {
    always {
      script {
        currentBuild.result = currentBuild.result ?: 'SUCCESS'
        notifyBitbucket()
      }
    }
    cleanup { 
      cleanWs()
    }
  }
}
