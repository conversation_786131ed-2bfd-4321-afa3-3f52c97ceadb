global:
  stage: prod
  namespace: t-core-calc-prod

lht-platform-java:
  base-config:
    buildConfig:
      include: false
    
    # Enabling the pod disruption budget will prevent unwanted (but i.e. manually caused) deletions and ensures service availability
    # More information: https://kubernetes.io/docs/concepts/workloads/pods/disruptions/
    podDisruptionBudget:
      include: true
      maxUnavailable: 1
    
    deploymentConfig:
      env:
        LHT_LOGGING_STAGE: prod

      envFrom:
        coca-backend-service-prod: secretRef
        coca-eventhub-prod: secretRef

      volumes:
        - name: cephfsvol
          persistentVolumeClaim:
            claimName: core-calc-quotation-events-prod

      volumeMounts:
        - name: cephfsvol
          mountPath: /events/quotations
          subPath: production
    
    route:
      prefix: core-calculation-service
