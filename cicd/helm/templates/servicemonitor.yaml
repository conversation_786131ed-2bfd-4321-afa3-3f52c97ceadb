apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  labels:
    app: {{ .Values.global.name }}
    lufthansa-technik.de/stage: {{ .Values.global.stage }}
  name: {{ .Values.global.name }}-{{ .Values.global.stage }}-monitor
  namespace: {{ .Values.global.namespace }}
spec:
  endpoints:
  - interval: 30s
    path: /api/actuator/prometheus
    targetPort: 8080
    scheme: http
  selector:
    matchLabels:
      app: {{ .Values.global.name }}
      lufthansa-technik.de/stage: {{ .Values.global.stage }}