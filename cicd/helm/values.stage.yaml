global:
  stage: stage
  namespace: t-core-calc-nonprod

lht-platform-java:
  base-config:
    buildConfig:
      include: false

    imageStream:
      include: false

    deploymentConfig:
      env:
        LHT_LOGGING_STAGE: stage
      
      envFrom:
        coca-backend-service-stage: secretRef
        coca-eventhub-stage: secretRef

      volumeMounts:
        - name: cephfsvol
          mountPath: /events/quotations
          subPath: stage
    
    route:
      prefix: core-calculation-service-stage
