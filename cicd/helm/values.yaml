global:
  name: coca-backend-service
  appId: 2938
  host: azureTI
  managedBy: Core Calculation Team

lht-platform-java:
  base-config:
    lhtmetrics:
      enabled: true
      port: 8080
      path: /api/actuator/prometheus
      scheme: http

    lhtLogging:
      enabled: true

    buildConfig:
      baseImage:
        name: lht-platform-java17
      outputImageTag: latest

    deploymentConfig:
      env:
        JAVA_OPTIONS: -Xmx1400m

      podAntiAffinity:
        requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              matchExpressions:
                - key: app.kubernetes.io/name
                  operator: In
                  values:
                    - '{{ include "app.fullname" . }}'
            topologyKey: kubernetes.io/hostname

      resources:
        requests:
          cpu: 200m
          memory: 750Mi
        limits:
          cpu: 400m
          memory: 1400Mi

      livenessProbe:
        enabled: true
        path: /api/actuator/health/liveness
        port: 8080
        initialDelaySeconds: 60

      readinessProbe:
        enabled: true
        path: /api/actuator/health/readiness
        port: 8080
        initialDelaySeconds: 60

      volumes:
        - name: cephfsvol
          persistentVolumeClaim:
            claimName: core-calc-quotation-events-dev
