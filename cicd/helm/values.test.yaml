global:
  stage: test
  namespace: t-core-calc-nonprod

lht-platform-java:
  base-config:
    buildConfig:
      include: false

    imageStream:
      include: false

    deploymentConfig:
      env:
        LHT_LOGGING_STAGE: test
      
      envFrom:
        coca-backend-service-test: secretRef
        coca-eventhub-test: secretRef

      volumeMounts:
        - name: cephfsvol
          mountPath: /events/quotations
          subPath: test
    
    route:
      prefix: core-calculation-service-test
