# Spring Sample App

![Maturity: Stable](https://img.shields.io/badge/maturity-stable-brightgreen?style=flat-square)
![OpenShift: OnPrem](https://img.shields.io/badge/OpenShift-OnPrem-informational?style=flat-square&logo=Red-Hat&logoColor=red)
![Helm: 3.0.0](https://img.shields.io/badge/Helm-3.0.0-informational?style=flat-square&logo=helm)

This is a sample app to build a backend application based on [Spring Boot](https://spring.io/projects/spring-boot).

For other sample apps, please check this link: [Software Development Guidelines](https://lht.app.lufthansa.com/confluence/display/SDG/Application+Blueprints)

## Getting Started

To start your own new application clone this repository.
Before you initialize your own Git-Repository remove the current `.git` folder first.

## Why to Use

You should use this sample app when you want to create a new stand-alone Java backend application.
*Spring Boot* offers use a way faster startup time compared to *JBoss* applications.
Once `spring-graalvm-native` is ready the startup time will be similar to that of *Quarkus*.

Apart from the startup time the *Spring* sample app brings you great development productivity with almost no need for configuration thanks to the opinionated approach of *Spring Boot*.

## Features

* Code formatting via *Maven* plugin
* *SonarQube* integration
* Artifact deployment to *Artifactory* ([LHT.Repo](https://lht.app.lufthansa.com/artifactory/webapp/#/home))
* [CRUD OpenAPI Spec](https://lht.app.lufthansa.com/stash/projects/LHT-P-SA/repos/crud-sample-openapi-spec/browse) implemented
* DB connection to *MariaDB*
* DB migrations with *Flyway*
* *OpenAPI* documentation + web ui via [springdoc](https://github.com/springdoc/springdoc-openapi)
* OpenID Connect authentication to LHT *Keycloak*
* Role-based access control (RBAC)
* Health checks (liveness, readiness) by *Spring Boot Actuator*
* Deployment using *Helm* templates
* *Snyk* support
* Integration with LHT.logging

### Authorization

> On LHT.Platform Authentication and Authorisation is done by OpenID Connect. For using the RedHat SSO with OpenID Connect for a new client/project, an onboarding is necessary. Developer or Application Owner <NAME_EMAIL>.

Authorization is done using the OpenID connect support of *Spring* security and the LHT *Keycloak*.
The sample app uses a configured `sample_app` client that has two users:
- sample_app_user
- sample_app_admin

Check out the `requests.http` file where you can authenticate as one of the two and fire up requests against the API.

If you wish you can also launch a local instance of *Red Hat* SSO which is configured like the *LHT* SSO.
Just check out this [repository](https://lht.app.lufthansa.com/stash/projects/LHT-P-SA/repos/local-lht-sso/browse) and change the `SSO_JWK_SET_URI`.

### Health Services

This application uses *Spring Boot Actuator* to provide basic health checks.


|***Route*** | ***Description***|
| -------- | ------ |
| ```/status/health/liveness``` | The application is up and running. |
| ```/status/health/readiness``` | The application is ready to serve requests. |

For more information check out [Spring Boot Actuator](https://docs.spring.io/spring-boot/docs/current/reference/html/production-ready-features.html#production-ready).

### Databases

We use *Flyway* for the database migrations. For database changes, please create a new migration file
with a version, it will be applied at the next app start.

To run a *MariaDB* instance locally just run ```$ docker-compose -f src/docker/docker-compose.yml up db```.
This database is getting pre-filled at application start up.

The database configuration (see `application.yaml`) can be controlled by environment variables, the default values are for the local instance.


### OpenAPI Documentation

*OpenAPI* documentation is generated via [springdoc](https://github.com/springdoc/springdoc-openapi).
The JSON specification is available at http://localhost:8080/api/v3/api-docs and the web ui is served at http://localhost:8080/api/swagger-ui/index.html.

## Configuration

The application should be configured via environment variables.
Environment variables will overwrite certain stage/deployment specific properties in the `application.yaml`.
In OpenShift the environment variables are provided via the `DeploymentConfig`.

| Variable | Effect |
| -------- | ------ |
| `DB_JDBC_URL` | JDBC connection string. |
| `DB_USERNAME` | Database username. |
| `DB_PASSWORD` | Database password. |
| `SSO_CLIENT_ID` | The SSO client. |
| `SSO_JWK_SET_URI` | URL to the SSO certificate endpoint. |
| `CORS_ALLOWED_ORIGINS` | Comma separated allowed CORS origin URLs. |
| `LOG_LEVEL` | Sets the *Spring* root log level. |
| `LHT_LOGGING_STAGE` | *stage* field within *LHT.logging* |

## Development

We suggest that you develop against the local *MariaDB* instance.
You can build, run and test your application as usual with *Maven*.

Disable the *stash* appender in [logback.xml](src/main/resources/logback.xml) 
for local development without a logstash server. 

- `mvn package`
- `mvn verify`
- `mvn spring-boot:run`.

## Build and Deployment Process

### Build (CI)

The application is built on the *Jenkins* via `Jenkinsfile`.
It has quality gates for the correct formatting and tests.
It also deploys the results to [SonarQube](https://sonar.app.lhtcloud.com/dashboard?id=2350_lht-platform-team%3Alht-platform-sample-applications%3Aspring-sample)
and security reports to [Snyk.io](https://app.snyk.io/org/lht-ti-lht-platform-team/project/eda1015a-0698-4e7d-a82d-9b42a6ce3435).

### Deployment (CD)

We configure our deployment via *Helm* charts.
Please check the `src/cicd/helm` folder in the source files.
Have a look at the deployment stage in the `Jenkinsfile` for further information.

## How to Adapt

Before you deploy your application you will have to change at least a couple of things.
In the *Helm* chart you will have to change the application id and adjust some environment variables.
In the `Jenkinsfile` your will want to change the application name.

Apart from that we use a *OpenAPI* spec (`src/main/resources/crud-sample-openapi-spec.yaml`) to generate controller-interfaces and our model-classes.
If you don't want to generate your interfaces and models like that you can remove that file as well as the `openapi-generator-maven-plugin`step from the `pom.xml`.

## Hyperlinks

| Link | Description |
| -------- | ------ |
| [Deployment](https://spring-sample-develop.apps.ham.lhtcloud.com/) | Deployment of Spring Sample App |
| [Software Development Guidelines](https://lht.app.lufthansa.com/confluence/display/SDG/Guidelines) | Link to the LHT Software Development Guidelines |
