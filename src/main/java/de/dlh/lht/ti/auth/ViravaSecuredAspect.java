package de.dlh.lht.ti.auth;

import com.cleverpine.viravaspringhelper.aop.BaseViravaSecuredAspect;
import com.cleverpine.viravaspringhelper.core.ViravaPrincipalProvider;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.stereotype.Component;

@Aspect
@Component
public class ViravaSecuredAspect extends BaseViravaSecuredAspect {

    public ViravaSecuredAspect(ViravaPrincipalProvider principalProvider) {
        super(principalProvider);
    }

    @Before("@annotation(viravaSecured)")
    public void secure(JoinPoint joinPoint, ViravaSecured viravaSecured) {
        authorize(
                joinPoint,
                viravaSecured.resource(),
                viravaSecured.resourceIdParamName(),
                viravaSecured.requireAllResourceIds(),
                viravaSecured.scope());
    }
}
