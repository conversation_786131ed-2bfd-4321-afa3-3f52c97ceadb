package de.dlh.lht.ti.auth;

import com.cleverpine.viravaspringhelper.config.AuthTokenConfig;
import com.cleverpine.viravaspringhelper.core.ViravaAuthenticationToken;
import com.cleverpine.viravaspringhelper.core.ViravaPrincipalProvider;
import com.cleverpine.viravaspringhelper.core.ViravaUserPrincipal;
import de.dlh.lht.ti.auth.contract.UserPrincipalProvider;
import de.dlh.lht.ti.auth.roles.Role;
import de.dlh.lht.ti.configuration.properties.UserDetailsProperties;
import de.dlh.lht.ti.exception.AuthenticationException;
import de.dlh.lht.ti.mapper.PermissionMapper;
import de.dlh.lht.ti.model.Permission;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;


import static de.dlh.lht.ti.utils.ErrorMessages.AUTH_TOKEN_NOT_PROVIDED_ERROR_MESSAGE;

@Component
public class UserPrincipalProviderImpl extends ViravaPrincipalProvider<UserPrincipal> implements UserPrincipalProvider {

    private final PermissionMapper permissionMapper;
    private final UserDetailsProperties userDetailsProperties;
    private final AuthTokenConfig authTokenConfig;

    protected UserPrincipalProviderImpl(
            PermissionMapper permissionMapper,
            UserDetailsProperties userDetailsProperties,
            AuthTokenConfig authTokenConfig
    ) {
        super(UserPrincipal.class);

        this.permissionMapper = permissionMapper;
        this.userDetailsProperties = userDetailsProperties;
        this.authTokenConfig = authTokenConfig;
    }

    @Override
    public List<Role> getRoles() {
        var authenticationToken = getAuthentication().orElse(null);
        if(authenticationToken == null) {
            return Collections.emptyList();
        }

        var rawRoles = authenticationToken.getRoles();
        if(rawRoles == null || rawRoles.isEmpty()) {
            return Collections.emptyList();
        }

        return permissionMapper.convertListOfRolesFromResources(rawRoles);
    }

    @Override
    public List<Permission> getPermissions() {
        return getAuthentication()
                .map(ViravaAuthenticationToken::getPrincipal)
                .map(ViravaUserPrincipal::getPermissionList)
                .orElse(Collections.emptyList())
                .stream().map(permissionMapper::convertPermission)
                .collect(Collectors.toList());
    }

    @Override
    public UserPrincipal getUserPrincipal() {
        var username = getBasePrincipal().getUsername();

        return provideCustomPrincipalInfo(username);
    }

    public ViravaUserPrincipal getBasePrincipal() {
        return getAuthentication().map(ViravaAuthenticationToken::getPrincipal).orElse(null);
    }

    public UserPrincipal get() {
        return getCustomPrincipalInfo();
    }

    @Override
    public UserPrincipal provideCustomPrincipalInfo(String username) {
        var authToken = getAuthentication()
                .orElseThrow(() -> new AuthenticationException(AUTH_TOKEN_NOT_PROVIDED_ERROR_MESSAGE));

        var name = authToken.getJsonAttr(userDetailsProperties.fullNamePath());
        var userUNumber = authToken.getJsonAttr(authTokenConfig.getUsernamePath());
        var email = authToken.getJsonAttr(authTokenConfig.getEmailPath());

        return new UserPrincipal(name, userUNumber, email);
    }
}
