package de.dlh.lht.ti.auth;

import com.cleverpine.viravaspringhelper.dto.ScopeType;
import de.dlh.lht.ti.auth.roles.Resources;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface ViravaSecured {
   
    Resources resource();

    ScopeType[] scope();

    String resourceIdParamName() default "";

    boolean requireAllResourceIds() default false;
}
