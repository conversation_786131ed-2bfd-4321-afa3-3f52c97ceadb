package de.dlh.lht.ti.auth.roles;

import com.cleverpine.viravaspringhelper.core.BaseResource;
import java.util.List;

public enum Resources implements BaseResource {

    FILTERS,
    PROJECT,
    QUOTATION,
    USER;

    @Override
    public String resource() {
        return toString();
    }

    @Override
    public List<BaseResource> getFullResourceList() {
        return List.of(Resources.values());
    }
}
