package de.dlh.lht.ti.auth.roles;

import com.cleverpine.viravaspringhelper.core.BaseRole;
import com.cleverpine.viravaspringhelper.dto.Permission;
import com.cleverpine.viravaspringhelper.dto.Scope;
import java.util.List;

public enum Role implements BaseRole {
    VIEW_ONLY_USER("view_only", List.of(
            Permission.of(Resources.FILTERS, Scope.READ),
            Permission.of(Resources.PROJECT, Scope.READ),
            Permission.of(Resources.QUOTATION, Scope.READ),
            Permission.of(Resources.USER, Scope.READ))),
    CALCULATION_USER("user", List.of(
            Permission.of(Resources.FILTERS, Scope.READ),
            Permission.of(Resources.PROJECT, Scope.CRU),
            Permission.of(Resources.QUOTATION, Scope.CRUD),
            Permission.of(Resources.USER, Scope.CRU))),
    ADMIN_USER("admin", List.of(
            Permission.of(Resources.FILTERS, Scope.READ),
            Permission.of(Resources.QUOTATION, Scope.CRUD),
            Permission.of(Resources.USER, Scope.CRUD),
            Permission.of(Resources.PROJECT, Scope.CRUD)));

    private final List<Permission> permissionList;

    private final String roleName;

    Role(String roleName, List<Permission> permissionList) {
        this.roleName = roleName;
        this.permissionList = permissionList;
    }

    @Override
    public String getRoleName() {
        return roleName;
    }

    @Override
    public List<Permission> getPermissionList() {
        return List.copyOf(permissionList);
    }
}
