package de.dlh.lht.ti.configuration;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.ExchangeStrategies;
import org.springframework.web.reactive.function.client.WebClient;

@Getter
@Configuration
@ConfigurationPropertiesScan
public class AppConfig {

    @Value("${anka-importer-service.base-url}")
    public String ankaImporterServiceBaseUrl;

    @Bean
    public WebClient ankaImporterWebClient() {
        var size = 1024 * 1024 * 1024;
        var strategies = ExchangeStrategies.builder()
                .codecs(codecs -> codecs.defaultCodecs().maxInMemorySize(size))
                .build();
        return WebClient
                .builder()
                .exchangeStrategies(strategies)
                .baseUrl(ankaImporterServiceBaseUrl)
                .build();
    }
}
