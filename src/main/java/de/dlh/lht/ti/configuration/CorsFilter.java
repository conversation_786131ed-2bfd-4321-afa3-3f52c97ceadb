package de.dlh.lht.ti.configuration;

import static java.util.Arrays.asList;

import java.io.IOException;
import java.util.regex.Pattern;
import jakarta.servlet.Filter;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Component
@Order(Ordered.HIGHEST_PRECEDENCE)
@NoArgsConstructor(access = AccessLevel.PUBLIC)
public class CorsFilter implements Filter {

  private static final Pattern COMMA_SEPARATED_SPLIT_REGEX = Pattern.compile("\\s*,\\s*");

  @Value("${cors.allowed-origins}")
  private String allowedOrigins;

  @Override
  public void doFilter(ServletRequest req, ServletResponse res, FilterChain chain)
      throws IOException, ServletException {
    var response = (HttpServletResponse) res;
    var request = (HttpServletRequest) req;
    var originHeader = request.getHeader("Origin");

    if (asList(COMMA_SEPARATED_SPLIT_REGEX.split(allowedOrigins)).contains(originHeader)) {
      response.setHeader("Access-Control-Allow-Origin", originHeader);
    }

    response.setHeader("Access-Control-Expose-Headers", "Allow");
    response.setHeader("Access-Control-Allow-Credentials", "true");
    response.setHeader("Access-Control-Allow-Methods", "POST, PUT, PATCH, GET, OPTIONS, DELETE");
    response.setHeader("Access-Control-Max-Age", "3600");
    response.setHeader(
        "Access-Control-Allow-Headers", "x-requested-with, authorization, content-type");

    if ("OPTIONS".equalsIgnoreCase(request.getMethod())
        && request.getHeader("Authorization") == null) {
      response.setStatus(HttpServletResponse.SC_OK);
    } else {
      chain.doFilter(req, res);
    }
  }
}
