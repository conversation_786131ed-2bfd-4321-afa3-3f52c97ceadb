package de.dlh.lht.ti.configuration;

import com.cleverpine.viravaspringhelper.error.ViravaAuthErrorEntryPoint;
import com.cleverpine.viravaspringhelper.filter.ViravaFilter;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.actuate.autoconfigure.security.servlet.EndpointRequest;
import org.springframework.boot.actuate.health.HealthEndpoint;
import org.springframework.boot.actuate.info.InfoEndpoint;
import org.springframework.boot.actuate.metrics.MetricsEndpoint;
import org.springframework.boot.actuate.metrics.export.prometheus.PrometheusScrapeEndpoint;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.www.BasicAuthenticationFilter;

@Configuration
@EnableWebSecurity
@RequiredArgsConstructor
public class SecurityConfig {

    private final ObjectMapper objectMapper;
    private final ViravaFilter authoritiesFilter;


    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        return http
                .csrf(AbstractHttpConfigurer::disable)
                .authorizeHttpRequests((authorize) -> authorize
                        .requestMatchers("/swagger-ui/**", "/v3/api-docs/**", "/*/*.yml").permitAll()
                        .requestMatchers(getPublicEndpoints()).permitAll()
                        .anyRequest()
                        .authenticated()
                )
                .exceptionHandling((exception) -> exception
                        .authenticationEntryPoint(new ViravaAuthErrorEntryPoint(objectMapper))
                )
                .addFilterAfter(authoritiesFilter, BasicAuthenticationFilter.class)
                .sessionManagement((session) -> session
                        .sessionCreationPolicy(SessionCreationPolicy.STATELESS)
                )
                .build();
    }

    private EndpointRequest.EndpointRequestMatcher getPublicEndpoints() {
        return EndpointRequest.to(
                HealthEndpoint.class,
                PrometheusScrapeEndpoint.class,
                MetricsEndpoint.class,
                InfoEndpoint.class
        );
    }
}

