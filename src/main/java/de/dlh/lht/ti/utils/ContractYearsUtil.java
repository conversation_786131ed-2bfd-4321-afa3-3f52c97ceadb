package de.dlh.lht.ti.utils;

import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;

public class ContractYearsUtil {

    private ContractYearsUtil() {
        throw new AssertionError("Cannot create instances of this class");
    }

    public static List<String> extractYearsFromContract(ZonedDateTime contractStart, ZonedDateTime contractEnd, boolean excludeBaseYear) {
        var contractYears = new ArrayList<String>();
        int startYear = contractStart.getYear() + (excludeBaseYear ? 1 : 0);
        for (int year = startYear; year <= contractEnd.getYear(); year++) {
            contractYears.add(String.valueOf(year));
        }

        return contractYears;
    }
}
