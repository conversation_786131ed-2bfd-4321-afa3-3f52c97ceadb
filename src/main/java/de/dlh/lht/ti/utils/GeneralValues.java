package de.dlh.lht.ti.utils;

import de.dlh.lht.ti.dto.HandlingChargeValuesDto;
import de.dlh.lht.ti.dto.HandlingChargeValuesSetsDto;
import de.dlh.lht.ti.model.HandlingChargesPart;
import de.dlh.lht.ti.model.PartType;
import de.dlh.lht.ti.model.Subcontract;
import de.dlh.lht.ti.model.SubcontractValue;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

public class GeneralValues {

    private GeneralValues() {
        throw new AssertionError("Cannot create instances of this class");
    }

    public static HandlingChargeValuesDto getHandlingChargesGeneralValues(HandlingChargeValuesSetsDto handlingChargeValuesSetsDto) {
        var baseCase = getValueIfAllEqualElseNull(handlingChargeValuesSetsDto.getBaseCase());
        var z1 = getValueIfAllEqualElseNull(handlingChargeValuesSetsDto.getZ1());
        var z2 = getValueIfAllEqualElseNull(handlingChargeValuesSetsDto.getZ2());
        var pma = getValueIfAllEqualElseNull(handlingChargeValuesSetsDto.getPma());
        var csm = getValueIfAllEqualElseNull(handlingChargeValuesSetsDto.getCsm());
        var oneItemCap = getValueIfAllEqualElseNull(handlingChargeValuesSetsDto.getOneItemCap());
        var lineItemCap = getValueIfAllEqualElseNull(handlingChargeValuesSetsDto.getLineItemCap());

        return new HandlingChargeValuesDto(baseCase, z1, z2, pma, csm, oneItemCap, lineItemCap);
    }

    public static SubcontractValue getSubcontractPricingGlobalValues(List<Subcontract> subcontracts) {
        var margin = getValueIfAllEqualElseNull(subcontracts.stream().map(Subcontract::getMargin).collect(Collectors.toSet()));
        var cap = getValueIfAllEqualElseNull(subcontracts.stream().map(Subcontract::getCap).collect(Collectors.toSet()));

        return new SubcontractValue()
                .margin(margin)
                .cap(cap);
    }

    private static <T> T getValueIfAllEqualElseNull(Set<T> values) {
        if (values == null) {
            return null;
        }

        if (values.size() == 1 && !values.contains(null)) {
            try {
                return values.stream().findFirst().orElse(null);
            } catch (NullPointerException e) {
                return null;
            }
        } else {
            return null;
        }
    }

    public static void collectHandlingChargesGeneralValues(
            HandlingChargesPart handlingChargesPart,
            HandlingChargeValuesSetsDto handlingChargeValuesSetsDto) {
        var handlingChargeValuesSet = new HashSet<Float>();
        var z1 = handlingChargesPart.getZ1();
        handlingChargeValuesSetsDto.getZ1().add(z1);
        handlingChargeValuesSet.add(z1);
        if (canApplyZ2AndPma(handlingChargesPart.getType())) {
            var z2 = handlingChargesPart.getZ2();
            handlingChargeValuesSetsDto.getZ2().add(z2);
            handlingChargeValuesSet.add(z2);
            var pma = handlingChargesPart.getPma();
            handlingChargeValuesSetsDto.getPma().add(pma);
            handlingChargeValuesSet.add(pma);
        }
        if (handlingChargesPart.getType().equals(PartType.A_PART)) {
            var csm = handlingChargesPart.getCsm();
            handlingChargeValuesSetsDto.getCsm().add(csm);
            handlingChargeValuesSet.add(csm);
        }

        if (handlingChargesPart.getQuantity() == 1) {
            handlingChargeValuesSetsDto.getOneItemCap().add(handlingChargesPart.getOneItemCap());
        } else if (handlingChargesPart.getQuantity() > 1) {
            handlingChargeValuesSetsDto.getLineItemCap().add(handlingChargesPart.getLineItemCap());
        }

        var baseCase = findBaseCase(handlingChargeValuesSet);
        handlingChargeValuesSetsDto.getBaseCase().add(baseCase);
    }

    private static Float findBaseCase(Set<Float> valueSet) {
        if (valueSet.size() == 1 && !valueSet.contains(null)) {
            return valueSet.stream().findFirst().orElse(null);
        } else {
            return null;
        }
    }

    private static boolean canApplyZ2AndPma(PartType partType) {
        return List.of(PartType.A_PART, PartType.KIT, PartType.COMPONENT, PartType.PARTS_PACKAGE).contains(partType);
    }
}
