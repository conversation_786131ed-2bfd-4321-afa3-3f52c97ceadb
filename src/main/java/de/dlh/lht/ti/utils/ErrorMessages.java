package de.dlh.lht.ti.utils;

public class ErrorMessages {

    // region Messaging
    public static final String PARSING_QUOTATION_UPLOAD_EVENT_ERROR_MESSAGE = "Error while parsing QuotationUploadEvent";
    public static final String PARSING_QUOTATION_FETCHED_SUCCESS_EVENT_ERROR_MESSAGE =
            "Error while parsing QuotationFetchedErrorEvent for quotation with quotationId: [%d]";
    public static final String PARSING_QUOTATION_FETCHED_ERROR_EVENT_ERROR_MESSAGE =
            "Error while parsing QuotationFetchedErrorEvent for quotation with quotationId: [%d]";
    public static final String PARSING_QUOTATION_RESOURCE_TO_QUOTATION_RAW_ERROR_MESSAGE =
            "Error while parsing quotation resource to QuotationRaw for quotationId: [%d]";
    // endregion

    // region Entity Not Found
    public static final String ENGINE_ENTITY_WITH_NAME_NOT_FOUND_ERROR_MESSAGE = "Engine with name: [%s] is not found";
    public static final String ENGINE_ENTITY_RELATED_TO_QUOTATION_NOT_FOUND_ERROR_MESSAGE =
            "Engine related to quotation with id: [%s] is not found";
    public static final String PART_ENTITY_WITH_NAME_AND_TYPE_NOT_FOUND_ERROR_MESSAGE =
            "Part with name: [%s] and type: [%s] is not found";
    public static final String PROJECT_ENTITY_WITH_OFFER_NUMBER_NOT_FOUND_ERROR_MESSAGE = "Project with offer number: [%s] is not found";
    public static final String OFFER_NUMBER_VALIDATION_ERROR_MESSAGE = "Invalid offer number: %s";
    public static final String QUOTATION_ENTITY_WITH_ID_NOT_FOUND_ERROR_MESSAGE = "Quotation with id: [%d] is not found";
    public static final String LABOUR_RATE_ENTITY_NOT_FOUND_ERROR_MESSAGE = "Labour rate entity  with quotation id: [%d] is not found";
    public static final String EPAR_PRICE_ENTITY_NOT_FOUND_ERROR_MESSAGE =
            "Epar price entity for engine id: [%d] and engine version [%s] is not found";
    public static final String USER_ENTITY_WITH_ID_NOT_FOUND_ERROR_MESSAGE = "User with id: [%d] is not found";
    public static final String UNUMBER_BY_QUOTATION_ID_NOT_FOUND_ERROR_MESSAGE =
            "UNumber associated with quotation with id: [%d] is not found";
    public static final String WORKSCOPE_ENTITY_WITH_NAME_NOT_FOUND_ERROR_MESSAGE =
            "Workscopes with name: [%s] is not found";
    public static final String NAVIGATION_ITEM_ENTITY_WITH_QUOTATION_ID_AND_PROGRESS_STEP_NOT_FOUND_ERROR_MESSAGE =
            "Navigation item with id: [%d] and progress step: [%s] is not found";
    public static final String CLUSTER_ENTITY_WITH_NAME_NOT_FOUND_ERROR_MESSAGE = "Cluster with name: [%s] is not found";
    public static final String TESTRUN_ITEM_WITH_QUOTATION_ID_NOT_FOUND_ERROR_MESSAGE =
            "Testrun item with quotation id: [%d] not found";
    public static final String TESTRUN_ITEM_WITH_QUOTATION_ID_YEAR_AND_WORKSCOPE_ID_NOT_FOUND_ERROR_MESSAGE =
            "Testrun item with quotation id: [%d], year: [%s] and workscope id: [%d] not found";
    public static final String ESCALATION_ENTITY_NOT_FOUND_ERROR_MESSAGE =
            "Escalation entity with id: [%d] and quotationId: [%s] is not found";
    // endregion

    // region Quotations
    public static final String QUOTATION_WITH_ID_ALREADY_BEGUN_ERROR_MESSAGE = "Quotation with id: [%d] already had begun";
    public static final String QUOTATION_STATUS_WITH_INDEX_DOES_NOT_EXISTS_ERROR_MESSAGE =
            "Quotation status with index: [%d] does not exists";
    public static final String QUOTATION_EXCHANGE_RATE_NOT_FOUND_ERROR_MESSAGE = "Quotation's with id: [%d], exchange rate not found";
    //endregion

    // region Labour Pricing
    public static final String LABOUR_RATE_ROUTINE_VALIDATION_ERROR_MESSAGE = "Routine labour rate cannot be negative value";
    public static final String LABOUR_RATE_NON_ROUTINE_VALIDATION_ERROR_MESSAGE = "Non-routine labour rate cannot be negative value";
    public static final String LABOUR_RATE_EPAR_DISCOUNT_VALIDATION_ERROR_MESSAGE = "Epar discount cannot be null";
    public static final String LABOUR_RFP_ENGINE_VALIDATION_ERROR_MESSAGE = "Routine labour rfp engine price cannot be negative value";
    public static final String RFP_MODULE_VALIDATION_ERROR_MESSAGE = "RFP Module price invalid";
    //endregion

    // region Subcontractor Pricing
    public static final String SUBCONTRACT_PRICING_VALIDATION_ERROR_MESSAGE = "Subcontract Pricing margin or cap invalid";
    // endregion

    // region Escalation Pricing
    public static final String ESCALATION_PRICING_SUBCONTRACT_MISSING_DEFAULT_VALUE_ERROR_MESSAGE =
            "No subcontract default price value for year: ";
    public static final String ESCALATION_PRICING_VALIDATION_ERROR_MESSAGE = "Escalation pricing has invalid input";
    public static final String ESCALATION_PRICING_RFP_CONTRACT_NOT_SELECTED_ERROR_MESSAGE =
            "RFP Labour pricing input is not allowed. RFP contract not selected for quotation with id: [%d]";
    // endregion Escalation Pricing

    // region Authorization
    public static final String AUTH_TOKEN_NOT_PROVIDED_ERROR_MESSAGE = "Authentication token has not been provided";
    public static final String CHANGE_OWNER_NOT_ALLOWED_ERROR_MESSAGE =
            "Change owner not allowed. Current user does not match the project owner uNumber: [%s].";
    public static final String BEGIN_QUOTATION_NOT_ALLOWED_ERROR_MESSAGE =
            "Begin quotation not allowed. Current user does not match the project owner uNumber: [%s].";
    public static final String SAVE_HANDLING_CHARGES_NOT_ALLOWED_ERROR_MESSAGE =
            "Save handling charges not allowed. Current user does not match the project owner uNumber: [%s].";
    public static final String SAVE_Z2_RATING_NOT_ALLOWED_ERROR_MESSAGE =
            "Save Z2 ratings not allowed. Current user does not match the project owner uNumber: [%s].";
    public static final String SAVE_LABOUR_RATE_NOT_ALLOWED_ERROR_MESSAGE =
            "Save labour rate not allowed. Current user does not match the project owner uNumber: [%s].";
    public static final String SAVE_LABOUR_RFP_ENGINE_NOT_ALLOWED_ERROR_MESSAGE =
            "Save labour rfp engine not allowed. Current user does not match the project owner uNumber: [%s].";
    public static final String SAVE_RFP_MODULE_NOT_ALLOWED_ERROR_MESSAGE =
            "Save RFP Module not allowed. Current user does not match the project owner uNumber: [%s].";
    public static final String SAVE_SUBCONTRACT_PRICING_NOT_ALLOWED_ERROR_MESSAGE =
            "Save Subcontract Pricing not allowed. Current user does not match the project owner uNumber: [%s].";
    public static final String RFP_CONTRACT_IS_NOT_SELECTED_ERROR_MESSAGE =
            "Rfp engine and module methods not allowed. Rfp contract type is not selected for quotation with id: [%s].";
    public static final String SAVE_ESCALATION_PRICING_NOT_ALLOWED_ERROR_MESSAGE =
            "Save Escalation Pricing not allowed. Current user does not match the project owner uNumber: [%s].";
    // endregion

    // region Validation
    public static final String NOT_SUPPORTED_CURRENCY_ERROR_MESSAGE =
            "Currency [%s] is not yet supported.";
    // endregion

    private ErrorMessages() {
        throw new AssertionError("Cannot create instances of this class");
    }
}
