package de.dlh.lht.ti.utils;

import de.dlh.lht.ti.enums.PartType;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class SquashDtoList {

    private SquashDtoList() {
        throw new AssertionError("Cannot create instances of this class");
    }

    public static <T> List<T> squashDtoListByPartType(Map<PartType, List<T>> groupedDtosByPartType) {
        var squashedDtoList = new ArrayList<T>();
        groupedDtosByPartType.forEach((partType, groupedDtoList) -> {
            switch (partType) {
                case A_PART, LLP, CASE_AND_FRAME -> {
                    if (groupedDtoList != null) {
                        squashedDtoList.addAll(groupedDtoList);
                    }
                }
                default -> {
                    if (groupedDtoList != null) {
                        squashedDtoList.add(groupedDtoList.get(0));
                    }
                }
            }
        });
        return squashedDtoList;
    }

    public static <T> List<T> squashDtoListByYearAndPartType(Map<String, Map<PartType, List<T>>> groupedDtosByYearAndPartType) {
        var squashedDtoList = new ArrayList<T>();
        groupedDtosByYearAndPartType.forEach((year, groupedDtosByPartType) ->
                squashedDtoList.addAll(squashDtoListByPartType(groupedDtosByPartType))
        );
        return squashedDtoList;
    }
}
