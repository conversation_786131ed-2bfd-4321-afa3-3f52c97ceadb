package de.dlh.lht.ti.utils;

import java.math.BigDecimal;

public class EscalationConstants {
    public static final BigDecimal DEFAULT_PERCENTAGE = new BigDecimal("6.00");
    public static final int DEFAULT_YEAR = 2028;
    public static final BigDecimal SUBCONTRACT_ESCALATION_FOR_2022 = new BigDecimal("5.80");
    public static final BigDecimal SUBCONTRACT_ESCALATION_FOR_2023 = new BigDecimal("11.00");
    public static final BigDecimal SUBCONTRACT_ESCALATION_FOR_2024 = new BigDecimal("11.00");
    public static final BigDecimal SUBCONTRACT_ESCALATION_FOR_2025 = new BigDecimal("6.00");
    public static final BigDecimal SUBCONTRACT_ESCALATION_FOR_2026 = new BigDecimal("6.00");
    public static final BigDecimal SUBCONTRACT_ESCALATION_FOR_2027 = new BigDecimal("6.00");
    public static final BigDecimal LABOUR_PRICE_ESCALATION_DEFAULT = BigDecimal.valueOf(0.0);
    public static final BigDecimal EPAR_PRICE_ESCALATION_DEFAULT = BigDecimal.valueOf(5.0);
    public static final BigDecimal RFP_LABOUR_PRICE_ESCALATION_DEFAULT = BigDecimal.valueOf(0.0);

    private EscalationConstants() {
        throw new AssertionError("Cannot create instances of this class");
    }

}
