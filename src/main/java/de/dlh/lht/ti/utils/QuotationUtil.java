package de.dlh.lht.ti.utils;

import org.springframework.data.domain.Sort;

public class QuotationUtil {
    public static final Sort DEFAULT_QUOTATION_LIST_SORT = Sort.by(
            Sort.Order.asc("quotation.status"),
            Sort.Order.desc("quotation.statusLastUpdated"),
            Sort.Order.asc("id")
    );

    private QuotationUtil() {
        throw new AssertionError("Cannot create instances of this class");
    }
}