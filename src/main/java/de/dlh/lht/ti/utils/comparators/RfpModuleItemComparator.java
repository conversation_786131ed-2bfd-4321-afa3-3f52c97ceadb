package de.dlh.lht.ti.utils.comparators;

import de.dlh.lht.ti.model.RfpItem;
import java.util.Comparator;
import java.util.regex.Pattern;

public class RfpModuleItemComparator implements Comparator<RfpItem> {

    // Regex to find first letter, the following number, and the rest of the string
    private static final String REGEX = "([a-zA-Z])(\\d*)(.*)";

    @Override
    public int compare(RfpItem rfpItem1, RfpItem rfpItem2) {
        var moduleCode1 = extractModuleCode(rfpItem1.getName());
        var moduleCode2 = extractModuleCode(rfpItem2.getName());
        return moduleCode1.compareTo(moduleCode2);
    }

    /**
     * Extracts a sort key from the given module string.
     *
     * @param moduleName the module string to extract the sort key from.
     * @return a ModuleCode object containing the parts of the module code that we care about for sorting.
     */
    private ModuleCode extractModuleCode(String moduleName) {
        // Splitting the module string on " - ", then using a regular expression
        // to extract the first letter, the following number, and the rest of the string
        var matcher = Pattern.compile(REGEX).matcher(moduleName.split(" - ")[0]);
        // Advance to the first match
        matcher.find();
        // Extract the first letter
        var letterPart = matcher.group(1);
        // Extract the number; if no number is found, set it to the maximum possible integer
        var numberPart = matcher.group(2).isEmpty() ? Integer.MAX_VALUE : Integer.parseInt(matcher.group(2));
        // Extract the rest of the string
        var restPart = matcher.group(3);

        // Return a new ModuleCode object, which will be used as the sort key
        return new ModuleCode(letterPart, numberPart, restPart);
    }

    /**
     * A simple class to hold the parts of a module code that we care about for sorting.
     * Implements the Comparable interface to define how these objects should be compared for sorting.
     */
    private static class ModuleCode implements Comparable<ModuleCode> {
        String letterPart;
        int numberPart;
        String restPart;

        /**
         * Constructs a new ModuleCode object.
         *
         * @param letterPart the initial letter of the module code.
         * @param numberPart the number following the initial letter in the module code.
         * @param restPart   the rest of the module code after the initial letter and number.
         */
        ModuleCode(String letterPart, int numberPart, String restPart) {
            this.letterPart = letterPart;
            this.numberPart = numberPart;
            this.restPart = restPart;
        }

        /**
         * Compares this ModuleCode object to another ModuleCode object.
         * The comparison is done first on the initial letters, then on the numbers, and finally on the rest of the strings.
         *
         * @param other the ModuleCode object to compare this object to.
         * @return a negative integer, zero, or a positive integer as this object is less than, equal to, or greater than the specified object.
         */
        @Override
        public int compareTo(ModuleCode other) {
            int letterCompare = this.letterPart.compareTo(other.letterPart);
            if (letterCompare != 0) {
                return letterCompare;
            }

            int numberCompare = Integer.compare(this.numberPart, other.numberPart);
            if (numberCompare != 0) {
                return numberCompare;
            }

            return this.restPart.compareTo(other.restPart);
        }
    }
}