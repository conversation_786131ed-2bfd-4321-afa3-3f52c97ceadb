package de.dlh.lht.ti.utils.comparators;

import de.dlh.lht.ti.model.HandlingChargesPart;
import java.util.Comparator;

public class HandlingChargesPartComparator implements Comparator<HandlingChargesPart> {

    @Override
    public int compare(HandlingChargesPart handlingChargesPart1, HandlingChargesPart handlingChargesPart2) {
        return Long.compare(handlingChargesPart1.getOrder(), handlingChargesPart2.getOrder());
    }
}
