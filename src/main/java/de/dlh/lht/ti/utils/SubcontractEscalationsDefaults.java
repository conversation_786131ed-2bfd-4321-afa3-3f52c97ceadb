package de.dlh.lht.ti.utils;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static de.dlh.lht.ti.utils.EscalationConstants.*;

public class SubcontractEscalationsDefaults {

    private static final Map<String, BigDecimal> percentageByYear;

    static {
        percentageByYear = new HashMap<>();
        percentageByYear.put("2022", SUBCONTRACT_ESCALATION_FOR_2022);
        percentageByYear.put("2023", SUBCONTRACT_ESCALATION_FOR_2023);
        percentageByYear.put("2024", SUBCONTRACT_ESCALATION_FOR_2024);
        percentageByYear.put("2025", SUBCONTRACT_ESCALATION_FOR_2025);
        percentageByYear.put("2026", SUBCONTRACT_ESCALATION_FOR_2026);
        percentageByYear.put("2027", SUBCONTRACT_ESCALATION_FOR_2027);
        percentageByYear.put("2028", DEFAULT_PERCENTAGE);
    }

    public Optional<BigDecimal> getPercentageByYear(String year) {
        if (Integer.parseInt(year) > DEFAULT_YEAR) {
            return Optional.of(DEFAULT_PERCENTAGE);
        }
        return Optional.ofNullable(percentageByYear.get(year));
    }
}
