package de.dlh.lht.ti.utils;

public class LoggingMessages {

    // region Begin Quotation
    public static final String BEGIN_QUOTATION_RESPONSE = "Successfully finished Begin Quotation with Response: {} ";
    public static final String BEGIN_QUOTATION_REQUEST = "Begin Quotation Request: {} ";
    // endregion Begin Quotation

    // region Get Handling Charges
    public static final String GET_HANDLING_CHARGES_RESPONSE = "GET Handling Charges Response. ID: {}";
    public static final String GET_HANDLING_CHARGES_REQUEST = "GET Handling Charges Request. ID: {}";
    // endregion Get Handling Charges

    // region Get Handling Charges
    public static final String SAVE_HANDLING_CHARGES_RESPONSE = "Save Handling Charges Response: {}";
    public static final String SAVE_HANDLING_CHARGES_REQUEST = "Save Handling Charges Request: {}";
    // endregion Get Handling Charges

    //region z2 ratings
    public static final String GET_Z2_RATINGS_REQUEST = "GET Z2 Ratings Request. ID: {}";
    public static final String GET_Z2_RATINGS_RESPONSE = "GET Z2 Ratings Response. ID: {} Response {}";
    public static final String SAVE_Z2_RATINGS_REQUEST = "SAVE Z2 Ratings Request. ID: {} RequestBody: {}";
    public static final String SAVE_Z2_RATINGS_RESPONSE = "SAVE Z2 Ratings Response. ID: {} Response {}";
    public static final String RETRIEVE_Z2_RATINGS = "Retrieve z2 ratings data {} millis";
    //endregion z2 ratings

    //region labour rates
    public static final String GET_LABOUR_RATES_REQUEST = "GET Labour Rates Request. ID: {}";
    public static final String GET_LABOUR_RATES_RESPONSE = "GET Labour Rates Response. ID: {} Response {}";
    public static final String SAVE_LABOUR_RATES_REQUEST = "SAVE Labour Rates Request. ID: {} RequestBody: {}";
    public static final String SAVE_LABOUR_RATES_RESPONSE = "SAVE Labour Rates Response. ID: {} Response {}";
    // endregion labour rates

    // region rfp engine
    public static final String GET_LABOUR_RFP_ENGINE_REQUEST = "GET Labour RFP Engine Request. ID: {}";
    public static final String GET_LABOUR_RFP_ENGINE_RESPONSE = "GET Labour RFP Engine Response. ID: {} Response {}";
    public static final String SAVE_LABOUR_RFP_ENGINE_REQUEST = "SAVE Labour RFP Engine Request. ID: {} RequestBody: {}";
    public static final String SAVE_LABOUR_RFP_ENGINE_RESPONSE = "SAVE Labour RFP Engine Response. ID: {} Response {}";
    // endregion rfp engine

    // region rfp module
    public static final String GET_RFP_MODULE_REQUEST = "GET RFP Module Request. ID: {}";
    public static final String GET_RFP_MODULE_RESPONSE = "GET RFP Module Response. ID: {} Response {}";
    public static final String SAVE_RFP_MODULE_REQUEST = "SAVE RFP Module Request. ID: {} RequestBody: {}";
    public static final String SAVE_RFP_MODULE_RESPONSE = "SAVE RFP Module Response. ID: {} Response {}";
    // endregion rfp module

    // region Subcontract Pricing
    public static final String GET_SUBCONTRACT_PRICING_REQUEST = "GET Subcontract Pricing Request. ID: {}";
    public static final String GET_SUBCONTRACT_PRICING_RESPONSE = "GET Subcontract Pricing Response. ID: {} Response {}";
    public static final String SAVE_SUBCONTRACT_PRICING_REQUEST = "SAVE Subcontract Pricing Request. ID: {} RequestBody: {}";
    public static final String SAVE_SUBCONTRACT_PRICING_RESPONSE = "SAVE Subcontract Pricing Response. ID: {} Response {}";
    // endregion Subcontract Pricing

    // region Escalation Pricing
    public static final String GET_ESCALATION_PRICING_REQUEST = "GET Escalation Pricing Request. ID: {}";
    public static final String GET_ESCALATION_PRICING_RESPONSE = "GET Escalation Pricing Response. ID: {} Response {}";
    public static final String SAVE_ESCALATION_PRICING_REQUEST = "SAVE Escalation Pricing Request. ID: {} RequestBody: {}";
    public static final String SAVE_ESCALATION_PRICING_RESPONSE = "SAVE Escalation Pricing Response. ID: {} Response {}";
    // endregion Escalation Pricing

    // region Get Quotation Details
    public static final String GET_QUOTATION_DETAILS_RESPONSE = "GET Quotation Details Response. ID: {}";
    public static final String GET_QUOTATION_DETAILS_REQUEST = "GET Quotation Details Request. ID: {}";
    // endregion Get Quotation Details

    // region Get Quotations
    public static final String GET_QUOTATIONS_RESPONSE = "GET Quotations Response: {}";
    public static final String GET_QUOTATIONS_REQUEST = "GET Quotations Request: {}";
    // endregion Get Quotations

    // region Get Quotation Filters
    public static final String GET_QUOTATION_FILTERS_REQUEST = "GET Quotation Filters Request.";
    public static final String GET_QUOTATION_FILTERS_RESPONSE = "GET Quotation Filters Response: {}";
    // endregion Get Quotation Filters

    // region Change Owner
    public static final String GET_USERS_FOR_CHANGE_OWNER_REQUEST = "Get all users that quotation can be assign to Request.";
    public static final String GET_USERS_FOR_CHANGE_OWNER_RESPONSE = "Get all users that quotation can be assign to Response: {}";
    public static final String CHANGE_OWNER_REQUEST = "Change Owner Request for offer number: {}. Request: {}";
    public static final String CHANGE_OWNER_RESPONSE = "Successfully changed owner for offer number: {}";
    // endregion Change Owner

    // region Authorization
    public static final String GET_USER_DETAILS_REQUEST = "Get User Details Request for user with username: {}";
    public static final String GET_USER_DETAILS_RESPONSE = "Get User Details Response for user with username: {}. Response: {}";
    // endregion Authorization

    // region Get Workscope Summary
    public static final String GET_WORKSCOPE_SUMMARY_REQUEST = "Get Workscope Summary Request for quotation ID: {}";
    public static final String GET_WORKSCOPE_SUMMARY_RESPONSE = "Get Workscope Summary Response for ID: {}. Response: {}";
    // endregion Get Workscope Summary

    private LoggingMessages() {
        throw new AssertionError("Cannot create instances of this class");
    }
}
