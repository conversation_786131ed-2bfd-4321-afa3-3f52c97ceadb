package de.dlh.lht.ti.utils;

import de.dlh.lht.ti.dto.CalculationResultDto;
import de.dlh.lht.ti.dto.MetadataDto;
import de.dlh.lht.ti.dto.ProductionCostDto;
import de.dlh.lht.ti.dto.RfpCostDto;
import de.dlh.lht.ti.dto.TaskMetadataDto;
import de.dlh.lht.ti.dto.WorkscopeDto;
import de.dlh.lht.ti.enums.Currency;
import de.dlh.lht.ti.exception.CurrencyNotSupportedException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


import static de.dlh.lht.ti.utils.Constants.HUNDRED;
import static de.dlh.lht.ti.utils.Constants.MATH_CONTEXT;
import static de.dlh.lht.ti.utils.Constants.PRECISION;
import static de.dlh.lht.ti.utils.Constants.ZERO;
import static de.dlh.lht.ti.utils.ErrorMessages.NOT_SUPPORTED_CURRENCY_ERROR_MESSAGE;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.mapping;
import static java.util.stream.Collectors.toList;

public class Calculations {

    private Calculations() {
        throw new AssertionError("Cannot create instances of this class");
    }

    /**
     * Calculates the cost based on metadata and exchange rate.
     *
     * @param metadataDto  Metadata DTO object.
     * @param exchangeRate Exchange rate for cost calculation.
     * @return The cost calculated in terms of BigDecimal.
     */
    public static BigDecimal calculateCost(MetadataDto metadataDto, BigDecimal exchangeRate) {
        var cost = calculateCost(metadataDto);
        if (metadataDto.getCurrency().equals(Currency.EUR)) {
            cost = cost.multiply(exchangeRate);
        }
        return cost;
    }

    /**
     * Calculates the sum of task costs.
     *
     * @param taskMetadataDtoList List of TaskMetadata DTO objects.
     * @param exchangeRate        Exchange rate for cost calculation.
     * @return The cost sum calculated in terms of RfpCostDto.
     */
    public static RfpCostDto calculateTaskCostSum(
            List<? extends TaskMetadataDto> taskMetadataDtoList,
            BigDecimal exchangeRate) {
        var costs = new ArrayList<BigDecimal>();
        var ciHoursCosts = new ArrayList<BigDecimal>();
        taskMetadataDtoList.forEach(taskMetadataDto -> {
            var taskCoast = calculateCost(taskMetadataDto, exchangeRate);
            if (taskMetadataDto.getIsCleaningAndInspection()) {
                ciHoursCosts.add(taskCoast);
            } else {
                costs.add(taskCoast);
            }
        });
        var cost = costs.stream().reduce(ZERO, BigDecimal::add);
        var ciHoursCost = ciHoursCosts.stream().reduce(ZERO, BigDecimal::add);
        return new RfpCostDto(cost, ciHoursCost);
    }

    /**
     * Calculates production cost based on a list of ProductionCostDto.
     *
     * @param productionCostDtos List of ProductionCostDto.
     * @return A map of workscope DTO and the associated calculation result.
     */
    public static Map<WorkscopeDto, CalculationResultDto> calculateProductionCost(List<ProductionCostDto> productionCostDtos) {
        var calculationResultsByWorkscopeDto = new HashMap<WorkscopeDto, CalculationResultDto>();

        productionCostDtos.removeIf(dto -> dto.getMetadata().getCurrency() == null
                || dto.getMetadata().getValue() == null
                || dto.getMetadata().getWeightedQuantity() == null
                || dto.getMetadata().getQuantity() == null);

        var metadataByCurrencyByWorkscopeDto = productionCostDtos
                .stream()
                .collect(groupingBy(
                        ProductionCostDto::getWorkscope,
                        groupingBy(
                                dto -> dto.getMetadata().getCurrency(),
                                mapping(ProductionCostDto::getMetadata, toList()))));

        metadataByCurrencyByWorkscopeDto.forEach((workscope, map) -> {
            var result = new CalculationResultDto();
            map.forEach((currency, list) -> {
                var cost = list.stream()
                        .map(Calculations::calculateCost)
                        .reduce(ZERO, BigDecimal::add)
                        .stripTrailingZeros()
                        .setScale(PRECISION, RoundingMode.DOWN)
                        .stripTrailingZeros();

                switch (currency) {
                    case USD -> result.setValueUsd(cost);
                    case EUR -> result.setValueEur(cost);
                    default -> throw new CurrencyNotSupportedException(
                            String.format(NOT_SUPPORTED_CURRENCY_ERROR_MESSAGE, currency.name()));
                }
            });
            calculationResultsByWorkscopeDto.put(workscope, result);
        });

        return calculationResultsByWorkscopeDto;
    }

    /**
     * Calculates the sum of USD value and EUR value converted to USD.
     *
     * @param usdValue        Value in USD.
     * @param eurValue        Value in EUR.
     * @param usdExchangeRate Exchange rate for USD.
     * @return The sum in terms of BigDecimal.
     */
    public static BigDecimal calculateUsdPlusEur(BigDecimal usdValue, BigDecimal eurValue, BigDecimal usdExchangeRate) {
        return usdValue.add(eurValue.multiply(usdExchangeRate));
    }

    /**
     * Calculates the cost based on metadata.
     *
     * @param metadataDto Metadata DTO object.
     * @return The cost calculated in terms of BigDecimal.
     */
    private static BigDecimal calculateCost(MetadataDto metadataDto) {
        return metadataDto.getValue()
                .multiply(BigDecimal.valueOf(metadataDto.getQuantity()), MATH_CONTEXT)
                .multiply(metadataDto.getWeightedQuantity().divide(HUNDRED, MATH_CONTEXT), MATH_CONTEXT);
    }

    /**
     * Calculates the revenue for cleaning and inspection tasks.
     *
     * @param taskMetadataDto            TaskMetadata DTO object.
     * @param cleaningAndInspectionPrice Price for cleaning and inspection.
     * @return The revenue calculated in terms of BigDecimal.
     */
    private static BigDecimal calculateCiTaskRevenue(TaskMetadataDto taskMetadataDto, BigDecimal cleaningAndInspectionPrice) {
        return taskMetadataDto.getManHours()
                .multiply(BigDecimal.valueOf(taskMetadataDto.getQuantity()), MATH_CONTEXT)
                .multiply(taskMetadataDto.getWeightedQuantity().divide(HUNDRED, MATH_CONTEXT), MATH_CONTEXT)
                .multiply(cleaningAndInspectionPrice, MATH_CONTEXT);
    }
}
