package de.dlh.lht.ti.utils.comparators;

import de.dlh.lht.ti.model.HandlingChargesCluster;
import java.util.Comparator;

public class HandlingChargesClusterComparator implements Comparator<HandlingChargesCluster> {

    @Override
    public int compare(HandlingChargesCluster handlingChargesCluster1, HandlingChargesCluster handlingChargesCluster2) {
        return Long.compare(handlingChargesCluster1.getOrder(), handlingChargesCluster2.getOrder());
    }
}
