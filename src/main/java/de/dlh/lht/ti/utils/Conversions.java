package de.dlh.lht.ti.utils;

import java.sql.Timestamp;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;

public class Conversions {

    private Conversions() {
        throw new AssertionError("Cannot create instances of this class");
    }

    public static Long zonedDateTimeToLong(ZonedDateTime zonedDateTime) {
        return zonedDateTime.toInstant().toEpochMilli();
    }
    public static ZonedDateTime longToZonedDateTime(Long timestamp) {
        return ZonedDateTime.ofInstant(Instant.ofEpochMilli(timestamp), ZoneId.systemDefault());
    }

    public static Long timestampToLong(Timestamp timestamp) {
        return timestamp.toInstant().toEpochMilli();
    }
}
