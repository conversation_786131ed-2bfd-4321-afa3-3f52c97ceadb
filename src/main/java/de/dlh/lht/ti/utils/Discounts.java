package de.dlh.lht.ti.utils;

import de.dlh.lht.ti.entity.MaterialDiscountExpendableEntity;
import de.dlh.lht.ti.enums.ExpandableDiscountType;
import java.math.BigDecimal;
import java.util.Map;

public class Discounts {

    private Discounts() {
        throw new AssertionError("Cannot create instances of this class");
    }

    public static final BigDecimal VOLUME_BASED_DISCOUNT_BASELINE = BigDecimal.valueOf(0.65);
    public static final BigDecimal VOLUME_BASED_DISCOUNT_SCALING_FACTOR = BigDecimal.valueOf(0.35);

    public static final Map<ExpandableDiscountType, BigDecimal> EXPANDABLE_DISCOUNT_MAP = Map.of(
            ExpandableDiscountType.LEVEL_ZERO, BigDecimal.ZERO,
            ExpandableDiscountType.LEVEL_ONE, BigDecimal.valueOf(7_500),
            ExpandableDiscountType.LEVEL_TWO, BigDecimal.valueOf(15_000)
    );

    public static BigDecimal getExpandableDiscount(
            ExpandableDiscountType expandableDiscountType,
            MaterialDiscountExpendableEntity materialDiscountExpendableEntity
    ) {
        return switch (expandableDiscountType) {
            case LEVEL_ONE -> materialDiscountExpendableEntity.getLevelOne();
            case LEVEL_TWO -> materialDiscountExpendableEntity.getLevelTwo();
            default -> materialDiscountExpendableEntity.getLevelZero();
        };
    }
}