package de.dlh.lht.ti.utils;

import de.dlh.lht.ti.enums.TaskType;
import de.dlh.lht.ti.exception.ValidationException;
import de.dlh.lht.ti.model.EscalationPricingRequest;
import de.dlh.lht.ti.model.RfpRequest;
import de.dlh.lht.ti.model.SubcontractPricingRequest;


import static de.dlh.lht.ti.utils.ErrorMessages.ESCALATION_PRICING_VALIDATION_ERROR_MESSAGE;
import static de.dlh.lht.ti.utils.ErrorMessages.SUBCONTRACT_PRICING_VALIDATION_ERROR_MESSAGE;

public class Validations {

    private Validations() {
        throw new AssertionError("Cannot create instances of this class");
    }

    public static void rfpItemInputAssertion(RfpRequest rfpRequest, String errorMessage, TaskType taskType) throws ValidationException {
        if (rfpRequest == null || rfpRequest.getRfpItemInputs() == null) {
            throw new ValidationException(errorMessage);
        }

        var isInvalid = true;

        isInvalid = rfpRequest.getRfpItemInputs().stream()
                .anyMatch(item -> item != null
                        && item.getPrice() != null
                        && (item.getPrice() < 0
                        || item.getPrice().isNaN()
                        || item.getPrice().isInfinite()));

        if (taskType.equals(TaskType.RFP_ENGINE)) {
            var testrunItemsCount = rfpRequest.getRfpItemInputs().stream()
                    .filter(item -> item != null && item.getIsTestrunMaterial())
                    .count();
            isInvalid = testrunItemsCount != 1;
        }

        if (isInvalid) {
            throw new ValidationException(errorMessage);
        }
    }

    public static void subcontractInputAssertion(SubcontractPricingRequest subcontractPricingRequest)
            throws ValidationException {
        if (subcontractPricingRequest == null || subcontractPricingRequest.getSubcontractInputs() == null) {
            throw new ValidationException(SUBCONTRACT_PRICING_VALIDATION_ERROR_MESSAGE);
        }

        boolean hasInvalidPrice = subcontractPricingRequest.getSubcontractInputs().stream()
                .anyMatch(item -> item != null
                        && ((item.getMargin() != null
                        && (item.getMargin() < 0
                        || item.getMargin().isNaN()
                        || item.getMargin().isInfinite()
                        || item.getMargin() >= 100))
                        || (item.getCap() != null
                        && item.getCap() <= 0)));

        if (hasInvalidPrice) {
            throw new ValidationException(SUBCONTRACT_PRICING_VALIDATION_ERROR_MESSAGE);
        }
    }

    public static void escalationInputAssertion(EscalationPricingRequest escalationPricingRequest, Boolean isRfpContractSelected)
            throws ValidationException {
        if (escalationPricingRequest == null || escalationPricingRequest.getEscalationInputs() == null) {
            throw new ValidationException(ESCALATION_PRICING_VALIDATION_ERROR_MESSAGE);
        }

        boolean hasInvalidInput = escalationPricingRequest.getEscalationInputs().stream()
                .anyMatch(input ->
                        input == null
                                || (input.getEparPrices() != null && (input.getEparPrices() >= 100 || input.getEparPrices() < 0))
                                || (input.getLabourPrices() != null && (input.getLabourPrices() >= 100 || input.getLabourPrices() < 0))
                                || (input.getHcMaterialPrices() != null && (input.getHcMaterialPrices() >= 100 || input.getHcMaterialPrices() < 0))
                                || (input.getHcSubcontractPrices() != null && (input.getHcSubcontractPrices() >= 100 || input.getHcSubcontractPrices() < 0))
                                || (!isRfpContractSelected && input.getRfpLabour() != null && (input.getRfpLabour() >= 100 || input.getRfpLabour() < 0))
                );

        if (hasInvalidInput) {
            throw new ValidationException(ESCALATION_PRICING_VALIDATION_ERROR_MESSAGE);
        }
    }
}
