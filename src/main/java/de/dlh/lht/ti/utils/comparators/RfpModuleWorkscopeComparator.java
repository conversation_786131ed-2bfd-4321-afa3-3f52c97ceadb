package de.dlh.lht.ti.utils.comparators;

import de.dlh.lht.ti.model.RfpModuleWorkscope;
import java.util.Comparator;

public class RfpModuleWorkscopeComparator implements Comparator<RfpModuleWorkscope> {

    @Override
    public int compare(RfpModuleWorkscope rfpModuleWorkscope1, RfpModuleWorkscope rfpModuleWorkscope2) {
        return Long.compare(rfpModuleWorkscope1.getId(), rfpModuleWorkscope2.getId());
    }
}
