package de.dlh.lht.ti.utils;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;

public class Constants {

    public static final String OFFER_NUMBER_VALIDATION_REGEX = "^[0-9]{7}$";
    public static final String KITS = "Kits";
    public static final String COMPONENTS = "Components";
    public static final String PARTS_PACKAGE = "Parts Package";
    public static final String REPAIR_MATERIAL = "Repair Material";
    public static final String ROUTINE_MATERIAL = "Routine Material";
    public static final String TESTRUN_MATERIAL = "Fuel & Oil MATERIAL for the Testrun";

    public static final int PRECISION = 8;
    public static final BigDecimal ZERO = BigDecimal.ZERO.setScale(PRECISION, RoundingMode.HALF_UP);
    public static final BigDecimal HUNDRED = BigDecimal.valueOf(100).setScale(PRECISION, RoundingMode.HALF_UP);
    public static final MathContext MATH_CONTEXT = new MathContext(PRECISION);

    private Constants() {
        throw new AssertionError("Cannot create instances of this class");
    }
}
