package de.dlh.lht.ti.utils;

import java.util.Map;

public class SystemWorkscopesUtil {

    public static final Map<String, String> DEEPEST_WORKSCOPE_PER_ENGINE = Map.of(
            "LEAP-1A", "LEVEL 03",
            "LEAP-1B", "LEVEL 03",
            "V2500", "L3",
            "CFM56-5B", "L3",
            "CFM56-7B", "L3"
    );

    private SystemWorkscopesUtil() {
        throw new IllegalStateException("Utility class");
    }
}
