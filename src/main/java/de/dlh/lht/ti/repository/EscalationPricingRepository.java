package de.dlh.lht.ti.repository;

import de.dlh.lht.ti.dto.EscalationPricingDto;
import de.dlh.lht.ti.entity.EscalationPricingEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface EscalationPricingRepository extends JpaRepository<EscalationPricingEntity, Long> {
    
    List<EscalationPricingEntity> findAllByQuotationId(Long quotationId);

    @Query("""
            select new de.dlh.lht.ti.dto.EscalationPricingDto(
                ep.id,
                ep.year,
                ep.labourPrices,
                ep.eparPrices,
                ep.rfpLabour,
                ep.hcMaterialPrices,
                ep.hcSubcontractPrices,
                empd.value,
                :isRfpContractSelected)
            from EscalationPricingEntity as ep
            join QuotationEngineEntity as qe
            on qe.quotation.id = ep.quotationId
            join EscalationsMaterialPricesDefaultEntity as empd
            on qe.engine.id = empd.engineId and ep.year = empd.year
            where ep.quotationId = :quotationId
            """)
    List<EscalationPricingDto> findAllWithMaterialDefaultsByQuotationId(Long quotationId, Boolean isRfpContractSelected);
}