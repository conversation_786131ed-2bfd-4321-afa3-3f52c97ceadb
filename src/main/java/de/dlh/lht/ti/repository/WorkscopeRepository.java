package de.dlh.lht.ti.repository;

import de.dlh.lht.ti.entity.WorkscopeEntity;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface WorkscopeRepository extends JpaRepository<WorkscopeEntity, Long> {

    @Query("""
            select w.id
            from WorkscopeEntity as w
            join QuotationEngineWorkscopeEntity as qew
            on qew.workscopeId = w.id
            join QuotationEngineEntity as qe
            on qew.quotationEngineId = qe.id
            where qe.quotation.id = :quotationId and w.isSystem = true
            """)
    List<Long> findSystemWorkscopeIdsByQuotationId(Long quotationId);

    @Query("""
            select w
            from WorkscopeEntity as w
            join QuotationEngineWorkscopeEntity as qew
            on qew.workscopeId = w.id
            join QuotationEngineEntity as qe
            on qew.quotationEngineId = qe.id
            where qe.quotation.id = :quotationId and w.isSystem = false
            """)
    List<WorkscopeEntity> findWorkscopesByQuotationId(Long quotationId);

    @Query("""
            select w.id
            from WorkscopeEntity as w
            where w.name = :name
            """)
    Optional<Long> findWorkscopeIdByName(String name);
}
