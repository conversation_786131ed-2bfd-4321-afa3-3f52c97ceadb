package de.dlh.lht.ti.repository;

import de.dlh.lht.ti.entity.UserEntity;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface UserRepository extends JpaRepository<UserEntity, Long> {

    @Query("""
            select u
            from UserEntity as u
            join ProjectEntity as p
            on p.currentOwner.id = u.id
            join QuotationEntity as q
            on q.project.id = p.id
            """)
    List<UserEntity> findAllQuotationOwners();

    @Query("""
            select u.uNumber
            from UserEntity as u
            join ProjectEntity as p
            on p.currentOwner.id = u.id
            join QuotationEntity as q
            on q.project.id = p.id
            where q.id = :quotationId
            """)
    Optional<String> findCurrentOwnerUNumberByQuotationId(Long quotationId);

    Optional<UserEntity> findByuNumber(String uNumber);
}
