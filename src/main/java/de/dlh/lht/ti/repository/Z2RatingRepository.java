package de.dlh.lht.ti.repository;

import de.dlh.lht.ti.dto.Z2RatingDto;
import de.dlh.lht.ti.entity.Z2RatingEntity;
import java.util.Collection;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface Z2RatingRepository extends JpaRepository<Z2RatingEntity, Long> {

    @Query("""
            select distinct new de.dlh.lht.ti.dto.Z2RatingDto(z2r.id, c.id, c.name, ec.sortOrder, p.id, p.name, ecp.sortOrder, p.type, pm.id, z2r.year, pm.oemZ1Price, pm.oemZ1PriceCurrency, pm.z2Value, pm.quantity, z2r.z2Rating)
            from Z2RatingEntity as z2r
            join MaterialPricingItemEntity as mpi
            on z2r.materialPricingItemId = mpi.id
            join PartMetadataEntity as pm
            on mpi.id = pm.materialPricingItemId and pm.year = z2r.year
            join QuotationEngineEntity as qe
            on mpi.quotationEngineId = qe.id
            join ClusterEntity as c
            on mpi.clusterId = c.id
            join EngineClusterEntity as ec
            on ec.engineId = qe.engine.id and ec.clusterId = c.id
            join PartEntity as p
            on mpi.partId = p.id
            join EngineClusterPartEntity as ecp
            on ecp.engineClusterId = ec.id and ecp.partId = p.id
            where qe.quotation.id = :quotationId
            and pm.workscopeId = :workscopeId
            """)
    List<Z2RatingDto> findAllZ2RatingsByQuotationIdAndWorkscopeId(Long quotationId, Long workscopeId);

    @Query("""
            select z2r
            from Z2RatingEntity as z2r
            join MaterialPricingItemEntity as mpi
            on z2r.materialPricingItemId = mpi.id
            join QuotationEngineEntity as qe
            on mpi.quotationEngineId = qe.id
            where qe.quotation.id = :quotationId
            and z2r.id in :ids
            """)
    List<Z2RatingEntity> findAllByIdIn(Long quotationId, Collection<Long> ids);
}