package de.dlh.lht.ti.repository;

import de.dlh.lht.ti.entity.EngineEntity;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface EngineRepository extends JpaRepository<EngineEntity, Long> {

    @Query("select name from EngineEntity order by name asc")
    List<String> findAllEngineEntitiesNames();

    @Query("""
    select е.name
    from EngineEntity е
    join QuotationEngineEntity qe
    on qe.engine.id = е.id
    where qe.quotation.id = :quotationId
    """)
    String findEngineNameByQuotationId(Long quotationId);

    @Query("""
    select е
    from EngineEntity е
    join QuotationEngineEntity qe
    on qe.engine.id = е.id
    where qe.quotation.id = :quotationId
    """)
    EngineEntity findEngineEntityByQuotationId(Long quotationId);

    @Query("""
    select е.id
    from EngineEntity е
    join QuotationEngineEntity qe
    on qe.engine.id = е.id
    where qe.quotation.id = :quotationId
    """)
    Long findEngineIdByQuotationId(Long quotationId);
}
