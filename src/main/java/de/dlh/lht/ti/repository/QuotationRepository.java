package de.dlh.lht.ti.repository;

import de.dlh.lht.ti.dto.RfpQuotationDto;
import de.dlh.lht.ti.entity.QuotationEntity;
import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface QuotationRepository extends JpaRepository<QuotationEntity, Long> {

    boolean existsByPositionAndVersionAndScenarioAndProjectId(int position, int version, int scenario, Long projectId);

    @Query("""
            select q.contractStart
            from QuotationEntity as q
            where q.id = :quotationId
            """)
    Optional<ZonedDateTime> findContractStartById(Long quotationId);

    @Query("""
            select new de.dlh.lht.ti.dto.RfpQuotationDto(qe.engine.id, q.engineVersion, COALESCE(ci.isRfpEngineCiIncluded, false), COALESCE(ci.isRfpModuleCiIncluded, false), q.contractStart, q.usdExchangeRate )
            from QuotationEntity q
            left join CleaningAndInspectionEntity ci on ci.quotationId = q.id
            join QuotationEngineEntity qe on q.id = qe.quotation.id
            where q.id = :quotationId
            """)
    Optional<RfpQuotationDto> findRfpQuotationDtoById(Long quotationId);

    @Query("""
            select q.usdExchangeRate
            from QuotationEntity as q
            where q.id = :quotationId
            """)
    Optional<BigDecimal> findUsdExchangeRateByQuotationId(Long quotationId);
}
