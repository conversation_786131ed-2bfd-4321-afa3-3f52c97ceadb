package de.dlh.lht.ti.repository;

import de.dlh.lht.ti.dto.SubcontractDto;
import de.dlh.lht.ti.entity.SubcontractPricingItemEntity;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface SubcontractPricingItemRepository extends JpaRepository<SubcontractPricingItemEntity, Long> {

    @Query("""
            select spi
            from SubcontractPricingItemEntity as spi
            join QuotationEngineEntity as qe
            on spi.quotationEngineId = qe.id
            where qe.quotation.id = :quotationId
            """)
    List<SubcontractPricingItemEntity> findAllByQuotationId(Long quotationId);

    @Query("""
            select new de.dlh.lht.ti.dto.SubcontractDto(spi.id, spi.margin, spi.cap, c.id, c.name, ec.sortOrder)
            from SubcontractPricingItemEntity as spi
            join QuotationEngineEntity as qe
            on spi.quotationEngineId = qe.id
            join ClusterEntity as c
            on spi.clusterId = c.id
            join EngineClusterEntity as ec
            on ec.engineId = qe.engine.id and ec.clusterId = c.id
            where qe.quotation.id = :quotationId
            order by ec.sortOrder
            """)
    List<SubcontractDto> findAllSubcontractDtosByQuotationId(Long quotationId);
}
