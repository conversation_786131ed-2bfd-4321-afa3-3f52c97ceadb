package de.dlh.lht.ti.repository;

import de.dlh.lht.ti.entity.NavigationItemEntity;
import de.dlh.lht.ti.enums.QuotationProgress;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;

public interface NavigationItemRepository extends JpaRepository<NavigationItemEntity, Long> {

    Optional<NavigationItemEntity> findByQuotationIdAndProgressStepName(Long quotationId, QuotationProgress name);

    List<NavigationItemEntity> findAllByQuotationId(Long quotationId);
}
