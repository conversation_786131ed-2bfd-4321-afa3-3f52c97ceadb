package de.dlh.lht.ti.repository;

import de.dlh.lht.ti.entity.EscalationsMaterialPricesDefaultEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.math.BigDecimal;

public interface EscalationsMaterialPricesDefaultsEntityRepository extends JpaRepository<EscalationsMaterialPricesDefaultEntity, Long> {
    
    @Query("""
    select value
    from EscalationsMaterialPricesDefaultEntity
    where engineId = :engineId and year = :year
    """)
    BigDecimal findByYearAndEngineId(String year, Long engineId);
}