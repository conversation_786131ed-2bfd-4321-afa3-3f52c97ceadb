package de.dlh.lht.ti.repository;

import de.dlh.lht.ti.dto.RfpModuleDataDto;
import de.dlh.lht.ti.entity.RfpModuleEntity;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface RfpModuleRepository extends JpaRepository<RfpModuleEntity, Long> {

    @Query("""
            select rm
            from RfpModuleEntity as rm
            join LabourPricingItemEntity lpi
            on rm.labourPricingItemId = lpi.id
            join QuotationEngineEntity qe
            on lpi.quotationEngineId = qe.id
            where qe.quotation.id = :quotationId
            """)
    List<RfpModuleEntity> findAllByQuotationId(Long quotationId);

    @Query("""
            select new de.dlh.lht.ti.dto.RfpModuleDataDto(rm.id, rm.price, c.id, c.name, w.id, w.name, tm.manHours, tm.quantity, tm.weightedQuantity, tm.value, tm.currency, tm.isCleaningAndInspection)
            from RfpModuleEntity as rm
            join WorkscopeEntity as w
            on rm.workscopeId = w.id
            join LabourPricingItemEntity as lbi
            on rm.labourPricingItemId = lbi.id
            join QuotationEngineEntity as qe
            on lbi.quotationEngineId = qe.id
            join ClusterEntity as c
            on lbi.clusterId= c.id
            join TaskMetadataEntity as tm
            on tm.labourPricingItemId = lbi.id and tm.workscopeId = w.id
            where qe.quotation.id = :quotationId
                and tm.year = :year
                and lbi.type = de.dlh.lht.ti.enums.TaskType.RFP_MODULE
                and w.isSystem = true
            """)
    List<RfpModuleDataDto> findAllDtosByQuotationIdAndBaseYear(Long quotationId, String year);
}
