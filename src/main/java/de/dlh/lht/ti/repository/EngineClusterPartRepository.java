package de.dlh.lht.ti.repository;

import de.dlh.lht.ti.dto.importer.EngineClusterPartDto;
import de.dlh.lht.ti.entity.EngineClusterPartEntity;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface EngineClusterPartRepository extends JpaRepository<EngineClusterPartEntity, Long> {

    @Query("""
            select new de.dlh.lht.ti.dto.importer.EngineClusterPartDto(e.id, c.id, p.id)
            from EngineClusterPartEntity as ecp
            join PartEntity as p
            on ecp.partId = p.id
            join EngineClusterEntity as ec
            on ecp.engineClusterId = ec.id
            join EngineEntity as e
            on ec.engineId = e.id
            join ClusterEntity as c
            on ec.clusterId = c.id
            """)
    List<EngineClusterPartDto> findAllEngineClusterPartDtos();
}
