package de.dlh.lht.ti.repository;

import de.dlh.lht.ti.dto.ProductionCostDto;
import de.dlh.lht.ti.entity.SubcontractMetadataEntity;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface SubcontractMetadataRepository extends JpaRepository<SubcontractMetadataEntity, Long> {

    @Query("""
            select new de.dlh.lht.ti.dto.ProductionCostDto(sm.workscopeId, sm.year, sm.quantity, sm.value, sm.weightedQuantity, sm.currency)
            from SubcontractMetadataEntity as sm
            join SubcontractPricingItemEntity as spi
            on sm.subcontractPricingItemId = spi.id
            join QuotationEngineEntity as qe
            on spi.quotationEngineId = qe.id
            where qe.quotation.id = :quotationId
            """)
    List<ProductionCostDto> findAllProductionCostMetadataByQuotationId(Long quotationId);
}
