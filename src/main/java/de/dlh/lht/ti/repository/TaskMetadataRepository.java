package de.dlh.lht.ti.repository;

import de.dlh.lht.ti.dto.ProductionCostDto;
import de.dlh.lht.ti.dto.RfpEngineMetadataDto;
import de.dlh.lht.ti.entity.TaskMetadataEntity;
import de.dlh.lht.ti.enums.TaskType;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface TaskMetadataRepository extends JpaRepository<TaskMetadataEntity, Long> {

    @Query("""
            select new de.dlh.lht.ti.dto.RfpEngineMetadataDto(lpi.id, c.name, tm.name, tm.manHours, tm.quantity, tm.weightedQuantity, tm.value, tm.currency, tm.isCleaningAndInspection)
            from TaskMetadataEntity as tm
            join LabourPricingItemEntity as lpi
            on tm.labourPricingItemId = lpi.id
            join ClusterEntity as c
            on lpi.clusterId = c.id
            join QuotationEngineEntity as qe
            on lpi.quotationEngineId = qe.id
            where qe.quotation.id = :quotationId and lpi.type =:type and tm.workscopeId = :workscopeId and tm.year = :year
            """)
    List<RfpEngineMetadataDto> findAllByQuotationIdWorkscopeIdYearAndTaskType(
            Long quotationId,
            TaskType type,
            Long workscopeId,
            String year
    );

    @Query("""
            select new de.dlh.lht.ti.dto.ProductionCostDto(tm.workscopeId, tm.year, tm.quantity, tm.value, tm.weightedQuantity, tm.currency)
            from TaskMetadataEntity as tm
            join LabourPricingItemEntity as lpi
            on tm.labourPricingItemId = lpi.id
            join QuotationEngineEntity as qe
            on lpi.quotationEngineId = qe.id
            where qe.quotation.id = :quotationId
            """)
    List<ProductionCostDto> findAllProductionCostMetadataByQuotationId(Long quotationId);
}
