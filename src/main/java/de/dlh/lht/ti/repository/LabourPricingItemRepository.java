package de.dlh.lht.ti.repository;

import de.dlh.lht.ti.dto.LabourPricingItemDto;
import de.dlh.lht.ti.entity.LabourPricingItemEntity;
import de.dlh.lht.ti.enums.TaskType;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface LabourPricingItemRepository extends JpaRepository<LabourPricingItemEntity, Long> {

    //todo merge those two request in one
    @Query("""
            select lpi.id
            from LabourPricingItemEntity as lpi
            join QuotationEngineEntity as qe
            on lpi.quotationEngineId = qe.id
            where lpi.type = de.dlh.lht.ti.enums.TaskType.RFP_MODULE
                and qe.quotation.id = :quotationId
            """)
    List<Long> findAllRfpModuleLabourPricingItemIdsByQuotationId(Long quotationId);

    @Query("""
            select new de.dlh.lht.ti.dto.LabourPricingItemDto(lpi.id, t.name)
            from LabourPricingItemEntity as lpi
            join ClusterEntity c
            on lpi.clusterId = c.id
            join QuotationEngineEntity qe
            on lpi.quotationEngineId = qe.id
            join EngineClusterEntity ec
            on ec.engineId = qe.engine.id and ec.clusterId = c.id
            join EngineClusterTaskEntity ect
            on ect.engineClusterId = ec.id
            join TaskEntity t
            on t.id = ect.taskId
            where lpi.quotationEngineId = :quotationEngineId
            """)
    List<LabourPricingItemDto> findAllLabourPricingItemsByQuotationEngineId(
            Long quotationEngineId
    );

    @Query("""
            select new de.dlh.lht.ti.dto.LabourPricingItemDto(lpi.id, c.name)
            from LabourPricingItemEntity as lpi
            join ClusterEntity c
            on lpi.clusterId = c.id
            join QuotationEngineEntity qe
            on lpi.quotationEngineId = qe.id and lpi.clusterId = c.id
            where lpi.quotationEngineId = :quotationEngineId and lpi.type in :types
            """)
    List<LabourPricingItemDto> findAllLabourPricingItemsFromDynamicClusterByQuotationEngineId(
            Long quotationEngineId,
            List<TaskType> types
    );
}
