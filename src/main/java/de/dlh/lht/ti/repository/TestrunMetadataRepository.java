package de.dlh.lht.ti.repository;

import de.dlh.lht.ti.dto.ProductionCostDto;
import de.dlh.lht.ti.entity.TestrunMetadataEntity;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface TestrunMetadataRepository extends JpaRepository<TestrunMetadataEntity, Long> {

    @Query("""
            select new de.dlh.lht.ti.dto.ProductionCostDto(tm.workscopeId, tm.year, tm.quantity, tm.value, tm.weightedQuantity, tm.currency)
            from TestrunMetadataEntity tm
            join TestrunItemEntity as ti
            on tm.testrunItemId = ti.id
            join QuotationEngineEntity as qe
            on ti.quotationEngineId = qe.id
            where qe.quotation.id = :quotationId
            """)
    List<ProductionCostDto> findAllProductionCostMetadataByQuotationId(Long quotationId);
}
