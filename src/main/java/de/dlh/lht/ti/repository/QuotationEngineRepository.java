package de.dlh.lht.ti.repository;

import de.dlh.lht.ti.dto.QuotationDetailsDto;
import de.dlh.lht.ti.entity.QuotationEngineEntity;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface QuotationEngineRepository extends JpaRepository<QuotationEngineEntity, Long> {

    @Query("""
            select new de.dlh.lht.ti.dto.QuotationDetailsDto(qe.quotation, qe.engine)
            from QuotationEngineEntity qe
            where qe.quotation.id = :quotationId
            """)
    Optional<QuotationDetailsDto> findQuotationDetailsDto(Long quotationId);

    @Query("""
            select q
            from QuotationEngineEntity as q
            where
                 q.quotation.project.currentOwner.id = coalesce(:ownerId, q.quotation.project.currentOwner.id)
                 and q.quotation.project.offerNumber = coalesce(:offerNumber, q.quotation.project.offerNumber)
                 and q.quotation.version = coalesce(:version, q.quotation.version)
                 and q.quotation.position = coalesce(:position, q.quotation.position)
                 and q.quotation.scenario = coalesce(:scenario, q.quotation.scenario)
                 and q.quotation.status = coalesce(:quotationStatus, q.quotation.status)
                 and q.engine.name = coalesce(:engineType, q.engine.name)
                 and q.quotation.customer.threeLetterCode = coalesce(:customer, q.quotation.customer.threeLetterCode)
            """)
    Page<QuotationEngineEntity> findAllWithAppliedFilter(
            Pageable request,
            @Param("ownerId") Long ownerId,
            @Param("offerNumber") String offerNumber,
            @Param("version") Integer version,
            @Param("position") Integer position,
            @Param("scenario") Integer scenario,
            @Param("quotationStatus") Integer quotationStatus,
            @Param("engineType") String engineType,
            @Param("customer") String customer
    );

    @Query("""
            select e.name
            from QuotationEngineEntity qe
            join qe.engine e
            where qe.quotation.id = :quotationId
            """)
    Optional<String> findQuotationEngineName(Long quotationId);
}
