package de.dlh.lht.ti.repository;

import de.dlh.lht.ti.dto.RfpEngineItemDto;
import de.dlh.lht.ti.entity.RfpEngineItemEntity;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface RfpEngineItemRepository extends JpaRepository<RfpEngineItemEntity, Long> {

    @Query("""
            select new de.dlh.lht.ti.dto.RfpEngineItemDto(rei.labourPricingItemId,c.id, c.name, ec.sortOrder, rei.rfpEngineBaseCost, rei.rfpEngineCiHoursCost, rei.rfpEnginePrice)
            from RfpEngineItemEntity rei
            join LabourPricingItemEntity lpi
            on rei.labourPricingItemId = lpi.id
            join ClusterEntity c
            on c.id = lpi.clusterId
            join QuotationEngineEntity qe
            on lpi.quotationEngineId = qe.id
            join EngineClusterEntity ec
            on qe.engine.id = ec.engineId and ec.clusterId = c.id
            where qe.quotation.id = :quotationId
            """)
    List<RfpEngineItemDto> findAllDtosByQuotationId(Long quotationId);

    @Query("""
            select rfpep
            from RfpEngineItemEntity rfpep
            join LabourPricingItemEntity lpi
            on rfpep.labourPricingItemId = lpi.id
            join QuotationEngineEntity qe
            on lpi.quotationEngineId = qe.id
            where qe.quotation.id = :quotationId
            """)
    List<RfpEngineItemEntity> findAllByQuotationId(Long quotationId);
}