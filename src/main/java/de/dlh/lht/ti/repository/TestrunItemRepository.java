package de.dlh.lht.ti.repository;

import de.dlh.lht.ti.dto.TestrunItemDto;
import de.dlh.lht.ti.entity.TestrunItemEntity;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface TestrunItemRepository extends JpaRepository<TestrunItemEntity, Long> {

    @Query("""
            select ti
            from TestrunItemEntity as ti
            join QuotationEngineEntity as qe
            on ti.quotationEngineId = qe.id
            where qe.quotation.id = :quotationId
            """)
    Optional<TestrunItemEntity> findByQuotationId(Long quotationId);

    @Query("""
            select new de.dlh.lht.ti.dto.TestrunItemDto(ti.id, ti.price, c.id, c.name, ec.sortOrder, tm.quantity, tm.weightedQuantity,tm.value, tm.currency)
            from TestrunItemEntity as ti
            join QuotationEngineEntity as qe
            on ti.quotationEngineId = qe.id
            join ClusterEntity as c
            on ti.clusterId = c.id
            join EngineClusterEntity as ec
            on ec.engineId = qe.engine.id and ec.clusterId = c.id
            join TestrunMetadataEntity as tm
            on tm.testrunItemId = ti.id
            where qe.quotation.id = :quotationId and tm.year = :year and tm.workscopeId = :workscopeId
            """)
    Optional<TestrunItemDto> findDtoByQuotationIdYearAndWorkscopeId(Long quotationId, String year, Long workscopeId);
}
