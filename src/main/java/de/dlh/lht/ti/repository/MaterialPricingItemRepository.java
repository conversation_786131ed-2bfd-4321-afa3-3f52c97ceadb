package de.dlh.lht.ti.repository;

import de.dlh.lht.ti.entity.MaterialPricingItemEntity;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface MaterialPricingItemRepository extends JpaRepository<MaterialPricingItemEntity, Long> {

    @Query("""
            select mpi
            from MaterialPricingItemEntity as mpi
            join QuotationEngineEntity as qe
            on mpi.quotationEngineId = qe.id
            where qe.quotation.id = :quotationId
            """)
    List<MaterialPricingItemEntity> findAllIdsByQuotationId(Long quotationId);

    @Query("""
            select mpi.id
            from MaterialPricingItemEntity as mpi
            join QuotationEngineEntity as qe
            on mpi.quotationEngineId = qe.id
            where qe.quotation.id = :quotationId
            """)
    List<Long> findAllMaterialPricingItemsIdsByQuotationId(Long quotationId);
}
