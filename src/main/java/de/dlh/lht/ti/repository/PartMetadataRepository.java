package de.dlh.lht.ti.repository;

import de.dlh.lht.ti.dto.discounts.MaterialDiscountDto;
import de.dlh.lht.ti.dto.ProductionCostDto;
import de.dlh.lht.ti.entity.PartMetadataEntity;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface PartMetadataRepository extends JpaRepository<PartMetadataEntity, Long> {

    @Query("""
            select distinct pm.year
            from PartMetadataEntity as pm
            join MaterialPricingItemEntity as mpi
            on pm.materialPricingItemId = mpi.id
            join QuotationEngineEntity as qe
            on mpi.quotationEngineId = qe.id
            where qe.quotation.id = :quotationId
            """)
    List<String> findAllUniqueYearsOfPartMetadataByQuotationId(Long quotationId);

    @Query("""
            select new de.dlh.lht.ti.dto.ProductionCostDto(pm.workscopeId, pm.year, pm.quantity, pm.z1Value, pm.z1WeightedQuantity, pm.z1Currency)
            from PartMetadataEntity as pm
            join MaterialPricingItemEntity as mpi
            on pm.materialPricingItemId = mpi.id
            join QuotationEngineEntity as qe
            on mpi.quotationEngineId = qe.id
            where qe.quotation.id = :quotationId
            """)
    List<ProductionCostDto> findAllZ1ProductionCostMetadataByQuotationId(Long quotationId);

    @Query("""
            select new de.dlh.lht.ti.dto.ProductionCostDto(pm.workscopeId, pm.year, pm.quantity, pm.z2Value, pm.z2WeightedQuantity, pm.z2Currency)
            from PartMetadataEntity as pm
            join MaterialPricingItemEntity as mpi
            on pm.materialPricingItemId = mpi.id
            join QuotationEngineEntity as qe
            on mpi.quotationEngineId = qe.id
            where qe.quotation.id = :quotationId
            """)
    List<ProductionCostDto> findAllZ2ProductionCostMetadataByQuotationId(Long quotationId);

    @Query("""
            select new de.dlh.lht.ti.dto.ProductionCostDto(pm.workscopeId, pm.year, pm.quantity, pm.pmaValue, pm.pmaWeightedQuantity, pm.pmaCurrency)
            from PartMetadataEntity as pm
            join MaterialPricingItemEntity as mpi
            on pm.materialPricingItemId = mpi.id
            join QuotationEngineEntity as qe
            on mpi.quotationEngineId = qe.id
            where qe.quotation.id = :quotationId
            """)
    List<ProductionCostDto> findAllPmaProductionCostMetadataByQuotationId(Long quotationId);

    @Query("""
            select new de.dlh.lht.ti.dto.ProductionCostDto(pm.workscopeId, pm.year, pm.quantity, pm.csmValue, pm.csmWeightedQuantity, pm.csmCurrency)
            from PartMetadataEntity as pm
            join MaterialPricingItemEntity as mpi
            on pm.materialPricingItemId = mpi.id
            join QuotationEngineEntity as qe
            on mpi.quotationEngineId = qe.id
            where qe.quotation.id = :quotationId
            """)
    List<ProductionCostDto> findAllCsmProductionCostMetadataByQuotationId(Long quotationId);

    @Query("""
            select new de.dlh.lht.ti.dto.discounts.MaterialDiscountDto(pm.workscopeId, w.workscopeClass, pm.year, pm.name, pm.quantity, p.type, mpi.nonRoutineMaterialSubType, pm.z1Value, pm.z1WeightedQuantity, pm.z1Currency)
            from PartMetadataEntity as pm
            join MaterialPricingItemEntity as mpi
            on pm.materialPricingItemId = mpi.id
            join PartEntity as p
            on mpi.partId = p.id
            join WorkscopeEntity as w
            on pm.workscopeId = w.id
            join QuotationEngineEntity as qe
            on mpi.quotationEngineId = qe.id
            where qe.quotation.id = :quotationId and pm.z1Value is not null and pm.z1WeightedQuantity is not null and pm.quantity is not null
            """)
    List<MaterialDiscountDto> findAllMaterialDiscountsByQuotationId(Long quotationId);
}
