package de.dlh.lht.ti.repository;

import de.dlh.lht.ti.entity.EparPriceEntity;
import java.math.BigDecimal;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface EparPriceRepository extends JpaRepository<EparPriceEntity, Long> {

    @Query("""
            select ep.cleaningAndInspection
            from EparPriceEntity as ep
            where ep.engineId = :engineId
                and ep.year = :year
                and (:engineVersion is null
                    or ep.engineVersion = :engineVersion)
            """)
    Optional<BigDecimal> findCleaningAndInspectionEparPriceByEngineIdVersionAndYear(Long engineId, String engineVersion, String year);
}
