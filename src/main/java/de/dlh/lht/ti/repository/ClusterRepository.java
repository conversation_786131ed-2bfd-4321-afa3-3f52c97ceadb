package de.dlh.lht.ti.repository;

import de.dlh.lht.ti.dto.ClusterTaskDto;
import de.dlh.lht.ti.entity.ClusterEntity;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface ClusterRepository extends JpaRepository<ClusterEntity, Long> {

    @Query(value = """
            select c.id
            from ClusterEntity as c
            where c.name = :name
            """)
    Optional<Long> findIdByName(String name);

    List<ClusterEntity> findAllByNameIn(List<String> names);

    @Query(value = """
            select new de.dlh.lht.ti.dto.ClusterTaskDto(c.id, t.name)
            from ClusterEntity as c
            join EngineClusterEntity as ec
            on ec.clusterId = c.id
            join EngineClusterTaskEntity as ect
            on ect.engineClusterId = ec.id
            join TaskEntity as t
            on t.id = ect.taskId
            where ec.engineId = :engineId
            """)
    List<ClusterTaskDto> findAllClusterTasksByEngineId(Long engineId);
}
