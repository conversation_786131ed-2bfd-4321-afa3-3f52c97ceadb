package de.dlh.lht.ti.repository;

import de.dlh.lht.ti.dto.summary.WorkscopeSummaryDto;
import de.dlh.lht.ti.entity.WorkscopeSummaryEntity;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface WorkscopeSummaryRepository extends JpaRepository<WorkscopeSummaryEntity, Long> {

    @Query("""
            select new de.dlh.lht.ti.dto.summary.WorkscopeSummaryDto(w.id, w.name, ws.year, ws.revenueUsd, ws.revenueEur, ws.productionCostUsd, ws.productionCostEur, ws.discountUsd, ws.discountEur, ws.surchargesCostUsd, ws.surchargesCostEur)
            from WorkscopeSummaryEntity as ws
            join WorkscopeEntity as w
            on ws.workscopeId = w.id
            where ws.quotationId = :quotationId and w.isSystem = false
            """)
    List<WorkscopeSummaryDto> findAllByQuotationId(Long quotationId);
}
