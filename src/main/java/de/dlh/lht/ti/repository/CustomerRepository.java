package de.dlh.lht.ti.repository;

import de.dlh.lht.ti.entity.CustomerEntity;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface CustomerRepository extends JpaRepository<CustomerEntity, Long> {

    @Query("select threeLetterCode from CustomerEntity order by threeLetterCode asc")
    List<String> findAllCustomerEntitiesNames();
}
