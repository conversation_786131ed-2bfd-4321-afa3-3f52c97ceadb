package de.dlh.lht.ti.repository;

import de.dlh.lht.ti.dto.HandlingChargeDto;
import de.dlh.lht.ti.entity.HandlingChargeEntity;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface HandlingChargeRepository extends JpaRepository<HandlingChargeEntity, Long> {

    @Query("""
            select new de.dlh.lht.ti.dto.HandlingChargeDto(hc, c.id, c.name, ec.sortOrder, p.id, p.name, ecp.sortOrder, p.type, pm.quantity)
            from HandlingChargeEntity as hc
            join MaterialPricingItemEntity as mpi
            on hc.materialPricingItemId = mpi.id
            join QuotationEngineEntity as qe
            on mpi.quotationEngineId = qe.id
            join ClusterEntity as c
            on mpi.clusterId = c.id
            join EngineClusterEntity as ec
            on ec.engineId = qe.engine.id and ec.clusterId = c.id
            join PartEntity as p
            on mpi.partId = p.id
            join EngineClusterPartEntity as ecp
            on ecp.engineClusterId = ec.id and ecp.partId = p.id
            join PartMetadataEntity as pm
            on pm.materialPricingItemId = mpi.id
            where pm.workscopeId = :workscopeId
            and pm.year = :year
            and qe.quotation.id = :quotationId
            """)
    List<HandlingChargeDto> findAllByQuotationId(Long quotationId, Long workscopeId, String year);
}
