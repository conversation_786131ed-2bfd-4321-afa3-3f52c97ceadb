package de.dlh.lht.ti.controller;

import com.cleverpine.cpspringerrorutil.util.GenericResponseEntityUtil;
import com.cleverpine.viravaspringhelper.dto.ScopeType;
import de.dlh.lht.ti.api.ProjectApi;
import de.dlh.lht.ti.auth.ViravaSecured;
import de.dlh.lht.ti.auth.roles.Resources;
import de.dlh.lht.ti.model.ChangeOwnerRequest;
import de.dlh.lht.ti.service.contract.ProjectService;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;


import static de.dlh.lht.ti.utils.LoggingMessages.CHANGE_OWNER_REQUEST;
import static de.dlh.lht.ti.utils.LoggingMessages.CHANGE_OWNER_RESPONSE;

@Log4j2
@RestController
@RequiredArgsConstructor
public class ProjectController implements ProjectApi {

    private final ProjectService projectService;

    private final GenericResponseEntityUtil genericResponseEntityUtil;

    @Override
    @ViravaSecured(resource = Resources.PROJECT, scope = ScopeType.UPDATE)
    public ResponseEntity<Void> changeOwner(String offerNumber, ChangeOwnerRequest changeOwnerRequest) {
        log.debug(CHANGE_OWNER_REQUEST, offerNumber, changeOwnerRequest);
        projectService.changeOwner(offerNumber, changeOwnerRequest.getId());

        log.debug(CHANGE_OWNER_RESPONSE, offerNumber);
        return genericResponseEntityUtil.ok();
    }
}
