package de.dlh.lht.ti.controller;

import com.cleverpine.cpspringerrorutil.util.GenericResponseEntityUtil;
import com.cleverpine.viravaspringhelper.dto.ScopeType;
import de.dlh.lht.ti.api.LabourPricingApi;
import de.dlh.lht.ti.auth.ViravaSecured;
import de.dlh.lht.ti.auth.roles.Resources;
import de.dlh.lht.ti.model.LabourRate;
import de.dlh.lht.ti.model.LabourRateInput;
import de.dlh.lht.ti.model.LabourRateResponse;
import de.dlh.lht.ti.model.LabourRfpEngineResponse;
import de.dlh.lht.ti.model.RfpEngine;
import de.dlh.lht.ti.model.RfpModule;
import de.dlh.lht.ti.model.RfpModuleResponse;
import de.dlh.lht.ti.model.RfpRequest;
import de.dlh.lht.ti.service.contract.LabourPricingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;


import static de.dlh.lht.ti.utils.LoggingMessages.GET_LABOUR_RATES_REQUEST;
import static de.dlh.lht.ti.utils.LoggingMessages.GET_LABOUR_RATES_RESPONSE;
import static de.dlh.lht.ti.utils.LoggingMessages.GET_LABOUR_RFP_ENGINE_REQUEST;
import static de.dlh.lht.ti.utils.LoggingMessages.GET_LABOUR_RFP_ENGINE_RESPONSE;
import static de.dlh.lht.ti.utils.LoggingMessages.GET_RFP_MODULE_REQUEST;
import static de.dlh.lht.ti.utils.LoggingMessages.GET_RFP_MODULE_RESPONSE;
import static de.dlh.lht.ti.utils.LoggingMessages.SAVE_LABOUR_RATES_REQUEST;
import static de.dlh.lht.ti.utils.LoggingMessages.SAVE_LABOUR_RATES_RESPONSE;
import static de.dlh.lht.ti.utils.LoggingMessages.SAVE_LABOUR_RFP_ENGINE_REQUEST;
import static de.dlh.lht.ti.utils.LoggingMessages.SAVE_LABOUR_RFP_ENGINE_RESPONSE;
import static de.dlh.lht.ti.utils.LoggingMessages.SAVE_RFP_MODULE_REQUEST;
import static de.dlh.lht.ti.utils.LoggingMessages.SAVE_RFP_MODULE_RESPONSE;

@Log4j2
@RestController
@RequiredArgsConstructor
public class LabourPricingController implements LabourPricingApi {

    private final LabourPricingService labourPricingService;

    private final GenericResponseEntityUtil genericResponseEntityUtil;

    @Override
    @ViravaSecured(resource = Resources.QUOTATION, scope = ScopeType.READ)
    public ResponseEntity<LabourRateResponse> getLabourRates(Long quotationId) {
        log.debug(GET_LABOUR_RATES_REQUEST, quotationId);
        var response = labourPricingService.getLabourRates(quotationId);
        log.debug(GET_LABOUR_RATES_RESPONSE, quotationId, response);

        return genericResponseEntityUtil.ok(response, LabourRate.class, LabourRateResponse.class);
    }

    @Override
    @ViravaSecured(resource = Resources.QUOTATION, scope = ScopeType.UPDATE)
    public ResponseEntity<LabourRateResponse> saveLabourRates(Long quotationId, LabourRateInput labourRateInput) {
        log.debug(SAVE_LABOUR_RATES_REQUEST, quotationId, labourRateInput);
        var response = labourPricingService.saveLabourRates(quotationId, labourRateInput);
        log.debug(SAVE_LABOUR_RATES_RESPONSE, quotationId, response);

        return genericResponseEntityUtil.ok(response, LabourRate.class, LabourRateResponse.class);
    }

    @Override
    @ViravaSecured(resource = Resources.QUOTATION, scope = ScopeType.READ)
    public ResponseEntity<LabourRfpEngineResponse> getRfpEngineData(Long quotationId) {
        log.debug(GET_LABOUR_RFP_ENGINE_REQUEST, quotationId);
        var response = labourPricingService.getRfpEngineData(quotationId);
        log.debug(GET_LABOUR_RFP_ENGINE_RESPONSE, quotationId, response);

        return genericResponseEntityUtil.ok(response, RfpEngine.class, LabourRfpEngineResponse.class);
    }

    @Override
    @ViravaSecured(resource = Resources.QUOTATION, scope = ScopeType.UPDATE)
    public ResponseEntity<LabourRfpEngineResponse> saveRfpEngineData(Long quotationId, RfpRequest rfpRequest) {
        log.debug(SAVE_LABOUR_RFP_ENGINE_REQUEST, quotationId, rfpRequest);
        var response = labourPricingService.saveRfpEngineData(quotationId, rfpRequest);
        log.debug(SAVE_LABOUR_RFP_ENGINE_RESPONSE, quotationId, response);

        return genericResponseEntityUtil.ok(response, RfpEngine.class, LabourRfpEngineResponse.class);
    }

    @Override
    @ViravaSecured(resource = Resources.QUOTATION, scope = ScopeType.READ)
    public ResponseEntity<RfpModuleResponse> getRfpModule(Long quotationId) {
        log.debug(GET_RFP_MODULE_REQUEST, quotationId);
        var response = labourPricingService.getRfpModule(quotationId);
        log.debug(GET_RFP_MODULE_RESPONSE, quotationId, response);

        return genericResponseEntityUtil.ok(response, RfpModule.class, RfpModuleResponse.class);
    }

    @Override
    @ViravaSecured(resource = Resources.QUOTATION, scope = ScopeType.UPDATE)
    public ResponseEntity<RfpModuleResponse> saveRfpModule(Long quotationId, RfpRequest rfpRequest) {
        log.debug(SAVE_RFP_MODULE_REQUEST, quotationId, rfpRequest);
        var response = labourPricingService.saveRfpModule(quotationId, rfpRequest);
        log.debug(SAVE_RFP_MODULE_RESPONSE, quotationId, response);

        return genericResponseEntityUtil.ok(response, RfpModule.class, RfpModuleResponse.class);
    }
}
