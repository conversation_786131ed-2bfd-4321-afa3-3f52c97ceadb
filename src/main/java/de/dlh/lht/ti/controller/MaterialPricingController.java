package de.dlh.lht.ti.controller;

import com.cleverpine.cpspringerrorutil.util.GenericResponseEntityUtil;
import com.cleverpine.springlogginglibrary.aop.PerformanceMeasure;
import com.cleverpine.viravaspringhelper.dto.ScopeType;
import de.dlh.lht.ti.api.MaterialPricingApi;
import de.dlh.lht.ti.auth.ViravaSecured;
import de.dlh.lht.ti.auth.roles.Resources;
import de.dlh.lht.ti.mapper.MaterialPricingControllerMapper;
import de.dlh.lht.ti.model.HandlingCharges;
import de.dlh.lht.ti.model.HandlingChargesRequest;
import de.dlh.lht.ti.model.HandlingChargesResponse;
import de.dlh.lht.ti.model.Z2Ratings;
import de.dlh.lht.ti.model.Z2RatingsRequest;
import de.dlh.lht.ti.model.Z2RatingsResponse;
import de.dlh.lht.ti.service.contract.MaterialPricingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;


import static de.dlh.lht.ti.utils.LoggingMessages.GET_HANDLING_CHARGES_REQUEST;
import static de.dlh.lht.ti.utils.LoggingMessages.GET_HANDLING_CHARGES_RESPONSE;
import static de.dlh.lht.ti.utils.LoggingMessages.GET_Z2_RATINGS_REQUEST;
import static de.dlh.lht.ti.utils.LoggingMessages.GET_Z2_RATINGS_RESPONSE;
import static de.dlh.lht.ti.utils.LoggingMessages.SAVE_HANDLING_CHARGES_REQUEST;
import static de.dlh.lht.ti.utils.LoggingMessages.SAVE_HANDLING_CHARGES_RESPONSE;
import static de.dlh.lht.ti.utils.LoggingMessages.SAVE_Z2_RATINGS_REQUEST;
import static de.dlh.lht.ti.utils.LoggingMessages.SAVE_Z2_RATINGS_RESPONSE;

@Log4j2
@RestController
@RequiredArgsConstructor
public class MaterialPricingController implements MaterialPricingApi {

    private final MaterialPricingControllerMapper materialPricingControllerMapper;

    private final MaterialPricingService materialPricingService;

    private final GenericResponseEntityUtil genericResponseEntityUtil;

    @Override
    @ViravaSecured(resource = Resources.QUOTATION, scope = ScopeType.READ)
    public ResponseEntity<HandlingChargesResponse> getHandlingCharges(Long quotationId) {
        log.debug(GET_HANDLING_CHARGES_REQUEST, quotationId);
        var response = materialPricingService.getHandlingCharges(quotationId);
        log.debug(GET_HANDLING_CHARGES_RESPONSE, response);

        return genericResponseEntityUtil.ok(response, HandlingCharges.class, HandlingChargesResponse.class);
    }

    @Override
    @ViravaSecured(resource = Resources.QUOTATION, scope = ScopeType.UPDATE)
    public ResponseEntity<HandlingChargesResponse> saveHandlingCharges(Long quotationId, HandlingChargesRequest handlingChargesRequest) {
        log.debug(SAVE_HANDLING_CHARGES_REQUEST, handlingChargesRequest);
        var response = materialPricingService.saveHandlingCharges(quotationId, handlingChargesRequest);
        log.debug(SAVE_HANDLING_CHARGES_RESPONSE, response);

        return genericResponseEntityUtil.ok(response, HandlingCharges.class, HandlingChargesResponse.class);
    }

    @Override
    @ViravaSecured(resource = Resources.QUOTATION, scope = ScopeType.READ)
    public ResponseEntity<Z2RatingsResponse> getZ2Ratings(Long quotationId) {
        log.debug(GET_Z2_RATINGS_REQUEST, quotationId);
        var response = materialPricingService.getZ2Ratings(quotationId);
        log.debug(GET_Z2_RATINGS_RESPONSE, quotationId, response);

        return genericResponseEntityUtil.ok(response, Z2Ratings.class, Z2RatingsResponse.class);
    }

    @Override
    @ViravaSecured(resource = Resources.QUOTATION, scope = ScopeType.UPDATE)
    public ResponseEntity<Z2RatingsResponse> saveZ2Ratings(Long quotationId, Z2RatingsRequest z2RatingsRequest) {
        log.debug(SAVE_Z2_RATINGS_REQUEST, quotationId, z2RatingsRequest);
        var rawData = materialPricingControllerMapper.mapZ2RatingsToRawDataInput(z2RatingsRequest);
        var response = materialPricingService.saveZ2Ratings(quotationId, rawData);
        log.debug(SAVE_Z2_RATINGS_RESPONSE, quotationId, response);

        return genericResponseEntityUtil.ok(response, Z2Ratings.class, Z2RatingsResponse.class);
    }
}
