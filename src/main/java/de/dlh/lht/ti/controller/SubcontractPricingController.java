package de.dlh.lht.ti.controller;

import com.cleverpine.cpspringerrorutil.util.GenericResponseEntityUtil;
import com.cleverpine.viravaspringhelper.dto.ScopeType;
import de.dlh.lht.ti.api.SubcontractPricingApi;
import de.dlh.lht.ti.auth.ViravaSecured;
import de.dlh.lht.ti.auth.roles.Resources;
import de.dlh.lht.ti.model.SubcontractPricing;
import de.dlh.lht.ti.model.SubcontractPricingRequest;
import de.dlh.lht.ti.model.SubcontractPricingResponse;
import de.dlh.lht.ti.service.contract.SubcontractPricingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;


import static de.dlh.lht.ti.utils.LoggingMessages.GET_SUBCONTRACT_PRICING_REQUEST;
import static de.dlh.lht.ti.utils.LoggingMessages.GET_SUBCONTRACT_PRICING_RESPONSE;
import static de.dlh.lht.ti.utils.LoggingMessages.SAVE_SUBCONTRACT_PRICING_REQUEST;
import static de.dlh.lht.ti.utils.LoggingMessages.SAVE_SUBCONTRACT_PRICING_RESPONSE;

@Log4j2
@RestController
@RequiredArgsConstructor
public class SubcontractPricingController implements SubcontractPricingApi {

    private final SubcontractPricingService subcontractPricingService;

    private final GenericResponseEntityUtil genericResponseEntityUtil;

    @Override
    @ViravaSecured(resource = Resources.QUOTATION, scope = ScopeType.READ)
    public ResponseEntity<SubcontractPricingResponse> getSubcontractPricing(Long quotationId) {
        log.debug(GET_SUBCONTRACT_PRICING_REQUEST, quotationId);
        var response = subcontractPricingService.getSubcontractPricing(quotationId);
        log.debug(GET_SUBCONTRACT_PRICING_RESPONSE, quotationId, response);

        return genericResponseEntityUtil.ok(response, SubcontractPricing.class, SubcontractPricingResponse.class);
    }

    @Override
    @ViravaSecured(resource = Resources.QUOTATION, scope = ScopeType.UPDATE)
    public ResponseEntity<SubcontractPricingResponse> saveSubcontractPricing(
            Long quotationId,
            SubcontractPricingRequest subcontractPricingRequest) {
        log.debug(SAVE_SUBCONTRACT_PRICING_REQUEST, quotationId, subcontractPricingRequest);
        var response = subcontractPricingService.saveSubcontractPricing(quotationId, subcontractPricingRequest);
        log.debug(SAVE_SUBCONTRACT_PRICING_RESPONSE, quotationId, response);

        return genericResponseEntityUtil.ok(response, SubcontractPricing.class, SubcontractPricingResponse.class);
    }
}
