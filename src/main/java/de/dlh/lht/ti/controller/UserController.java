package de.dlh.lht.ti.controller;

import com.cleverpine.cpspringerrorutil.util.GenericResponseEntityUtil;
import com.cleverpine.viravaspringhelper.dto.ScopeType;
import de.dlh.lht.ti.api.UserApi;
import de.dlh.lht.ti.auth.ViravaSecured;
import de.dlh.lht.ti.auth.roles.Resources;
import de.dlh.lht.ti.model.QuotationOwnerResponse;
import de.dlh.lht.ti.model.UserDetails;
import de.dlh.lht.ti.model.UserDetailsResponse;
import de.dlh.lht.ti.service.contract.UserService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;


import static de.dlh.lht.ti.utils.LoggingMessages.GET_USERS_FOR_CHANGE_OWNER_REQUEST;
import static de.dlh.lht.ti.utils.LoggingMessages.GET_USERS_FOR_CHANGE_OWNER_RESPONSE;
import static de.dlh.lht.ti.utils.LoggingMessages.GET_USER_DETAILS_RESPONSE;

@Log4j2
@RestController
@RequiredArgsConstructor
public class UserController implements UserApi {

    private final UserService userService;

    private final GenericResponseEntityUtil genericResponseEntityUtil;

    @Override
    @ViravaSecured(resource = Resources.USER, scope = ScopeType.READ)
    public ResponseEntity<QuotationOwnerResponse> getAllUsers() {
        log.debug(GET_USERS_FOR_CHANGE_OWNER_REQUEST);
        var response = userService.getAllUsers();
        log.debug(GET_USERS_FOR_CHANGE_OWNER_RESPONSE, response);

        return genericResponseEntityUtil.ok(response, List.class, QuotationOwnerResponse.class);
    }

    @Override
    @ViravaSecured(resource = Resources.USER, scope = ScopeType.READ)
    public ResponseEntity<UserDetailsResponse> getOrCreateUserDetails() {
        var response = userService.getUserDetails();
        log.debug(GET_USER_DETAILS_RESPONSE, response.getUsername(), response);

        return genericResponseEntityUtil.ok(response, UserDetails.class, UserDetailsResponse.class);
    }
}