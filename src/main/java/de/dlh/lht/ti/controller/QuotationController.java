package de.dlh.lht.ti.controller;

import com.cleverpine.cpspringerrorutil.util.GenericResponseEntityUtil;
import com.cleverpine.viravaspringhelper.dto.ScopeType;
import de.dlh.lht.ti.api.QuotationApi;
import de.dlh.lht.ti.auth.ViravaSecured;
import de.dlh.lht.ti.auth.roles.Resources;
import de.dlh.lht.ti.mapper.QuotationControllerMapper;
import de.dlh.lht.ti.model.BeginQuotationRequest;
import de.dlh.lht.ti.model.QuotationDetails;
import de.dlh.lht.ti.model.QuotationDetailsResponse;
import de.dlh.lht.ti.model.QuotationsPage;
import de.dlh.lht.ti.model.QuotationsPageResponse;
import de.dlh.lht.ti.model.QuotationsQueryParameters;
import de.dlh.lht.ti.service.contract.QuotationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;


import static de.dlh.lht.ti.utils.LoggingMessages.BEGIN_QUOTATION_REQUEST;
import static de.dlh.lht.ti.utils.LoggingMessages.BEGIN_QUOTATION_RESPONSE;
import static de.dlh.lht.ti.utils.LoggingMessages.GET_QUOTATIONS_REQUEST;
import static de.dlh.lht.ti.utils.LoggingMessages.GET_QUOTATIONS_RESPONSE;
import static de.dlh.lht.ti.utils.LoggingMessages.GET_QUOTATION_DETAILS_REQUEST;
import static de.dlh.lht.ti.utils.LoggingMessages.GET_QUOTATION_DETAILS_RESPONSE;

@Log4j2
@RestController
@RequiredArgsConstructor
public class QuotationController implements QuotationApi {
    private final QuotationControllerMapper quotationControllerMapper;

    private final QuotationService quotationService;

    private final GenericResponseEntityUtil genericResponseEntityUtil;

    @Override
    @ViravaSecured(resource = Resources.QUOTATION, scope = ScopeType.READ)
    public ResponseEntity<QuotationsPageResponse> getQuotations(QuotationsQueryParameters quotationsQueryParameters) {
        log.debug(GET_QUOTATIONS_REQUEST, quotationsQueryParameters);
        var quotationsQueryParametersDto =
                quotationControllerMapper.quotationsQueryParametersToQuotationsQueryParametersDto(quotationsQueryParameters);
        var response = quotationService.getQuotations(quotationsQueryParametersDto);
        log.debug(GET_QUOTATIONS_RESPONSE, response);

        return genericResponseEntityUtil.ok(response, QuotationsPage.class, QuotationsPageResponse.class);
    }

    @Override
    @ViravaSecured(resource = Resources.QUOTATION, scope = ScopeType.READ)
    public ResponseEntity<QuotationDetailsResponse> getQuotationDetails(Long quotationId) {
        log.debug(GET_QUOTATION_DETAILS_REQUEST, quotationId);
        var response = quotationService.getQuotationDetails(quotationId);
        log.debug(GET_QUOTATION_DETAILS_RESPONSE, response);

        return genericResponseEntityUtil.ok(response, QuotationDetails.class, QuotationDetailsResponse.class);
    }

    @Override
    @ViravaSecured(resource = Resources.QUOTATION, scope = ScopeType.UPDATE)
    public ResponseEntity<QuotationDetailsResponse> beginQuotation(Long quotationId, BeginQuotationRequest beginQuotationRequest) {
        log.debug(BEGIN_QUOTATION_REQUEST, beginQuotationRequest);
        var contractTypesDto = quotationControllerMapper.beginQuotationRequestToContractTypesDto(beginQuotationRequest);
        var response = quotationService.beginQuotation(quotationId, contractTypesDto);
        log.debug(BEGIN_QUOTATION_RESPONSE, response);

        return genericResponseEntityUtil.ok(response, QuotationDetails.class, QuotationDetailsResponse.class);
    }
}
