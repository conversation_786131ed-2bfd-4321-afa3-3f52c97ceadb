package de.dlh.lht.ti.controller;

import com.cleverpine.cpspringerrorutil.util.GenericResponseEntityUtil;
import com.cleverpine.viravaspringhelper.dto.ScopeType;
import de.dlh.lht.ti.api.WorkscopeSummaryApi;
import de.dlh.lht.ti.auth.ViravaSecured;
import de.dlh.lht.ti.auth.roles.Resources;
import de.dlh.lht.ti.model.WorkscopeSummaryResponse;
import de.dlh.lht.ti.service.contract.WorkscopeSummaryService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;


import static de.dlh.lht.ti.utils.LoggingMessages.GET_WORKSCOPE_SUMMARY_REQUEST;
import static de.dlh.lht.ti.utils.LoggingMessages.GET_WORKSCOPE_SUMMARY_RESPONSE;

@Log4j2
@RestController
@RequiredArgsConstructor
public class WorkscopeSummaryController implements WorkscopeSummaryApi {

    private final WorkscopeSummaryService workscopeSummaryService;

    private final GenericResponseEntityUtil genericResponseEntityUtil;

    @Override
    @ViravaSecured(resource = Resources.QUOTATION, scope = ScopeType.READ)
    public ResponseEntity<WorkscopeSummaryResponse> getWorkscopeSummary(Long quotationId) {
        log.debug(GET_WORKSCOPE_SUMMARY_REQUEST, quotationId);
        var response = workscopeSummaryService.getWorkscopeSummaries(quotationId);
        log.debug(GET_WORKSCOPE_SUMMARY_RESPONSE, quotationId, response);

        return genericResponseEntityUtil.ok(response, List.class, WorkscopeSummaryResponse.class);
    }
}
