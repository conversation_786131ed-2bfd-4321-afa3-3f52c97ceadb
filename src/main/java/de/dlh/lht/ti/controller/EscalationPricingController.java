package de.dlh.lht.ti.controller;

import com.cleverpine.cpspringerrorutil.util.GenericResponseEntityUtil;
import com.cleverpine.viravaspringhelper.dto.ScopeType;
import de.dlh.lht.ti.api.EscalationPricingApi;
import de.dlh.lht.ti.auth.ViravaSecured;
import de.dlh.lht.ti.auth.roles.Resources;
import de.dlh.lht.ti.model.EscalationPricingRequest;
import de.dlh.lht.ti.model.EscalationsPricing;
import de.dlh.lht.ti.model.EscalationsPricingResponse;
import de.dlh.lht.ti.service.contract.EscalationPricingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;


import static de.dlh.lht.ti.utils.LoggingMessages.GET_ESCALATION_PRICING_REQUEST;
import static de.dlh.lht.ti.utils.LoggingMessages.GET_ESCALATION_PRICING_RESPONSE;
import static de.dlh.lht.ti.utils.LoggingMessages.SAVE_ESCALATION_PRICING_REQUEST;
import static de.dlh.lht.ti.utils.LoggingMessages.SAVE_ESCALATION_PRICING_RESPONSE;

@Log4j2
@RestController
@RequiredArgsConstructor
public class EscalationPricingController implements EscalationPricingApi {

    private final EscalationPricingService escalationPricingService;

    private final GenericResponseEntityUtil genericResponseEntityUtil;

    @Override
    @ViravaSecured(resource = Resources.QUOTATION, scope = ScopeType.READ)
    public ResponseEntity<EscalationsPricingResponse> getEscalationPricing(Long quotationId) {
        log.debug(GET_ESCALATION_PRICING_REQUEST, quotationId);
        var response = escalationPricingService.getEscalationPricing(quotationId);
        log.debug(GET_ESCALATION_PRICING_RESPONSE, quotationId, response);

        return genericResponseEntityUtil.ok(response, EscalationsPricing.class, EscalationsPricingResponse.class);
    }

    @Override
    @ViravaSecured(resource = Resources.QUOTATION, scope = ScopeType.UPDATE)
    public ResponseEntity<EscalationsPricingResponse> saveEscalationPricing(Long quotationId, EscalationPricingRequest body) {
        log.debug(SAVE_ESCALATION_PRICING_REQUEST, quotationId, body);
        var response = escalationPricingService.saveEscalationPricing(quotationId, body);
        log.debug(SAVE_ESCALATION_PRICING_RESPONSE, quotationId, response);

        return genericResponseEntityUtil.ok(response, EscalationsPricing.class, EscalationsPricingResponse.class);
    }
}
