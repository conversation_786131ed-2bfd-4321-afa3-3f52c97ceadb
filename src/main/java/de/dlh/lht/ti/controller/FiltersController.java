package de.dlh.lht.ti.controller;

import com.cleverpine.cpspringerrorutil.util.GenericResponseEntityUtil;
import com.cleverpine.viravaspringhelper.dto.ScopeType;
import de.dlh.lht.ti.api.FiltersApi;
import de.dlh.lht.ti.auth.ViravaSecured;
import de.dlh.lht.ti.auth.roles.Resources;
import de.dlh.lht.ti.model.QuotationFilters;
import de.dlh.lht.ti.model.QuotationFiltersResponse;
import de.dlh.lht.ti.service.contract.FiltersService;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;


import static de.dlh.lht.ti.utils.LoggingMessages.GET_QUOTATION_FILTERS_REQUEST;
import static de.dlh.lht.ti.utils.LoggingMessages.GET_QUOTATION_FILTERS_RESPONSE;

@Log4j2
@RestController
@RequiredArgsConstructor
public class FiltersController implements FiltersApi {

    private final FiltersService filtersService;

    private final GenericResponseEntityUtil genericResponseEntityUtil;

    @Override
    @ViravaSecured(resource = Resources.FILTERS, scope = ScopeType.READ)
    public ResponseEntity<QuotationFiltersResponse> getQuotationFilters() {
        log.debug(GET_QUOTATION_FILTERS_REQUEST);
        var response = filtersService.getQuotationFilters();

        log.debug(GET_QUOTATION_FILTERS_RESPONSE, response);
        return genericResponseEntityUtil.ok(response, QuotationFilters.class, QuotationFiltersResponse.class);
    }
}
