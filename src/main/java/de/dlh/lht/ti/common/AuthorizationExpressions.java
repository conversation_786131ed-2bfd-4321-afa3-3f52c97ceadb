package de.dlh.lht.ti.common;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class AuthorizationExpressions {
  public static final String IS_AUTHENTICATED = "hasAuthority('AUTHENTICATED')";
  public static final String IS_USER = "hasAuthority('APP_SAMPLE_USER')";
  public static final String IS_ADMIN = "hasAuthority('APP_SAMPLE_ADMIN')";
}
