package de.dlh.lht.ti.dto;

import de.dlh.lht.ti.dto.api.ClusterDto;
import de.dlh.lht.ti.dto.api.PartDto;
import de.dlh.lht.ti.entity.HandlingChargeEntity;
import de.dlh.lht.ti.enums.PartType;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public class HandlingChargeDto {

    private HandlingChargeEntity handlingCharge;
    private ClusterDto cluster;
    private PartDto part;
    private Integer quantity;

    public HandlingChargeDto(HandlingChargeEntity handlingCharge, Long clusterId, String clusterName, Integer clusterOrder,
            Long partId, String partName, Integer partOrder, PartType partType, Integer quantity) {
        this.handlingCharge = handlingCharge;
        this.cluster = new ClusterDto(clusterId, clusterName, clusterOrder);
        this.part = new PartDto(partId, partName, partOrder, partType);
        this.quantity = partType.equals(PartType.A_PART)
                ||  partType.equals(PartType.LLP)
                || partType.equals(PartType.CASE_AND_FRAME) ? quantity : -1;
    }
}
