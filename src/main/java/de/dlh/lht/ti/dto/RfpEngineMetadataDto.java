package de.dlh.lht.ti.dto;

import de.dlh.lht.ti.enums.Currency;
import java.math.BigDecimal;
import lombok.Getter;

@Getter
public class RfpEngineMetadataDto extends TaskMetadataDto {

    private Long labourPricingItemId;
    private String clusterName;
    private String taskName;

    public RfpEngineMetadataDto(
            Long labourPricingItemId,
            String clusterName,
            String taskName,
            BigDecimal manHours,
            Integer quantity,
            BigDecimal weightedQuantity,
            BigDecimal value,
            Currency currency,
            Boolean isCleaningAndInspection) {
        super(manHours, quantity, weightedQuantity, value, currency, isCleaningAndInspection);
        this.labourPricingItemId = labourPricingItemId;
        this.clusterName = clusterName;
        this.taskName = taskName;
    }

    public RfpEngineMetadataDto() {
        super();
    }
}