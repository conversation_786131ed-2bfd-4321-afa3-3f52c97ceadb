package de.dlh.lht.ti.dto.summary;

import de.dlh.lht.ti.enums.Currency;
import de.dlh.lht.ti.enums.NonRoutineMaterialSubType;
import de.dlh.lht.ti.enums.PartType;
import java.math.BigDecimal;
import lombok.Getter;

@Getter
public class MaterialProductionCostDto {

    private Long workScopeId;
    private String year;
    private PartType partType;
    private NonRoutineMaterialSubType nonRoutineMaterialSubType;
    private BigDecimal z1Value;
    private Currency currency;
    private BigDecimal z1WeightedQuantity;
    private int quantity;

    public MaterialProductionCostDto(
            Long workScopeId,
            String year,
            PartType partType,
            NonRoutineMaterialSubType nonRoutineMaterialSubType,
            BigDecimal z1Value,
            Currency currency,
            BigDecimal z1WeightedQuantity,
            int quantity) {
        this.workScopeId = workScopeId;
        this.year = year;
        this.partType = partType;
        this.nonRoutineMaterialSubType = nonRoutineMaterialSubType;
        this.z1Value = z1Value;
        this.currency = currency;
        this.z1WeightedQuantity = z1WeightedQuantity;
        this.quantity = quantity;
    }

}