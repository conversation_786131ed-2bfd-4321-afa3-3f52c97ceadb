package de.dlh.lht.ti.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class EscalationPricingDto {

    private Long id;

    private String year;

    private Boolean isRfpContractSelected;

    private BigDecimal labourPrices;

    private BigDecimal labourPricesDefault;

    private BigDecimal eparPrices;

    private BigDecimal eparPricesDefault;

    private BigDecimal rfpLabour;

    private BigDecimal rfpLabourDefault;

    private BigDecimal hcMaterialPrices;

    private BigDecimal hcMaterialPricesDefault;

    private BigDecimal hcSubcontractPrices;

    private BigDecimal hcSubcontractPricesDefault;

    public EscalationPricingDto(Long id, String year, BigDecimal labourPrices, BigDecimal eparPrices, BigDecimal rfpLabour, BigDecimal hcMaterialPrices, BigDecimal hcSubcontractPrices, BigDecimal hcMaterialPricesDefault, Boolean isRfpContractSelected) {
        this.id = id;
        this.year = year;
        this.labourPrices = labourPrices;
        this.eparPrices = eparPrices;
        this.rfpLabour = rfpLabour;
        this.hcMaterialPrices = hcMaterialPrices;
        this.hcSubcontractPrices = hcSubcontractPrices;
        this.hcMaterialPricesDefault = hcMaterialPricesDefault;
        this.isRfpContractSelected = isRfpContractSelected;
    }
}
