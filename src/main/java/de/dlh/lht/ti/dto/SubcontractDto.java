package de.dlh.lht.ti.dto;

import de.dlh.lht.ti.dto.api.ClusterDto;
import java.math.BigDecimal;
import lombok.Getter;

@Getter
public class SubcontractDto {

    private final Long id;
    private final BigDecimal margin;
    private final Integer cap;
    private final ClusterDto cluster;

    public SubcontractDto(Long id, BigDecimal margin, Integer cap, Long clusterId, String clusterName, Integer clusterOrder) {
        this.id = id;
        this.margin = margin;
        this.cap = cap;
        this.cluster = new ClusterDto(clusterId, clusterName, clusterOrder);
    }
}
