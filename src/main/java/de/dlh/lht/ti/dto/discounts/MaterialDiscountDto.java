package de.dlh.lht.ti.dto.discounts;

import de.dlh.lht.ti.enums.Currency;
import de.dlh.lht.ti.enums.NonRoutineMaterialSubType;
import de.dlh.lht.ti.enums.PartType;
import de.dlh.lht.ti.enums.WorkscopeClass;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Getter;


import static de.dlh.lht.ti.utils.Constants.HUNDRED;
import static de.dlh.lht.ti.utils.Constants.MATH_CONTEXT;

@Getter
@AllArgsConstructor
public class MaterialDiscountDto {

    private DiscountWorkscopeDto workscopeDto;
    private String year;
    private String name;
    private PartType partType;
    private NonRoutineMaterialSubType nonRoutineMaterialSubType;
    private BigDecimal productionCost;
    private Currency z1Currency;

    public MaterialDiscountDto(
            Long workscopeId,
            WorkscopeClass workscopeClass,
            String year,
            String name,
            int quantity,
            PartType partType,
            NonRoutineMaterialSubType nonRoutineMaterialSubType,
            BigDecimal z1Value,
            BigDecimal z1WeightedQuantity,
            Currency z1Currency
    ) {
        this.workscopeDto = new DiscountWorkscopeDto(workscopeId, workscopeClass);
        this.year = year;
        this.name = name;
        this.partType = partType;
        this.nonRoutineMaterialSubType = nonRoutineMaterialSubType;
        this.z1Currency = z1Currency;
        this.productionCost = z1Value
                .multiply(BigDecimal.valueOf(quantity), MATH_CONTEXT)
                .multiply(z1WeightedQuantity.divide(HUNDRED, MATH_CONTEXT), MATH_CONTEXT);
    }
}