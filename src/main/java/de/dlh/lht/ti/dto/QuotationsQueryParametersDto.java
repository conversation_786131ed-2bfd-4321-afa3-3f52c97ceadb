package de.dlh.lht.ti.dto;

import de.dlh.lht.ti.enums.QuotationSort;
import de.dlh.lht.ti.enums.QuotationStatus;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class QuotationsQueryParametersDto {

    private Integer pageSize;

    private Integer pageIndex;

    private QuotationSort sortBy;

    private Long ownerId;

    private String offerNumber;

    private Integer version;

    private Integer position;

    private Integer scenario;

    private QuotationStatus quotationStatus;

    private String engineType;

    private String customer;
}
