package de.dlh.lht.ti.dto;

import java.util.HashSet;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class HandlingChargeValuesSetsDto {

    private Set<Float> baseCase = new HashSet<>();

    private Set<Float> z1 = new HashSet<>();

    private Set<Float> z2 = new HashSet<>();

    private Set<Float> pma = new HashSet<>();

    private Set<Float> csm = new HashSet<>();

    private Set<Integer> oneItemCap = new HashSet<>();

    private Set<Integer> lineItemCap = new HashSet<>();
}
