package de.dlh.lht.ti.dto;

import de.dlh.lht.ti.dto.api.ClusterDto;
import de.dlh.lht.ti.dto.api.PartDto;
import de.dlh.lht.ti.enums.Currency;
import de.dlh.lht.ti.enums.PartType;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class Z2RatingDto {

    private Long id;
    private ClusterDto cluster;
    private PartDto part;
    private Long partMetadataId;
    private String year;
    private BigDecimal oemZ1Price;
    private Currency oemZ1PriceCurrency;
    private BigDecimal z2Cost;
    private int quantity;
    private Float z2Rating;

    public Z2RatingDto(Long id, Long clusterId, String clusterName, int clusterOrder,
            Long partId, String partName, int partOrder, PartType partType, Long partMetadataId, String year, BigDecimal oemZ1Price,
            Currency oemZ1PriceCurrency, BigDecimal z2Cost, int quantity, Float z2Rating) {
        this.id = id;
        this.cluster = new ClusterDto(clusterId, clusterName, clusterOrder);
        this.part = new PartDto(partId, partName, partOrder, partType);
        this.partMetadataId = partMetadataId;
        this.year = year;
        this.oemZ1Price = oemZ1Price;
        this.oemZ1PriceCurrency = oemZ1PriceCurrency;
        this.z2Cost = z2Cost;
        this.quantity = partType.equals(PartType.A_PART)
                ||  partType.equals(PartType.LLP)
                || partType.equals(PartType.CASE_AND_FRAME) ? quantity : -1;
        this.z2Rating = z2Rating;
    }
}
