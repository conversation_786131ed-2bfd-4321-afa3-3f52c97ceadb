package de.dlh.lht.ti.dto;

import de.dlh.lht.ti.enums.Currency;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public class ProductionCostDto {

    private final WorkscopeDto workscope;
    private final MetadataDto metadata;

    public ProductionCostDto(
            Long workscopeId,
            String year,
            Integer quantity,
            BigDecimal value,
            BigDecimal weightedQuantity,
            Currency currency) {
        this.workscope = new WorkscopeDto(workscopeId, year);
        this.metadata = new MetadataDto(quantity, value, weightedQuantity, currency);
    }
}
