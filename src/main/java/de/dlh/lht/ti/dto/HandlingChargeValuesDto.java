package de.dlh.lht.ti.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class HandlingChargeValuesDto {

    private Float baseCase;

    private Float z1;

    private Float z2;

    private Float pma;

    private Float csm;

    private Integer oneItemCap;

    private Integer lineItemCap;
}
