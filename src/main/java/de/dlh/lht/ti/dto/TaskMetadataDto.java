package de.dlh.lht.ti.dto;

import de.dlh.lht.ti.enums.Currency;
import java.math.BigDecimal;
import lombok.Getter;

@Getter
public class TaskMetadataDto extends MetadataDto {

    private BigDecimal manHours;
    private Boolean isCleaningAndInspection;

    public TaskMetadataDto(
            BigDecimal manHours,
            Integer quantity,
            BigDecimal weightedQuantity,
            BigDecimal value,
            Currency currency,
            Boolean isCleaningAndInspection) {
        super(quantity, weightedQuantity, value, currency);
        this.manHours = manHours;
        this.isCleaningAndInspection = isCleaningAndInspection;
    }

    public TaskMetadataDto() {
        super();
    }
}
