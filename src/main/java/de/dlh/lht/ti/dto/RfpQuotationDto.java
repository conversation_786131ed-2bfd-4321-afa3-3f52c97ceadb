package de.dlh.lht.ti.dto;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public class RfpQuotationDto {

    private Long engineId;
    private String engineVersion;
    private Boolean isRfpEngineCiIncluded;
    private Boolean isRfpModuleCiIncluded;
    private ZonedDateTime contactStart;
    private BigDecimal usdExchangeRate;
}