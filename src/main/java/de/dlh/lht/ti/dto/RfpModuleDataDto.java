package de.dlh.lht.ti.dto;

import de.dlh.lht.ti.dto.api.ClusterDto;
import de.dlh.lht.ti.enums.Currency;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@AllArgsConstructor
@NoArgsConstructor
public class RfpModuleDataDto {

    private RfpModuleDto rfpModuleDto;
    private TaskMetadataDto task;
    private RfpWorkscopeDto workscope;

    public RfpModuleDataDto(
            Long id,
            BigDecimal price,
            Long clusterId,
            String clusterName,
            Long workscopeId,
            String workscopeName,
            BigDecimal manHours,
            Integer quantity,
            BigDecimal weightedQuantity,
            BigDecimal value,
            Currency currency,
            Boolean isCleaningAndInspection) {
        var cluster = new ClusterDto(clusterId, clusterName, null);
        this.rfpModuleDto = new RfpModuleDto(id, price, cluster);
        this.task = new TaskMetadataDto(manHours, quantity, weightedQuantity, value, currency, isCleaningAndInspection);
        this.workscope = new RfpWorkscopeDto(workscopeId, workscopeName);
    }
}
