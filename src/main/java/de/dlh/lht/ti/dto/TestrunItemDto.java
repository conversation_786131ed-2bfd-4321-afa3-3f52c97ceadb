package de.dlh.lht.ti.dto;

import de.dlh.lht.ti.dto.api.ClusterDto;
import de.dlh.lht.ti.enums.Currency;
import java.math.BigDecimal;
import lombok.Getter;

@Getter
public class TestrunItemDto {

    private final Long id;
    private final BigDecimal price;
    private final ClusterDto cluster;
    private final MetadataDto metadata;

    public TestrunItemDto(
            Long id,
            BigDecimal price,
            Long clusterId,
            String clusterName,
            int sortOrder,
            Integer quantity,
            BigDecimal weightedQuantity,
            BigDecimal value,
            Currency currency
    ) {
        this.id = id;
        this.price = price;
        this.cluster = new ClusterDto(clusterId, clusterName, sortOrder);
        this.metadata = new MetadataDto(quantity, weightedQuantity, value, currency);
    }
}
