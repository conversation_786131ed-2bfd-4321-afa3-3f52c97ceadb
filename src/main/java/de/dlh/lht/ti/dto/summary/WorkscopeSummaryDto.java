package de.dlh.lht.ti.dto.summary;

import java.math.BigDecimal;
import lombok.Getter;

@Getter
public class WorkscopeSummaryDto {

    private final SummaryWorkscopeDto workscope;
    private final String year;
    private final BigDecimal revenueUsd;
    private final BigDecimal revenueEur;
    private final BigDecimal productionCostUsd;
    private final BigDecimal productionCostEur;
    private final BigDecimal discountUsd;
    private final BigDecimal discountEur;
    private final BigDecimal surchargesCostUsd;
    private final BigDecimal surchargesCostEur;

    public WorkscopeSummaryDto(Long workscopeId,
            String name,
            String year,
            BigDecimal revenueUsd,
            BigDecimal revenueEur,
            BigDecimal productionCostUsd,
            BigDecimal productionCostEur,
            BigDecimal discountUsd,
            BigDecimal discountEur,
            BigDecimal surchargesCostUsd,
            BigDecimal surchargesCostEur) {
        this.workscope = new SummaryWorkscopeDto(workscopeId, name);
        this.year = year;
        this.revenueUsd = revenueUsd;
        this.revenueEur = revenueEur;
        this.productionCostUsd = productionCostUsd;
        this.productionCostEur = productionCostEur;
        this.discountUsd = discountUsd;
        this.discountEur = discountEur;
        this.surchargesCostUsd = surchargesCostUsd;
        this.surchargesCostEur = surchargesCostEur;
    }
}
