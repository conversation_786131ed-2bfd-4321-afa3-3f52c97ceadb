package de.dlh.lht.ti.dto;

import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


import static java.math.BigDecimal.ZERO;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CalculationResultDto {

    private BigDecimal valueUsd = ZERO;
    private BigDecimal valueEur = ZERO;
}
