package de.dlh.lht.ti.dto;

import de.dlh.lht.ti.dto.api.ClusterDto;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public class RfpEngineItemDto {
    private Long labourPricingItemId;
    private ClusterDto cluster;
    private BigDecimal rfpEngineBaseCost;
    private BigDecimal rfpEngineCiHoursCost;
    private BigDecimal rfpEnginePrice;

    public RfpEngineItemDto(
            Long labourPricingItemId,
            Long clusterId,
            String clusterName,
            Integer order,
            BigDecimal rfpEngineBaseCost,
            BigDecimal rfpEngineCiHoursCost,
            BigDecimal rfpEnginePrice) {
        this.labourPricingItemId = labourPricingItemId;
        this.cluster = new ClusterDto(clusterId, clusterName, order);
        this.rfpEngineBaseCost = rfpEngineBaseCost;
        this.rfpEngineCiHoursCost = rfpEngineCiHoursCost;
        this.rfpEnginePrice = rfpEnginePrice;
    }
}