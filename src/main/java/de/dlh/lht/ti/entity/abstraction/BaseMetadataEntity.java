package de.dlh.lht.ti.entity.abstraction;

import jakarta.persistence.Column;
import jakarta.persistence.Inheritance;
import jakarta.persistence.InheritanceType;
import jakarta.persistence.MappedSuperclass;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@MappedSuperclass
@Inheritance(strategy = InheritanceType.TABLE_PER_CLASS)
public abstract class BaseMetadataEntity extends BaseEntity {

    @Column(name = "workscope_id")
    private Long workscopeId;

    private String name;

    private int quantity;

    private String year;
}