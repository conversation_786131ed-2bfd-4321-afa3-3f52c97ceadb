package de.dlh.lht.ti.entity;

import de.dlh.lht.ti.entity.abstraction.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.*;

import java.math.BigDecimal;

@SuppressWarnings("JpaDataSourceORMInspection")
@Entity
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "labour_rates")
public class LabourRateEntity extends BaseEntity {

    @Column(name = "quotation_id")
    private Long quotationId;

    @Column(name = "routine_labour_rate")
    private Integer routineLabourRate;

    @Column(name = "non_routine_labour_rate")
    private Integer nonRoutineLabourRate;

    @Column(name = "epar_discount")
    private BigDecimal eparDiscount;
}
