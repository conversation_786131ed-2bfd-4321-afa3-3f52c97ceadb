package de.dlh.lht.ti.entity;

import de.dlh.lht.ti.entity.abstraction.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@SuppressWarnings("JpaDataSourceORMInspection")
@Entity
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "workscope_summaries")
public class WorkscopeSummaryEntity extends BaseEntity {

    @Column(name = "revenue_usd")
    private BigDecimal revenueUsd;

    @Column(name = "revenue_eur")
    private BigDecimal revenueEur;

    @Column(name = "production_cost_usd")
    private BigDecimal productionCostUsd;

    @Column(name = "production_cost_eur")
    private BigDecimal productionCostEur;

    @Column(name = "discount_usd")
    private BigDecimal discountUsd;

    @Column(name = "discount_eur")
    private BigDecimal discountEur;

    @Column(name = "surcharges_cost_usd")
    private BigDecimal surchargesCostUsd;

    @Column(name = "surcharges_cost_eur")
    private BigDecimal surchargesCostEur;

    private String year;

    @Column(name = "quotation_id")
    private Long quotationId;

    @Column(name = "workscope_id")
    private Long workscopeId;
}
