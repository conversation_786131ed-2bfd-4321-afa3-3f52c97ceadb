package de.dlh.lht.ti.entity;

import de.dlh.lht.ti.entity.abstraction.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@SuppressWarnings("JpaDataSourceORMInspection")
@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "material_discounts_expendables")
public class MaterialDiscountExpendableEntity extends BaseEntity {

    @Column(name = "engine_id")
    private Long engineId;

    private String year;

    @Column(name = "level_zero")
    private BigDecimal levelZero;

    @Column(name = "level_one")
    private BigDecimal levelOne;

    @Column(name = "level_two")
    private BigDecimal levelTwo;
}