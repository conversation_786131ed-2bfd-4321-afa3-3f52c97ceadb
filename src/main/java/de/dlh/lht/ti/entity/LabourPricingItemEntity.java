package de.dlh.lht.ti.entity;

import de.dlh.lht.ti.entity.abstraction.BaseEntity;
import de.dlh.lht.ti.enums.TaskType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@SuppressWarnings("JpaDataSourceORMInspection")
@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "labour_pricing_items")
public class LabourPricingItemEntity extends BaseEntity {

    @Column(name = "quotation_engine_id")
    private Long quotationEngineId;

    @Column(name = "cluster_id")
    private Long clusterId;

    @Enumerated(EnumType.STRING)
    private TaskType type;
}
