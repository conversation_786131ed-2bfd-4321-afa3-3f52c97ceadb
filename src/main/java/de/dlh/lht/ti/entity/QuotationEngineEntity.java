package de.dlh.lht.ti.entity;

import de.dlh.lht.ti.entity.abstraction.BaseEntity;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@SuppressWarnings("JpaDataSourceORMInspection")
@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "quotation_engines")
public class QuotationEngineEntity extends BaseEntity {

    @OneToOne(cascade = CascadeType.MERGE)
    @JoinColumn(name = "quotation_id")
    private QuotationEntity quotation;

    @ManyToOne(cascade = CascadeType.MERGE)
    @JoinColumn(name = "engine_id")
    private EngineEntity engine;

    @OneToMany(fetch = FetchType.EAGER)
    @JoinTable(
            name = "quotation_engine_workscopes",
            joinColumns = @JoinColumn(name = "quotation_engine_id"),
            inverseJoinColumns = @JoinColumn(name = "workscope_id"))
    private List<WorkscopeEntity> workscopes;
}
