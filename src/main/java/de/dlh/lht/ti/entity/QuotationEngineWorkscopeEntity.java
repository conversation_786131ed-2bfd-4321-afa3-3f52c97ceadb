package de.dlh.lht.ti.entity;

import de.dlh.lht.ti.entity.abstraction.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@SuppressWarnings("JpaDataSourceORMInspection")
@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "quotation_engine_workscopes")
public class QuotationEngineWorkscopeEntity extends BaseEntity {

    @Column(name = "quotation_engine_id")
    private Long quotationEngineId;

    @Column(name = "workscope_id")
    private Long workscopeId;
}
