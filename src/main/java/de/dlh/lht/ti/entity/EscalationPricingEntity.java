package de.dlh.lht.ti.entity;

import de.dlh.lht.ti.entity.abstraction.BaseEntity;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.Column;
import lombok.*;

import java.math.BigDecimal;

@SuppressWarnings("JpaDataSourceORMInspection")
@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "escalations")
public class EscalationPricingEntity extends BaseEntity {

    @Column(name = "quotation_id", nullable = false)
    private Long quotationId;

    @Column(name = "year", nullable = false)
    private String year;

    @Column(name = "labour_prices", nullable = false)
    private BigDecimal labourPrices;

    @Column(name = "epar_prices", nullable = false)
    private BigDecimal eparPrices;

    @Column(name = "rfp_labour", nullable = true)
    private BigDecimal rfpLabour;

    @Column(name = "hc_material_prices", nullable = false)
    private BigDecimal hcMaterialPrices;

    @Column(name = "hc_subcontract_prices", nullable = false)
    private BigDecimal hcSubcontractPrices;

}
