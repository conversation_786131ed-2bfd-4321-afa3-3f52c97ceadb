package de.dlh.lht.ti.entity;

import de.dlh.lht.ti.entity.abstraction.BaseEntity;
import de.dlh.lht.ti.enums.DiscountLabourType;
import de.dlh.lht.ti.enums.DiscountPartType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@SuppressWarnings("JpaDataSourceORMInspection")
@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "material_discounts")
public class MaterialDiscountTypeEntity extends BaseEntity {

    @Column(name = "engine_id")
    private Long engineId;

    @Enumerated(EnumType.STRING)
    @Column(name = "labour_type")
    private DiscountLabourType discountLabourType;

    @Enumerated(EnumType.STRING)
    @Column(name = "part_type")
    private DiscountPartType discountPartType;

    @Column(name = "expendable_portion")
    private BigDecimal expendablePortion;

    @Column(name = "non_expendable_non_llp_portion")
    private BigDecimal nonExpendableNonLlpPortion;

    @Column(name = "llp_portion")
    private BigDecimal llpPortion;
}