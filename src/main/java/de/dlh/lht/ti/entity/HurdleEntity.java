package de.dlh.lht.ti.entity;

import de.dlh.lht.ti.entity.abstraction.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@SuppressWarnings("JpaDataSourceORMInspection")
@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "hurdles")
public class HurdleEntity extends BaseEntity {

    private String year;

    @Column(name = "hurdle_ens")
    private BigDecimal hurdleEns;

    @Column(name = "hurdle_mes")
    private BigDecimal hurdleMes;
}
