package de.dlh.lht.ti.entity;

import de.dlh.lht.ti.entity.abstraction.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@SuppressWarnings("JpaDataSourceORMInspection")
@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "volume_based_material_discounts")
public class VolumeBasedDiscountEntity extends BaseEntity {

    @Column(name = "engine_id")
    private Long engineId;

    private String year;

    @Column(name = "target_material_cost")
    private BigDecimal targetMaterialCost;

    @Column(name = "minimum_material_cost")
    private BigDecimal minimumMaterialCost;

    @Column(name = "maximum_volume_based_discount")
    private BigDecimal maximumVolumeBasedDiscount;
}