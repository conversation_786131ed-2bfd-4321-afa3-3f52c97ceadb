package de.dlh.lht.ti.entity;

import de.dlh.lht.ti.entity.abstraction.BaseMetadataEntity;
import de.dlh.lht.ti.enums.Currency;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@SuppressWarnings("JpaDataSourceORMInspection")
@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "task_metadata")
public class TaskMetadataEntity extends BaseMetadataEntity {

    @Column(name = "labour_pricing_item_id")
    private Long labourPricingItemId;

    @Column(name = "is_routine")
    private boolean isRoutine;

    @Column(name = "is_cleaning_and_inspection")
    private Boolean isCleaningAndInspection;

    @Column(name = "man_hours")
    private BigDecimal manHours;

    @Column(name = "weighted_quantity")
    private BigDecimal weightedQuantity;

    @Column(name = "is_epar_catalogue_part")
    private boolean isEparCataloguePart;

    @Column(name = "is_repair_task")
    private boolean isRepairTask;

    private BigDecimal value;

    @Enumerated(EnumType.STRING)
    private Currency currency;
}
