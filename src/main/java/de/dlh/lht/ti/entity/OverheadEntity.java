package de.dlh.lht.ti.entity;

import de.dlh.lht.ti.entity.abstraction.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@SuppressWarnings("JpaDataSourceORMInspection")
@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "overheads")
public class OverheadEntity extends BaseEntity {

    private String year;

    @Column(name = "db3_overhead_dlh")
    private BigDecimal db3OverheadDlh;

    @Column(name = "db3_overhead_int")
    private BigDecimal db3OverheadInt;

    @Column(name = "db3_overhead_ext")
    private BigDecimal db3OverheadExt;

    @Column(name = "db3_overhead_llp")
    private BigDecimal db3OverheadLlp;

    @Column(name = "ebit_overhead_dlh")
    private BigDecimal ebitOverheadDlh;

    @Column(name = "ebit_overhead_int")
    private BigDecimal ebitOverheadInt;

    @Column(name = "ebit_overhead_ext")
    private BigDecimal ebitOverheadExt;

    @Column(name = "ebit_overhead_llp")
    private BigDecimal ebitOverheadLlp;

    @Column(name = "ebit_overhead_ebit_sc")
    private BigDecimal ebitOverheadEbitSc;

    @Column(name = "ebit_overhead_d3_sc")
    private BigDecimal ebitOverheadD3Sc;
}
