package de.dlh.lht.ti.entity;

import de.dlh.lht.ti.entity.abstraction.BaseMetadataEntity;
import de.dlh.lht.ti.enums.Currency;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@SuppressWarnings("JpaDataSourceORMInspection")
@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "testrun_metadata")
public class TestrunMetadataEntity extends BaseMetadataEntity {

    @Column(name = "weighted_quantity")
    private BigDecimal weightedQuantity;

    private BigDecimal value;

    @Enumerated(EnumType.STRING)
    private Currency currency;

    @Column(name = "testrun_item_id")
    private Long testrunItemId;

    @Column(name = "workscope_id")
    private Long workscopeId;
}
