package de.dlh.lht.ti.entity;

import de.dlh.lht.ti.entity.abstraction.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@SuppressWarnings("JpaDataSourceORMInspection")
@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "material_discounts_conditions")
public class MaterialDiscountsConditionEntity extends BaseEntity {

    @Column(name = "engine_id")
    private Long engineId;

    private String year;

    @Column(name = "non_llp_tiered")
    private BigDecimal nonLlpTiered;

    @Column(name = "non_llp_escalated")
    private BigDecimal nonLlpEscalated;

    @Column(name = "llp_tiered")
    private BigDecimal llpTiered;

    @Column(name = "llp_escalated")
    private BigDecimal llpEscalated;
}