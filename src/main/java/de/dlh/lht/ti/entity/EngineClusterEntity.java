package de.dlh.lht.ti.entity;

import de.dlh.lht.ti.entity.abstraction.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@SuppressWarnings("JpaDataSourceORMInspection")
@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "engine_clusters")
public class EngineClusterEntity extends BaseEntity {

    @Column(name = "engine_id")
    private Long engineId;

    @Column(name = "cluster_id")
    private Long clusterId;

    @Column(name = "sort_order")
    private Integer sortOrder;
}
