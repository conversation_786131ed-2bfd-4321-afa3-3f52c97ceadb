package de.dlh.lht.ti.entity;

import de.dlh.lht.ti.entity.abstraction.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "quotation_parts")
@Entity
public class QuotationPartEntity extends BaseEntity {

    @Column(name = "quotation_id")
    private Long quotationId;

    @Column(name = "importer_part_id")
    private Long importerPartId;
}
