package de.dlh.lht.ti.entity;

import de.dlh.lht.ti.entity.abstraction.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@SuppressWarnings("JpaDataSourceORMInspection")
@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "rfp_engine_items")
public class RfpEngineItemEntity extends BaseEntity {

    @Column(name = "labour_pricing_item_id")
    private Long labourPricingItemId;

    @Column(name = "rfp_engine_base_cost")
    private BigDecimal rfpEngineBaseCost;

    @Column(name = "rfp_engine_ci_hours_cost")
    private BigDecimal rfpEngineCiHoursCost;

    @Column(name = "rfp_engine_price")
    private BigDecimal rfpEnginePrice;
}