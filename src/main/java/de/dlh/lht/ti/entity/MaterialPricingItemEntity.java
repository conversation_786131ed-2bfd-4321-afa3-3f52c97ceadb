package de.dlh.lht.ti.entity;

import de.dlh.lht.ti.entity.abstraction.BaseEntity;
import de.dlh.lht.ti.enums.NonRoutineMaterialSubType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@SuppressWarnings("JpaDataSourceORMInspection")
@Entity
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "material_pricing_items")
public class MaterialPricingItemEntity extends BaseEntity {

    @Column(name = "quotation_engine_id")
    private Long quotationEngineId;

    @Column(name = "cluster_id")
    private Long clusterId;

    @Column(name = "part_id")
    private Long partId;

    @Enumerated(EnumType.STRING)
    @Column(name = "non_routine_material_sub_type")
    private NonRoutineMaterialSubType nonRoutineMaterialSubType;
}
