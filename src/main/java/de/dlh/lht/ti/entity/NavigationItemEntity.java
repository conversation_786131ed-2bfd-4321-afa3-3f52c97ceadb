package de.dlh.lht.ti.entity;

import de.dlh.lht.ti.entity.abstraction.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.*;

@SuppressWarnings("JpaDataSourceORMInspection")
@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "navigation_items")
public class NavigationItemEntity extends BaseEntity {

    @Column(name = "is_valid")
    private Boolean isValid;

    @Column(name = "quotation_id")
    private Long quotationId;

    @ManyToOne
    @JoinColumn(name = "navigation_step_id")
    private NavigationStepEntity navigationStep;

    @ManyToOne
    @JoinColumn(name = "progress_step_id")
    private ProgressStepEntity progressStep;
}
