package de.dlh.lht.ti.entity;

import de.dlh.lht.ti.entity.abstraction.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@SuppressWarnings("JpaDataSourceORMInspection")
@Entity
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "handling_charges")
public class HandlingChargeEntity extends BaseEntity {

    @Column(name = "z1_margin")
    private Float z1Margin;

    @Column(name = "z2_margin")
    private Float z2Margin;

    @Column(name = "pma_margin")
    private Float pmaMargin;

    @Column(name = "csm_margin")
    private Float csmMargin;

    @Column(name = "one_item_cap")
    private Integer oneItemCap;

    @Column(name = "line_item_cap")
    private Integer lineItemCap;

    @Column(name = "material_pricing_item_id")
    private Long materialPricingItemId;
}
