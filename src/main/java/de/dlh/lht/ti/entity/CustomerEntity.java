package de.dlh.lht.ti.entity;

import de.dlh.lht.ti.entity.abstraction.BaseEntity;
import de.dlh.lht.ti.enums.CustomerType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@SuppressWarnings("JpaDataSourceORMInspection")
@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "customers")
public class CustomerEntity extends BaseEntity {

    @Column(name = "three_letter_code")
    private String threeLetterCode;

    @Enumerated(EnumType.STRING)
    private CustomerType type;
}
