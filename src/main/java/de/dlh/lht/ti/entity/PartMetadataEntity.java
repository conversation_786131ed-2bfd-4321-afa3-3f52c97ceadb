package de.dlh.lht.ti.entity;

import de.dlh.lht.ti.entity.abstraction.BaseMetadataEntity;
import de.dlh.lht.ti.enums.Currency;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@SuppressWarnings("JpaDataSourceORMInspection")
@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "part_metadata")
public class PartMetadataEntity extends BaseMetadataEntity {

    @Column(name = "z1_value")
    private BigDecimal z1Value;

    @Enumerated(EnumType.STRING)
    @Column(name = "z1_currency")
    private Currency z1Currency;

    @Column(name = "z1_weighted_quantity")
    private BigDecimal z1WeightedQuantity;

    @Column(name = "z2_value")
    private BigDecimal z2Value;

    @Enumerated(EnumType.STRING)
    @Column(name = "z2_currency")
    private Currency z2Currency;

    @Column(name = "z2_weighted_quantity")
    private BigDecimal z2WeightedQuantity;

    @Column(name = "pma_value")
    private BigDecimal pmaValue;

    @Enumerated(EnumType.STRING)
    @Column(name = "pma_currency")
    private Currency pmaCurrency;

    @Column(name = "pma_weighted_quantity")
    private BigDecimal pmaWeightedQuantity;

    @Column(name = "csm_value")
    private BigDecimal csmValue;

    @Enumerated(EnumType.STRING)
    @Column(name = "csm_currency")
    private Currency csmCurrency;

    @Column(name = "csm_weighted_quantity")
    private BigDecimal csmWeightedQuantity;

    @Column(name = "oem_z1_price")
    private BigDecimal oemZ1Price;

    @Enumerated(EnumType.STRING)
    @Column(name = "oem_z1_price_currency")
    private Currency oemZ1PriceCurrency;

    @Column(name = "recycling_quote")
    private BigDecimal recyclingQuote;

    @Column(name = "material_pricing_item_id")
    private Long materialPricingItemId;
}
