package de.dlh.lht.ti.entity;

import de.dlh.lht.ti.entity.abstraction.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@SuppressWarnings("JpaDataSourceORMInspection")
@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "surcharges")
public class SurchargesEntity extends BaseEntity {

    private String year;

    @Column(name = "labour_costs")
    private BigDecimal labourCosts;

    @Column(name = "ah_rate_v25_cfm56_cf6_leap")
    private BigDecimal ahRateV25Cfm56Cf6Leap;

    @Column(name = "ah_rate_v25_cfm56_cf6_leap_yoy")
    private BigDecimal ahRateV25Cfm56Cf6LeapYoy;

    @Column(name = "tec_surcharge_per_ws_a_inh_standard")
    private Integer tecSurchargePerWsAInhStandard;

    @Column(name = "tec_surcharge_per_ws_a_inh_leap_cfm_oem_offload")
    private Integer tecSurchargePerWsAInhLeapCfmOemOffload;

    @Column(name = "tec_surcharge_per_ws_a_sc_dlh_swr_pw1_ovh")
    private Integer tecSurchargePerWsAScDlhSwrPw1Ovh;

    @Column(name = "tec_surcharge_enl_per_month_per_lease_event")
    private Integer tecSurchargeEnlPerMonthPerLeaseEvent;

    @Column(name = "tec_surcharge_per_ws_b_fixed_part_inh_sc_oem_offloads")
    private Integer tecSurchargePerWsBFixedPartInhScOemOffloads;

    @Column(name = "tec_surcharge_per_ws_b_c_variable_part_inh_sc_oem_offloads")
    private BigDecimal tecSurchargePerWsBCVariablePartInhScOemOffloads;

    @Column(name = "teo21_22_surcharge_per_ws_a_b")
    private Integer teo2122SurchargePerWsAB;

    @Column(name = "teo21_22_surcharge_per_mes_hub_module_event_ham")
    private Integer teo2122SurchargePerMesHubModuleEventHam;

    @Column(name = "v25_classic_engine_epar_royalties_per_sle_ws_a")
    private Integer v25ClassicEngineEparRoyaltiesPerSleWsA;

    @Column(name = "v25_select_retrofit_engine_epar_royalties_per_sle_ws_a")
    private Integer v25SelectRetrofitEngineEparRoyaltiesPerSleWsA;

    @Column(name = "internal_logistic_ws_a")
    private Integer internalLogisticWsA;

    @Column(name = "internal_logistic_ws_b")
    private Integer internalLogisticWsB;

    @Column(name = "internal_logistic_ws_c")
    private Integer internalLogisticWsC;

    @Column(name = "loss_of_value")
    private BigDecimal lossOfValue;
}
