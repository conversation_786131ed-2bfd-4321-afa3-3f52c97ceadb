package de.dlh.lht.ti.entity;

import de.dlh.lht.ti.entity.abstraction.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@SuppressWarnings("JpaDataSourceORMInspection")
@Entity
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "testrun_items")
public class TestrunItemEntity extends BaseEntity {

    private BigDecimal price;

    @Column(name = "quotation_engine_id")
    private Long quotationEngineId;

    @Column(name = "cluster_id")
    private Long clusterId;

    @Column(name = "handling_charge_id")
    private Long handlingChargeId;
}
