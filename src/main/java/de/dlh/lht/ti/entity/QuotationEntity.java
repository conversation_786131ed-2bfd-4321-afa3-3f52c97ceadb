package de.dlh.lht.ti.entity;

import de.dlh.lht.ti.entity.abstraction.BaseEntity;
import de.dlh.lht.ti.enums.QuotationStatus;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import java.math.BigDecimal;
import java.time.ZonedDateTime;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@SuppressWarnings("JpaDataSourceORMInspection")
@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "quotations")
public class QuotationEntity extends BaseEntity {

    @Column(name = "contract_start")
    private ZonedDateTime contractStart;

    @Column(name = "contract_end")
    private ZonedDateTime contractEnd;

    private int position;

    private int version;

    private int scenario;

    @Column(name = "engine_version")
    private String engineVersion;

    @Column(name = "usd_exchange_rate")
    private BigDecimal usdExchangeRate;

    private int status = QuotationStatus.ANKA_VALIDATED.ordinal();

    @Column(name = "status_last_updated")
    private ZonedDateTime statusLastUpdated = ZonedDateTime.now();

    @Column(name = "time_and_material")
    private boolean timeAndMaterial = true;

    @Column(name = "routine_fixed_prices")
    private boolean routineFixedPrices = true;

    @ManyToOne
    @JoinColumn(name = "customer_id")
    private CustomerEntity customer;

    @ManyToOne
    @JoinColumn(name = "project_id")
    private ProjectEntity project;
}
