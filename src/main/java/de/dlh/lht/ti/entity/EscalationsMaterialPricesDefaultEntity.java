package de.dlh.lht.ti.entity;

import de.dlh.lht.ti.entity.abstraction.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
@Entity
@Table(name = "escalations_material_prices_defaults")
public class EscalationsMaterialPricesDefaultEntity extends BaseEntity {

    @Column(name = "engine_id", nullable = false)
    private Long engineId;

    @Column(name = "year", nullable = false)
    private String year;

    @Column(name = "value", nullable = false)
    private BigDecimal value;

}