package de.dlh.lht.ti.entity;

import de.dlh.lht.ti.entity.abstraction.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@SuppressWarnings("JpaDataSourceORMInspection")
@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "z2_ratings")
public class Z2RatingEntity extends BaseEntity {

    @Column(name = "material_pricing_item_id")
    private Long materialPricingItemId;

    private String year;

    @Column(name = "z2_rating")
    private Float z2Rating;
}
