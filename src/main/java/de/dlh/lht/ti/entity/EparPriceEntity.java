package de.dlh.lht.ti.entity;

import de.dlh.lht.ti.entity.abstraction.BaseEntity;
import de.dlh.lht.ti.enums.Currency;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@SuppressWarnings("JpaDataSourceORMInspection")
@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "epar_prices")
public class EparPriceEntity extends BaseEntity {

    @Column(name = "cleaning_and_inspection")
    private BigDecimal cleaningAndInspection;

    private BigDecimal repair;

    @Enumerated(EnumType.STRING)
    private Currency currency;

    private String year;

    @Column(name = "engine_id")
    private Long engineId;

    @Column(name = "engine_version")
    private String engineVersion;
}
