package de.dlh.lht.ti.entity;

import de.dlh.lht.ti.entity.abstraction.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@SuppressWarnings("JpaDataSourceORMInspection")
@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "cleaning_and_inspections")
public class CleaningAndInspectionEntity extends BaseEntity {

    @Column(name = "quotation_id")
    private Long quotationId;

    @Column(name = "is_rfp_engine_included")
    private boolean isRfpEngineCiIncluded;

    @Column(name = "is_rfp_module_included")
    private boolean isRfpModuleCiIncluded;
}
