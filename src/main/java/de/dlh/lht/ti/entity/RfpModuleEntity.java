package de.dlh.lht.ti.entity;

import de.dlh.lht.ti.entity.abstraction.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@SuppressWarnings("JpaDataSourceORMInspection")
@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "rfp_modules")
public class RfpModuleEntity extends BaseEntity {

    private BigDecimal price;

    @Column(name = "labour_pricing_item_id")
    private Long labourPricingItemId;

    @Column(name = "workscope_id")
    private Long workscopeId;
}
