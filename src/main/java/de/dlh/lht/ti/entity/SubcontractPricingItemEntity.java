package de.dlh.lht.ti.entity;

import de.dlh.lht.ti.entity.abstraction.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@SuppressWarnings("JpaDataSourceORMInspection")
@Entity
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "subcontract_pricing_items")
public class SubcontractPricingItemEntity extends BaseEntity {

    private BigDecimal margin;

    private Integer cap;

    @Column(name = "quotation_engine_id")
    private Long quotationEngineId;

    @Column(name = "cluster_id")
    private Long clusterId;
}
