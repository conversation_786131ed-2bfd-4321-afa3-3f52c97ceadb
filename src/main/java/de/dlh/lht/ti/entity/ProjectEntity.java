package de.dlh.lht.ti.entity;

import de.dlh.lht.ti.entity.abstraction.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@SuppressWarnings("JpaDataSourceORMInspection")
@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "projects")
public class ProjectEntity extends BaseEntity {

    @Column(name = "offer_number")
    private String offerNumber;

    @ManyToOne
    @JoinColumn(name = "original_owner_id")
    private UserEntity originalOwner;

    @ManyToOne
    @JoinColumn(name = "current_owner_id")
    private UserEntity currentOwner;
}
