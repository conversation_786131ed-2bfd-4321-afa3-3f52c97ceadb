package de.dlh.lht.ti;

import com.cleverpine.springlogginglibrary.aop.EnableCPLogging;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

@EnableCPLogging
@SpringBootApplication
@ComponentScan(basePackages = {"com.cleverpine.springlogginglibrary.*", "de.dlh.lht.ti.*"})
public class CoreCalculationApplication {

  public static void main(String[] args) {
    SpringApplication.run(CoreCalculationApplication.class, args);
  }
}
