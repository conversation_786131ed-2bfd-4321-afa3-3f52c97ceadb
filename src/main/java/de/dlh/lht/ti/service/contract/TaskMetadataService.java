package de.dlh.lht.ti.service.contract;

import de.dlh.lht.ti.dto.CalculationResultDto;
import de.dlh.lht.ti.dto.RfpEngineMetadataDto;
import de.dlh.lht.ti.dto.WorkscopeDto;
import de.dlh.lht.ti.entity.TaskMetadataEntity;
import de.dlh.lht.ti.enums.TaskType;
import de.dlh.lht.ti.importer.model.LabourPricingItemRaw;
import java.util.List;
import java.util.Map;

public interface TaskMetadataService {

    List<TaskMetadataEntity> createTaskMetadata(
            List<LabourPricingItemRaw> labourPricingItems,
            Map<String, Long> workscopeIdsMap,
            Long quotationEngineId);

    List<RfpEngineMetadataDto> findAllByQuotationIdWorkscopeIdYearAndTaskType(
            Long quotationId,
            TaskType type,
            Long workscopeId,
            String year
    );

    Map<WorkscopeDto, CalculationResultDto> getAllProductionCostByWorkscopeDtoQuotationId(Long quotationId);
}
