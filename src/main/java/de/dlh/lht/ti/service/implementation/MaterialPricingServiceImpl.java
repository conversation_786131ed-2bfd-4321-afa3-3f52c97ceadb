package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.exception.SaveMaterialPricingNotAllowedException;
import de.dlh.lht.ti.model.HandlingCharges;
import de.dlh.lht.ti.model.HandlingChargesRequest;
import de.dlh.lht.ti.model.Z2Ratings;
import de.dlh.lht.ti.service.contract.HandlingChangeService;
import de.dlh.lht.ti.service.contract.MaterialPricingService;
import de.dlh.lht.ti.service.contract.UserPermissionService;
import de.dlh.lht.ti.service.contract.UserService;
import de.dlh.lht.ti.service.contract.Z2RatingService;
import jakarta.transaction.Transactional;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;


import static de.dlh.lht.ti.utils.ErrorMessages.SAVE_HANDLING_CHARGES_NOT_ALLOWED_ERROR_MESSAGE;
import static de.dlh.lht.ti.utils.ErrorMessages.SAVE_Z2_RATING_NOT_ALLOWED_ERROR_MESSAGE;

@Service
@RequiredArgsConstructor
public class MaterialPricingServiceImpl implements MaterialPricingService {

    private final HandlingChangeService handlingChangeService;
    private final Z2RatingService z2RatingService;
    private final UserPermissionService userPermissionService;
    private final UserService userService;

    @Override
    @Transactional
    public HandlingCharges getHandlingCharges(Long quotationId) {
        var canCurrentUserEdit = canCurrentUserEdit(quotationId);

        return handlingChangeService.getHandlingCharges(quotationId, canCurrentUserEdit);
    }

    @Override
    @Transactional
    public HandlingCharges saveHandlingCharges(Long quotationId, HandlingChargesRequest handlingChargesRequest) {
        var currentOwnerUNumber = userService.getCurrentOwnerUNumberByQuotationId(quotationId);

        if (!userPermissionService.isUserOwnerOrAdmin(currentOwnerUNumber)) {
            throw new SaveMaterialPricingNotAllowedException(
                    String.format(SAVE_HANDLING_CHARGES_NOT_ALLOWED_ERROR_MESSAGE, currentOwnerUNumber));
        }

        return handlingChangeService.saveHandlingCharges(quotationId, handlingChargesRequest);
    }

    @Override
    public Z2Ratings getZ2Ratings(Long quotationId) {
        var canCurrentUserEdit = canCurrentUserEdit(quotationId);

        return z2RatingService.getZ2Ratings(quotationId, canCurrentUserEdit);
    }

    @Override
    @Transactional
    public Z2Ratings saveZ2Ratings(Long quotationId, Map<Long, Float> z2RatingsInput) {
        var currentOwnerUNumber = userService.getCurrentOwnerUNumberByQuotationId(quotationId);

        if (!userPermissionService.isUserOwnerOrAdmin(currentOwnerUNumber)) {
            throw new SaveMaterialPricingNotAllowedException(String.format(SAVE_Z2_RATING_NOT_ALLOWED_ERROR_MESSAGE, currentOwnerUNumber));
        }

        return z2RatingService.saveZ2Ratings(quotationId, z2RatingsInput);
    }

    private boolean canCurrentUserEdit(Long quotationId) {
        var currentOwnerUNumber = userService.getCurrentOwnerUNumberByQuotationId(quotationId);
        return userPermissionService.isUserOwnerOrAdmin(currentOwnerUNumber);
    }
}
