package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.enums.QuotationProgress;
import de.dlh.lht.ti.exception.SaveSubcontractPricingNotAllowedException;
import de.dlh.lht.ti.mapper.SubcontractPricingMapper;
import de.dlh.lht.ti.model.Subcontract;
import de.dlh.lht.ti.model.SubcontractInput;
import de.dlh.lht.ti.model.SubcontractPricing;
import de.dlh.lht.ti.model.SubcontractPricingRequest;
import de.dlh.lht.ti.service.contract.NavigationItemService;
import de.dlh.lht.ti.service.contract.SubcontractPricingItemService;
import de.dlh.lht.ti.service.contract.SubcontractPricingService;
import de.dlh.lht.ti.service.contract.UserPermissionService;
import de.dlh.lht.ti.service.contract.UserService;
import de.dlh.lht.ti.utils.Validations;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;


import static de.dlh.lht.ti.utils.ErrorMessages.SAVE_SUBCONTRACT_PRICING_NOT_ALLOWED_ERROR_MESSAGE;
import static de.dlh.lht.ti.utils.GeneralValues.getSubcontractPricingGlobalValues;

@Service
@RequiredArgsConstructor
public class SubcontractPricingServiceImpl implements SubcontractPricingService {

    private final SubcontractPricingMapper subcontractPricingMapper;

    private final NavigationItemService navigationItemService;
    private final SubcontractPricingItemService subcontractPricingItemService;
    private final UserPermissionService userPermissionService;
    private final UserService userService;

    @Override
    public SubcontractPricing getSubcontractPricing(Long quotationId) {
        var currentOwnerUNumber = userService.getCurrentOwnerUNumberByQuotationId(quotationId);
        var canCurrentUserEdit = userPermissionService.isUserOwnerOrAdmin(currentOwnerUNumber);
        var progress = navigationItemService.getQuotationProgressByQuotationId(quotationId);
        var subcontracts = getSubcontracts(quotationId);
        var globalValues = getSubcontractPricingGlobalValues(subcontracts);

        return new SubcontractPricing()
                .globalValues(globalValues)
                .subcontracts(subcontracts)
                .canCurrentUserEdit(canCurrentUserEdit)
                .progress(progress);
    }

    @Override
    public SubcontractPricing saveSubcontractPricing(Long quotationId, SubcontractPricingRequest subcontractPricingRequest) {
        Validations.subcontractInputAssertion(subcontractPricingRequest);

        var currentOwnerUNumber = userService.getCurrentOwnerUNumberByQuotationId(quotationId);
        if (!userPermissionService.isUserOwnerOrAdmin(currentOwnerUNumber)) {
            throw new SaveSubcontractPricingNotAllowedException(
                    String.format(SAVE_SUBCONTRACT_PRICING_NOT_ALLOWED_ERROR_MESSAGE, currentOwnerUNumber));
        }

        var subcontracts = updateSubcontractPricingItems(quotationId, subcontractPricingRequest.getSubcontractInputs());
        var areSubcontractsValid = areSubcontractsValid(subcontracts);

        navigationItemService.updateProgress(quotationId, QuotationProgress.SUBCONTRACT_PRICING, areSubcontractsValid);

        var progress = navigationItemService.getQuotationProgressByQuotationId(quotationId);

        var globalValues = getSubcontractPricingGlobalValues(subcontracts);

        return new SubcontractPricing()
                .globalValues(globalValues)
                .subcontracts(subcontracts)
                .canCurrentUserEdit(true)
                .progress(progress);
    }

    private boolean areSubcontractsValid(List<Subcontract> subcontracts) {
        return subcontracts.stream()
                .noneMatch(subcontract -> subcontract.getMargin() == null);
    }

    private List<Subcontract> getSubcontracts(Long quotationId) {
        var subcontractDtos = subcontractPricingItemService.getAllSubcontractDtosByQuotationId(quotationId);

        if (subcontractDtos == null || subcontractDtos.isEmpty()) {
            return List.of();
        }

        return subcontractPricingMapper.subcontractDtoListToSubcontractList(subcontractDtos);
    }

    private List<Subcontract> updateSubcontractPricingItems(Long quotationId, List<SubcontractInput> subcontractInputs) {
        if (subcontractInputs.isEmpty()) {
            return getSubcontracts(quotationId);
        }

        var subcontractPricingItemsByIdMap = subcontractPricingItemService.getSubcontractPricingItemsByIdMapByQuotationId(quotationId);
        var updatedSubcontractPricingItems = subcontractInputs.stream()
                .map(subcontractInput ->
                        subcontractPricingMapper.subcontractInputToSubcontractPricingItemEntity(
                                subcontractInput,
                                subcontractPricingItemsByIdMap.get(subcontractInput.getId())))
                .toList();

        subcontractPricingItemService.updateSubcontractPricingItems(updatedSubcontractPricingItems);
        return getSubcontracts(quotationId);
    }
}
