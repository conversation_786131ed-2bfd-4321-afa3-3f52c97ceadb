package de.dlh.lht.ti.service.contract;

import de.dlh.lht.ti.dto.LabourPricingItemDto;
import de.dlh.lht.ti.importer.model.LabourPricingItemRaw;
import java.util.List;

public interface LabourPricingItemService {

    void createLabourPricingItems(
            List<LabourPricingItemRaw> labourPricingItems,
            Long quotationEngineId,
            Long engineId
    );

    List<Long> getAllRfpModuleLabourPricingItemIdsByQuotationId(Long quotationId);

    List<LabourPricingItemDto> getLabourPricingItemsDtos(Long quotationEngineId);
}
