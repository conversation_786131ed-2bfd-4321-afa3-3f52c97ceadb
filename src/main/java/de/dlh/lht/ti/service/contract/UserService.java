package de.dlh.lht.ti.service.contract;

import de.dlh.lht.ti.entity.UserEntity;
import de.dlh.lht.ti.model.QuotationOwner;
import de.dlh.lht.ti.model.UserDetails;
import java.util.List;
import java.util.Map;

public interface UserService {

    UserEntity createUserFromUNumber(String uNumber);

    List<QuotationOwner> getAllUsers();

    List<QuotationOwner> getAllQuotationOwnerUsers();

    String getCurrentOwnerUNumberByQuotationId(Long quotationId);

    UserEntity getUserById(Long id);

    UserDetails getUserDetails();

    Map<String, UserEntity> getUserEntityByUNumberMap();
}
