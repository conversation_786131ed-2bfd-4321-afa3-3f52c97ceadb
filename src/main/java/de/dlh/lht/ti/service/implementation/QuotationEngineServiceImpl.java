package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.dto.QuotationDetailsDto;
import de.dlh.lht.ti.dto.QuotationsQueryParametersDto;
import de.dlh.lht.ti.entity.EngineEntity;
import de.dlh.lht.ti.entity.QuotationEngineEntity;
import de.dlh.lht.ti.entity.QuotationEntity;
import de.dlh.lht.ti.entity.WorkscopeEntity;
import de.dlh.lht.ti.enums.QuotationSort;
import de.dlh.lht.ti.exception.EntityNotFoundException;
import de.dlh.lht.ti.mapper.QuotationEngineMapper;
import de.dlh.lht.ti.model.QuotationDetails;
import de.dlh.lht.ti.model.QuotationsPage;
import de.dlh.lht.ti.repository.QuotationEngineRepository;
import de.dlh.lht.ti.service.contract.NavigationItemService;
import de.dlh.lht.ti.service.contract.QuotationEngineService;
import de.dlh.lht.ti.service.contract.UserPermissionService;
import de.dlh.lht.ti.service.contract.WorkscopeService;
import jakarta.transaction.Transactional;
import java.util.Collections;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;


import static de.dlh.lht.ti.utils.ErrorMessages.ENGINE_ENTITY_RELATED_TO_QUOTATION_NOT_FOUND_ERROR_MESSAGE;
import static de.dlh.lht.ti.utils.ErrorMessages.QUOTATION_ENTITY_WITH_ID_NOT_FOUND_ERROR_MESSAGE;
import static de.dlh.lht.ti.utils.QuotationUtil.DEFAULT_QUOTATION_LIST_SORT;

@Service
@RequiredArgsConstructor
public class QuotationEngineServiceImpl implements QuotationEngineService {

    private final QuotationEngineMapper quotationEngineMapper;

    private final QuotationEngineRepository quotationEngineRepository;

    private final NavigationItemService navigationItemService;
    private final UserPermissionService userPermissionService;
    private final WorkscopeService workscopeService;

    @Override
    @Transactional
    public QuotationEngineEntity createQuotationEngineEntity(
            QuotationEntity quotationEntity,
            EngineEntity engineEntity,
            List<WorkscopeEntity> workscopeEntities) {
        var quotationEngine = new QuotationEngineEntity(quotationEntity, engineEntity, workscopeEntities);
        return quotationEngineRepository.save(quotationEngine);
    }

    @Override
    public String getQuotationEngineNameByQuotationId(Long quotationId) {
        return quotationEngineRepository.findQuotationEngineName(quotationId).orElseThrow(
                () -> new EntityNotFoundException(String.format(
                        ENGINE_ENTITY_RELATED_TO_QUOTATION_NOT_FOUND_ERROR_MESSAGE,
                        quotationId
                ))
        );
    }

    @Override
    public QuotationsPage getQuotationsPage(QuotationsQueryParametersDto quotationsQueryParametersDto) {
        if (quotationsQueryParametersDto == null) {
            return new QuotationsPage()
                    .totalPages(0)
                    .totalItems(0L)
                    .quotations(Collections.emptyList());
        }

        var pageIndex = quotationsQueryParametersDto.getPageIndex() == null ? 0 : quotationsQueryParametersDto.getPageIndex();
        var pageSize = quotationsQueryParametersDto.getPageSize() == null ? 1 : quotationsQueryParametersDto.getPageSize();

        var quotationRequest = PageRequest.of(
                pageIndex,
                pageSize,
                getQuotationsSort(quotationsQueryParametersDto.getSortBy())
        );

        var dbQuotationStatus = quotationsQueryParametersDto.getQuotationStatus() == null ?
                null : quotationsQueryParametersDto.getQuotationStatus().ordinal();

        var quotationEntityPage = quotationEngineRepository.findAllWithAppliedFilter(
                quotationRequest,
                quotationsQueryParametersDto.getOwnerId(),
                quotationsQueryParametersDto.getOfferNumber(),
                quotationsQueryParametersDto.getVersion(),
                quotationsQueryParametersDto.getPosition(),
                quotationsQueryParametersDto.getScenario(),
                dbQuotationStatus,
                quotationsQueryParametersDto.getEngineType(),
                quotationsQueryParametersDto.getCustomer()
        );

        var quotationRawList = quotationEngineMapper.quotationEngineEntityListToQuotationLightList(
                quotationEntityPage.getContent(),
                userPermissionService);

        return new QuotationsPage()
                .totalItems(quotationEntityPage.getTotalElements())
                .totalPages(quotationEntityPage.getTotalPages())
                .quotations(quotationRawList);
    }

    @Override
    public QuotationDetails getQuotationDetails(Long quotationId) {
        var quotationDetailsDto = getQuotationDetailsDto(quotationId);
        var workscopes = workscopeService.getWorkscopesByQuotationId(quotationId);
        var navigationProgress = navigationItemService.getQuotationProgressByQuotationId(quotationId);
        var currentOwnerUNumber = quotationDetailsDto.getQuotation().getProject().getCurrentOwner().getUNumber();
        var canCurrentUserEdit = userPermissionService.isUserOwnerOrAdmin(currentOwnerUNumber);
        return quotationEngineMapper.quotationEngineEntityToQuotationDetails(
                        quotationDetailsDto.getQuotation(),
                        quotationDetailsDto.getEngine(),
                        workscopes)
                .progress(navigationProgress)
                .canCurrentUserEdit(canCurrentUserEdit);
    }

    private QuotationDetailsDto getQuotationDetailsDto(Long quotationId) {
        return quotationEngineRepository.findQuotationDetailsDto(quotationId).orElseThrow(
                () -> new EntityNotFoundException(String.format(QUOTATION_ENTITY_WITH_ID_NOT_FOUND_ERROR_MESSAGE, quotationId)));
    }

    private Sort getQuotationsSort(QuotationSort sort) {
        if (sort == null) {
            return DEFAULT_QUOTATION_LIST_SORT;
        }

        return switch (sort) {
            case OFFER_NUMBER -> Sort.by(Sort.Order.asc("project.offerNumber"), Sort.Order.asc("id"));
            case ENGINE_TYPE -> Sort.by(Sort.Order.asc("engine.name"), Sort.Order.asc("id"));
            case CUSTOMER -> Sort.by(Sort.Order.asc("quotation.customer.name"), Sort.Order.asc("id"));
            case LAST_UPDATE -> Sort.by(Sort.Order.desc("quotation.statusLastUpdated"), Sort.Order.asc("id"));
            case OWNER -> Sort.by(Sort.Order.asc("quotation.currentOwner.name"), Sort.Order.asc("id"));
            default -> DEFAULT_QUOTATION_LIST_SORT;
        };
    }
}
