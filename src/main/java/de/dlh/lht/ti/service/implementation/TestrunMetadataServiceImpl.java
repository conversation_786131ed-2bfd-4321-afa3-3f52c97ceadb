package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.dto.CalculationResultDto;
import de.dlh.lht.ti.dto.WorkscopeDto;
import de.dlh.lht.ti.entity.TestrunMetadataEntity;
import de.dlh.lht.ti.importer.model.TestrunItemRaw;
import de.dlh.lht.ti.mapper.TestrunMetadataMapper;
import de.dlh.lht.ti.repository.TestrunMetadataRepository;
import de.dlh.lht.ti.service.contract.TestrunMetadataService;
import de.dlh.lht.ti.utils.Calculations;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class TestrunMetadataServiceImpl implements TestrunMetadataService {

    private final TestrunMetadataMapper testrunMetadataMapper;

    private final TestrunMetadataRepository testrunMetadataRepository;

    @Override
    public List<TestrunMetadataEntity> createTestrunMetadata(
            List<TestrunItemRaw> rawTestrunItems,
            Long testrunItemId,
            Map<String, Long> workscopeIdsByNameMap) {
        if (rawTestrunItems == null || rawTestrunItems.isEmpty()) {
            return Collections.emptyList();
        }

        var newTestrunMetadata = new ArrayList<TestrunMetadataEntity>();

        rawTestrunItems.forEach(testrunItemRaw ->
                testrunItemRaw.getTestrunMetadata().forEach(testrunMetadataRaw -> {
                    var workscopeId = workscopeIdsByNameMap.get(testrunMetadataRaw.getWorkscope());
                    var testrunMetadata = testrunMetadataMapper.testrunMetadataRawToTestrunMetadataEntity(
                            testrunMetadataRaw,
                            testrunItemId,
                            workscopeId);
                    newTestrunMetadata.add(testrunMetadata);
                }));

        return testrunMetadataRepository.saveAll(newTestrunMetadata);
    }

    @Override
    public Map<WorkscopeDto, CalculationResultDto> getAllProductionCostByQuotationId(Long quotationId) {
        var productionCostDtos = testrunMetadataRepository.findAllProductionCostMetadataByQuotationId(quotationId);

        return Calculations.calculateProductionCost(productionCostDtos);
    }
}
