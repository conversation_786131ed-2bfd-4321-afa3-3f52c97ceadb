package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.entity.ProgressStepEntity;
import de.dlh.lht.ti.enums.QuotationProgress;
import de.dlh.lht.ti.repository.ProgressStepRepository;
import de.dlh.lht.ti.service.contract.ProgressStepService;
import java.util.Map;
import java.util.function.Function;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;


import static java.util.stream.Collectors.toMap;

@Service
@RequiredArgsConstructor
public class ProgressStepServiceImpl implements ProgressStepService {

    private final ProgressStepRepository progressStepRepository;

    @Override
    public Map<QuotationProgress, ProgressStepEntity> getAllProgressSteps() {
        return progressStepRepository
                .findAll()
                .stream()
                .collect(toMap(
                        ProgressStepEntity::getName,
                        Function.identity(),
                        (dataOne, dataTwo) -> dataOne));
    }
}
