package de.dlh.lht.ti.service.contract;

import de.dlh.lht.ti.dto.ClusterTaskDto;
import de.dlh.lht.ti.entity.ClusterEntity;
import java.util.List;
import java.util.Map;

public interface ClusterService {

    Map<String, ClusterEntity> createClusters(List<String> rawClusters);

    Long getClusterIdByName(String clusterName);

    Map<String, ClusterTaskDto> getClusterTaskDtoByEngineNameMap(Long engineId);
}