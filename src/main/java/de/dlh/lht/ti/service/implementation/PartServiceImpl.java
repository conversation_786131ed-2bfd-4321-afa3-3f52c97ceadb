package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.dto.importer.PartDto;
import de.dlh.lht.ti.entity.PartEntity;
import de.dlh.lht.ti.exception.EntityNotFoundException;
import de.dlh.lht.ti.importer.model.PartRaw;
import de.dlh.lht.ti.mapper.PartMapper;
import de.dlh.lht.ti.repository.PartRepository;
import de.dlh.lht.ti.service.contract.PartService;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;


import static de.dlh.lht.ti.utils.ErrorMessages.PART_ENTITY_WITH_NAME_AND_TYPE_NOT_FOUND_ERROR_MESSAGE;
import static java.util.stream.Collectors.toMap;

@Service
@RequiredArgsConstructor
public class PartServiceImpl implements PartService {

    private final PartMapper partMapper;

    private final PartRepository partRepository;

    @Override
    public PartDto partRawToPartDto(PartRaw part) {
        return partMapper.partRawToPartDto(part);
    }

    @Override
    public Map<PartDto, Long> getPartIdByPartDtoMap() {
        return partRepository
                .findAll()
                .stream()
                .collect(toMap(partMapper::partEntityToPartDto, PartEntity::getId));
    }

    @Override
    public Long findPartIdByNameAndType(PartRaw partRaw, Map<PartDto, Long> partIdByPartDtoMap) {
        var partDto = partMapper.partRawToPartDto(partRaw);
        var partId = partIdByPartDtoMap.get(partDto);
        if (partId == null) {
            throw new EntityNotFoundException(
                    String.format(PART_ENTITY_WITH_NAME_AND_TYPE_NOT_FOUND_ERROR_MESSAGE, partDto.getName(), partDto.getType()));
        }
        return partId;
    }
}
