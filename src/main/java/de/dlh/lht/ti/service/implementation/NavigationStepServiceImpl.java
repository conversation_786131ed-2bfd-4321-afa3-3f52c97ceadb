package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.entity.NavigationStepEntity;
import de.dlh.lht.ti.enums.NavigationSteps;
import de.dlh.lht.ti.repository.NavigationStepRepository;
import de.dlh.lht.ti.service.contract.NavigationStepService;
import java.util.Map;
import java.util.function.Function;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;


import static java.util.stream.Collectors.toMap;

@Service
@RequiredArgsConstructor
public class NavigationStepServiceImpl implements NavigationStepService {

    private final NavigationStepRepository navigationStepRepository;

    @Override
    public Map<NavigationSteps, NavigationStepEntity> getAllNavigationSteps() {
        return navigationStepRepository
                .findAll()
                .stream()
                .collect(toMap(
                        NavigationStepEntity::getName,
                        Function.identity(),
                        (dataOne, dataTwo) -> dataOne));
    }
}
