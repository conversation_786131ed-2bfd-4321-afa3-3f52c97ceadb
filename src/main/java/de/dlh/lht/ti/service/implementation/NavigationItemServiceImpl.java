package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.entity.NavigationItemEntity;
import de.dlh.lht.ti.enums.NavigationSteps;
import de.dlh.lht.ti.enums.QuotationProgress;
import de.dlh.lht.ti.exception.EntityNotFoundException;
import de.dlh.lht.ti.mapper.ProgressMapper;
import de.dlh.lht.ti.model.Progress;
import de.dlh.lht.ti.repository.NavigationItemRepository;
import de.dlh.lht.ti.service.contract.NavigationItemService;
import de.dlh.lht.ti.service.contract.NavigationStepService;
import de.dlh.lht.ti.service.contract.ProgressStepService;
import java.util.ArrayList;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;


import static de.dlh.lht.ti.utils.ErrorMessages.NAVIGATION_ITEM_ENTITY_WITH_QUOTATION_ID_AND_PROGRESS_STEP_NOT_FOUND_ERROR_MESSAGE;

@Service
@RequiredArgsConstructor
public class NavigationItemServiceImpl implements NavigationItemService {

    private final ProgressMapper progressMapper;

    private final NavigationItemRepository navigationItemRepository;

    private final NavigationStepService navigationStepService;
    private final ProgressStepService progressStepService;

    @Override
    public void createNavigationItems(Long quotationId, boolean isRoutineFixedPrices) {
        var navigationStepsNavigationStepMap = navigationStepService.getAllNavigationSteps();
        var quotationProgressProgressStepMap = progressStepService.getAllProgressSteps();
        var newNavigationItems = new ArrayList<NavigationItemEntity>();

        var cover = new NavigationItemEntity(
                true,
                quotationId,
                navigationStepsNavigationStepMap.get(NavigationSteps.COVER),
                quotationProgressProgressStepMap.get(QuotationProgress.COVER));
        newNavigationItems.add(cover);

        var handlingCharges = new NavigationItemEntity(
                null,
                quotationId,
                navigationStepsNavigationStepMap.get(NavigationSteps.MATERIAL_PRICING),
                quotationProgressProgressStepMap.get(QuotationProgress.HANDLING_CHARGES));
        newNavigationItems.add(handlingCharges);

        var z2Rating = new NavigationItemEntity(
                null,
                quotationId,
                navigationStepsNavigationStepMap.get(NavigationSteps.MATERIAL_PRICING),
                quotationProgressProgressStepMap.get(QuotationProgress.Z2_RATINGS));
        newNavigationItems.add(z2Rating);

        var labourRateAndEpar = new NavigationItemEntity(
                null,
                quotationId,
                navigationStepsNavigationStepMap.get(NavigationSteps.LABOUR_PRICING),
                quotationProgressProgressStepMap.get(QuotationProgress.LABOUR_RATE_AND_EPAR));
        newNavigationItems.add(labourRateAndEpar);

        if (isRoutineFixedPrices) {
            var rfpEngine = new NavigationItemEntity(
                    null,
                    quotationId,
                    navigationStepsNavigationStepMap.get(NavigationSteps.LABOUR_PRICING),
                    quotationProgressProgressStepMap.get(QuotationProgress.RFP_ENGINE));
            newNavigationItems.add(rfpEngine);

            var rfpModule = new NavigationItemEntity(
                    null,
                    quotationId,
                    navigationStepsNavigationStepMap.get(NavigationSteps.LABOUR_PRICING),
                    quotationProgressProgressStepMap.get(QuotationProgress.RFP_MODULE));
            newNavigationItems.add(rfpModule);
        }
        var subcontractPricing = new NavigationItemEntity(
                null,
                quotationId,
                navigationStepsNavigationStepMap.get(NavigationSteps.SUBCONTRACT_PRICING),
                quotationProgressProgressStepMap.get(QuotationProgress.SUBCONTRACT_PRICING));
        newNavigationItems.add(subcontractPricing);

        var pricingEscalation = new NavigationItemEntity(
                null,
                quotationId,
                navigationStepsNavigationStepMap.get(NavigationSteps.PRICING_ESCALATION),
                quotationProgressProgressStepMap.get(QuotationProgress.PRICING_ESCALATION));
        newNavigationItems.add(pricingEscalation);

        var workscopeSummary = new NavigationItemEntity(
                null,
                quotationId,
                navigationStepsNavigationStepMap.get(NavigationSteps.WORKSCOPE_SUMMARY),
                quotationProgressProgressStepMap.get(QuotationProgress.WORKSCOPE_SUMMARY));
        newNavigationItems.add(workscopeSummary);

        navigationItemRepository.saveAll(newNavigationItems);
    }

    @Override
    public Progress getQuotationProgressByQuotationId(Long quotationId) {
        var navigationItems = navigationItemRepository.findAllByQuotationId(quotationId);

        return progressMapper.navigationItemEntityListToProgress(navigationItems);
    }

    @Override
    public void updateProgress(Long quotationId, QuotationProgress progressStep, Boolean isValid) {
        var navigationItem = getNavigationItemByQuotationIdAndProgressStep(quotationId, progressStep);

        navigationItem.setIsValid(isValid);

        navigationItemRepository.save(navigationItem);
    }

    private NavigationItemEntity getNavigationItemByQuotationIdAndProgressStep(Long quotationId, QuotationProgress progressStep) {
        return navigationItemRepository.findByQuotationIdAndProgressStepName(quotationId, progressStep).orElseThrow(() -> {
            throw new EntityNotFoundException(
                    String.format(NAVIGATION_ITEM_ENTITY_WITH_QUOTATION_ID_AND_PROGRESS_STEP_NOT_FOUND_ERROR_MESSAGE, quotationId,
                            progressStep));
        });
    }
}
