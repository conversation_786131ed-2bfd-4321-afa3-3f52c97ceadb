package de.dlh.lht.ti.service.contract;

import de.dlh.lht.ti.model.LabourRate;
import de.dlh.lht.ti.model.LabourRateInput;
import de.dlh.lht.ti.model.RfpEngine;
import de.dlh.lht.ti.model.RfpModule;
import de.dlh.lht.ti.model.RfpRequest;

public interface LabourPricingService {

    LabourRate getLabourRates(Long quotationId);

    LabourRate saveLabourRates(Long quotationId, LabourRateInput labourRateInput);

    RfpEngine getRfpEngineData(Long quotationId);

    RfpEngine saveRfpEngineData(Long quotationId, RfpRequest rfpEngineData);

    RfpModule getRfpModule(Long quotationId);

    RfpModule saveRfpModule(Long quotationId, RfpRequest rfpEngineData);
}
