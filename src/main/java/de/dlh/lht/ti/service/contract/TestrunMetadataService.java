package de.dlh.lht.ti.service.contract;

import de.dlh.lht.ti.dto.CalculationResultDto;
import de.dlh.lht.ti.dto.WorkscopeDto;
import de.dlh.lht.ti.entity.TestrunMetadataEntity;
import de.dlh.lht.ti.importer.model.TestrunItemRaw;
import java.util.List;
import java.util.Map;

public interface TestrunMetadataService {

    List<TestrunMetadataEntity> createTestrunMetadata(
            List<TestrunItemRaw> rawTestrunItems,
            Long testrunItemId,
            Map<String, Long> workscopeIdsByNameMap);

    Map<WorkscopeDto, CalculationResultDto> getAllProductionCostByQuotationId(Long quotationId);
}
