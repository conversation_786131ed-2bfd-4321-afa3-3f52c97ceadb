package de.dlh.lht.ti.service.contract;

import de.dlh.lht.ti.dto.EscalationPricingDto;
import de.dlh.lht.ti.entity.EscalationPricingEntity;

import java.math.BigDecimal;
import java.util.List;

public interface EscalationPricingDefaultsService {

    EscalationPricingDto mapEntityToDtoWithMaterialPricesDefaultValue(EscalationPricingEntity entity, Long engineId);

    BigDecimal getEscalationsMaterialPricesDefault(String year, Long engineId);

    BigDecimal getEscalationsSubcontractPricesDefault(String year);

    void setDefaultValues(List<EscalationPricingDto> escalationPriceDtoList, Boolean isRfpContractSelected);
}
