package de.dlh.lht.ti.service.contract;

import de.dlh.lht.ti.model.HandlingCharges;
import de.dlh.lht.ti.model.HandlingChargesRequest;
import de.dlh.lht.ti.model.Z2Ratings;
import java.util.Map;

public interface MaterialPricingService {

    HandlingCharges getHandlingCharges(Long quotationId);

    HandlingCharges saveHandlingCharges(Long quotationId, HandlingChargesRequest handlingChargesRequest);

    Z2Ratings getZ2Ratings(Long quotationId);

    Z2Ratings saveZ2Ratings(Long quotationId, Map<Long, Float> z2RatingsInput);
}
