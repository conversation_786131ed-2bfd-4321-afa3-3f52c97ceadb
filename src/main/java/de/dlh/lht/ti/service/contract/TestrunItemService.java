package de.dlh.lht.ti.service.contract;

import de.dlh.lht.ti.entity.TestrunItemEntity;
import de.dlh.lht.ti.model.RfpItem;
import de.dlh.lht.ti.model.RfpItemInput;
import java.math.BigDecimal;

public interface TestrunItemService {

    TestrunItemEntity createTestrunItem(Long quotationEngineId);

    RfpItem getRfpItemByQuotationIdYearAndWorkscopeId(Long quotationId, String year, Long workscopeId, BigDecimal exchangeRate);

    void setHandlingChargeId(Long quotationId, Long handlingChargeId);

    void setPrice(Long quotationId, RfpItemInput rfpItemInput);
}
