package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.dto.ClusterTaskDto;
import de.dlh.lht.ti.entity.ClusterEntity;
import de.dlh.lht.ti.exception.EntityNotFoundException;
import de.dlh.lht.ti.repository.ClusterRepository;
import de.dlh.lht.ti.service.contract.ClusterService;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;


import static de.dlh.lht.ti.utils.ErrorMessages.CLUSTER_ENTITY_WITH_NAME_NOT_FOUND_ERROR_MESSAGE;

@Service
@RequiredArgsConstructor
public class ClusterServiceImpl implements ClusterService {

    private final ClusterRepository clusterRepository;

    @Override
    public Map<String, ClusterEntity> createClusters(List<String> rawClusters) {
        var clusters = new ArrayList<>(rawClusters);
        var alreadyExistingClusters = clusterRepository.findAllByNameIn(rawClusters)
                .stream().collect(Collectors.toMap(ClusterEntity::getName, Function.identity()));

        clusters.removeAll(alreadyExistingClusters.keySet());

        var newClusters = clusters.stream()
                .map(ClusterEntity::new)
                .toList();
        newClusters = clusterRepository.saveAll(newClusters);

        var groupedClustersByName = new ArrayList<ClusterEntity>();
        groupedClustersByName.addAll(alreadyExistingClusters.values());
        groupedClustersByName.addAll(newClusters);

        return groupedClustersByName.stream()
                .collect(Collectors.toMap(ClusterEntity::getName, Function.identity()));
    }

    @Override
    public Long getClusterIdByName(String clusterName) {
        return clusterRepository.findIdByName(clusterName).orElseThrow(
                () -> new EntityNotFoundException(String.format(CLUSTER_ENTITY_WITH_NAME_NOT_FOUND_ERROR_MESSAGE, clusterName)));
    }

    @Override
    public Map<String, ClusterTaskDto> getClusterTaskDtoByEngineNameMap(Long engineId) {
        var clusterTasks = clusterRepository.findAllClusterTasksByEngineId(engineId);
        return clusterTasks.stream()
                .collect(Collectors.toMap(ClusterTaskDto::getTaskName, clusterTaskDto -> clusterTaskDto));
    }
}
