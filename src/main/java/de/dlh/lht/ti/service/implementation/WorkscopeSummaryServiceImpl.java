package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.dto.CalculationResultDto;
import de.dlh.lht.ti.entity.PartMetadataEntity;
import de.dlh.lht.ti.entity.SubcontractMetadataEntity;
import de.dlh.lht.ti.entity.TaskMetadataEntity;
import de.dlh.lht.ti.entity.TestrunMetadataEntity;
import de.dlh.lht.ti.entity.WorkscopeSummaryEntity;
import de.dlh.lht.ti.entity.abstraction.BaseMetadataEntity;
import de.dlh.lht.ti.mapper.WorkscopeSummaryMapper;
import de.dlh.lht.ti.model.WorkscopeSummary;
import de.dlh.lht.ti.repository.WorkscopeSummaryRepository;
import de.dlh.lht.ti.service.contract.DiscountService;
import de.dlh.lht.ti.service.contract.ProductionCostService;
import de.dlh.lht.ti.service.contract.QuotationService;
import de.dlh.lht.ti.service.contract.WorkscopeSummaryService;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class WorkscopeSummaryServiceImpl implements WorkscopeSummaryService {

    private final WorkscopeSummaryMapper workscopeSummaryMapper;

    private final WorkscopeSummaryRepository workscopeSummaryRepository;

    private final ProductionCostService productionCostService;
    private final QuotationService quotationService;
    private final DiscountService discountService;

    @Override
    public List<WorkscopeSummary> getWorkscopeSummaries(Long quotationId) {
        var usdExchangeRate = quotationService.getQuotationUsdExchangeRate(quotationId);
        var workscopeSummaries = workscopeSummaryRepository.findAllByQuotationId(quotationId);

        return workscopeSummaryMapper.workscopeSummaryDtoListToWorkscopeSummaryList(workscopeSummaries, usdExchangeRate);
    }

    @Override
    public void saveWorkscopeSummaries(
            Long quotationId,
            List<Long> workscopeIds,
            int startYear,
            int endYear,
            List<PartMetadataEntity> partMetadata,
            List<TaskMetadataEntity> taskMetadata,
            List<SubcontractMetadataEntity> subcontractMetadata,
            List<TestrunMetadataEntity> testrunMetadata
    ) {
        var groupedPartMetadataByWorkscopeAndYearMap = getGroupedMetadataByWorkscopeAndYear(partMetadata);
        var groupedTaskMetadataByWorkscopeAndYearMap = getGroupedMetadataByWorkscopeAndYear(taskMetadata);
        var groupedSubContractMetadataByWorkscopeAndYearMap = getGroupedMetadataByWorkscopeAndYear(subcontractMetadata);
        var groupedTestrunMetadataByWorkscopeAndYearMap = getGroupedMetadataByWorkscopeAndYear(testrunMetadata);

        var workscopeSummaries = createWorkscopeSummaries(quotationId, workscopeIds, startYear, endYear);
        workscopeSummaries.forEach(workscopeSummary -> setProductionCost(
                groupedPartMetadataByWorkscopeAndYearMap,
                groupedTaskMetadataByWorkscopeAndYearMap,
                groupedSubContractMetadataByWorkscopeAndYearMap,
                groupedTestrunMetadataByWorkscopeAndYearMap,
                workscopeSummary
        ));

        setDiscounts(quotationId, workscopeSummaries);

        workscopeSummaryRepository.saveAll(workscopeSummaries);
    }

    private void setDiscounts(Long quotationId, List<WorkscopeSummaryEntity> workscopeSummaries) {
        var discounts = discountService.getDiscounts(quotationId);
        for (WorkscopeSummaryEntity workscopeSummary : workscopeSummaries) {
            var workscopeId = workscopeSummary.getWorkscopeId();
            var year = workscopeSummary.getYear();
            var discount = discounts.getOrDefault(workscopeId, Collections.emptyMap()).getOrDefault(year, null);
            if (discount != null) {
                setWorkscopeSummaryDiscount(workscopeSummary, discount);
            }
        }
    }

    private void setWorkscopeSummaryDiscount(
            WorkscopeSummaryEntity workscopeSummary,
            CalculationResultDto discount
    ) {
        workscopeSummary.setDiscountUsd(discount.getValueUsd());
        workscopeSummary.setDiscountEur(discount.getValueEur());
    }

    private void setProductionCost(
            Map<Long, Map<String, List<PartMetadataEntity>>> groupedPartMetadataByWorkscopeAndYearMap,
            Map<Long, Map<String, List<TaskMetadataEntity>>> groupedTaskMetadataByWorkscopeAndYearMap,
            Map<Long, Map<String, List<SubcontractMetadataEntity>>> groupedSubContractMetadataByWorkscopeAndYearMap,
            Map<Long, Map<String, List<TestrunMetadataEntity>>> groupedTestrunMetadataByWorkscopeAndYearMap,
            WorkscopeSummaryEntity workscopeSummary
    ) {
        var productionCostEuro = BigDecimal.ZERO;
        var productionCostUsd = BigDecimal.ZERO;
        var materialProductCost = productionCostService.getMaterialProductionCost(
                groupedPartMetadataByWorkscopeAndYearMap
                        .getOrDefault(workscopeSummary.getWorkscopeId(), Collections.emptyMap())
                        .getOrDefault(workscopeSummary.getYear(), Collections.emptyList())
        );
        productionCostUsd = productionCostUsd.add(materialProductCost.getValueUsd());
        productionCostEuro = productionCostEuro.add(materialProductCost.getValueEur());

        var labourProductCost = productionCostService.getLabourProductionCost(
                groupedTaskMetadataByWorkscopeAndYearMap
                        .getOrDefault(workscopeSummary.getWorkscopeId(), Collections.emptyMap())
                        .getOrDefault(workscopeSummary.getYear(), Collections.emptyList())
        );
        productionCostUsd = productionCostUsd.add(labourProductCost.getValueUsd());
        productionCostEuro = productionCostEuro.add(labourProductCost.getValueEur());

        var subContactProductCost = productionCostService.getSubContractProductionCost(
                groupedSubContractMetadataByWorkscopeAndYearMap
                        .getOrDefault(workscopeSummary.getWorkscopeId(), Collections.emptyMap())
                        .getOrDefault(workscopeSummary.getYear(), Collections.emptyList())
        );
        productionCostUsd = productionCostUsd.add(subContactProductCost.getValueUsd());
        productionCostEuro = productionCostEuro.add(subContactProductCost.getValueEur());

        var testRunProductCost = productionCostService.getTestrunProductionCost(
                groupedTestrunMetadataByWorkscopeAndYearMap
                        .getOrDefault(workscopeSummary.getWorkscopeId(), Collections.emptyMap())
                        .getOrDefault(workscopeSummary.getYear(), Collections.emptyList())
        );
        productionCostUsd = productionCostUsd.add(testRunProductCost.getValueUsd());
        productionCostEuro = productionCostEuro.add(testRunProductCost.getValueEur());

        workscopeSummary.setProductionCostUsd(productionCostUsd);
        workscopeSummary.setProductionCostEur(productionCostEuro);
    }

    private <T extends BaseMetadataEntity> Map<Long, Map<String, List<T>>> getGroupedMetadataByWorkscopeAndYear(
            List<T> metadata
    ) {
        return metadata.stream().collect(Collectors.groupingBy(
                BaseMetadataEntity::getWorkscopeId,
                Collectors.groupingBy(BaseMetadataEntity::getYear)
        ));
    }

    private List<WorkscopeSummaryEntity> createWorkscopeSummaries(
            Long quotationId,
            List<Long> workscopeIds,
            int startYear,
            int endYear
    ) {
        var workscopeSummaries = new ArrayList<WorkscopeSummaryEntity>();
        workscopeIds.forEach(workscopeId -> {
            for (var year = startYear; year <= endYear; year++) {
                var workscopeSummary = WorkscopeSummaryEntity.builder()
                        .quotationId(quotationId)
                        .workscopeId(workscopeId)
                        .year(Integer.toString(year))
                        .build();
                workscopeSummaries.add(workscopeSummary);
            }
        });

        return workscopeSummaries;
    }
}
