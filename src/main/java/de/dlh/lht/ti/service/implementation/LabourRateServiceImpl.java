package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.entity.LabourRateEntity;
import de.dlh.lht.ti.exception.EntityNotFoundException;
import de.dlh.lht.ti.exception.ValidationException;
import de.dlh.lht.ti.mapper.LabourRateMapper;
import de.dlh.lht.ti.model.LabourRate;
import de.dlh.lht.ti.model.LabourRateInput;
import de.dlh.lht.ti.repository.LabourRateRepository;
import de.dlh.lht.ti.service.contract.LabourRateService;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;


import java.math.BigDecimal;

import static de.dlh.lht.ti.utils.ErrorMessages.LABOUR_RATE_ENTITY_NOT_FOUND_ERROR_MESSAGE;
import static de.dlh.lht.ti.utils.ErrorMessages.LABOUR_RATE_EPAR_DISCOUNT_VALIDATION_ERROR_MESSAGE;
import static de.dlh.lht.ti.utils.ErrorMessages.LABOUR_RATE_NON_ROUTINE_VALIDATION_ERROR_MESSAGE;
import static de.dlh.lht.ti.utils.ErrorMessages.LABOUR_RATE_ROUTINE_VALIDATION_ERROR_MESSAGE;

@Service
@RequiredArgsConstructor
public class LabourRateServiceImpl implements LabourRateService {

    private final LabourRateMapper labourRateMapper;

    private final LabourRateRepository labourRateRepository;

    @Override
    public LabourRate getLabourRates(Long quotationId) {
        var labourRateEntity = getOrCreateLabourRateEntity(quotationId);
        var labourRate = labourRateMapper.toApiModel(labourRateEntity);

        return new LabourRate().labourRate(labourRate);
    }

    @Override
    @Transactional
    public LabourRate saveLabourRates(Long quotationId, LabourRateInput labourRateInput)
            throws ValidationException {

        labourRateInputAssertion(labourRateInput);

        var labourRateEntity = updateLabourRateEntity(quotationId, labourRateInput);
        var labourRate = labourRateMapper.toApiModel(labourRateEntity);

        return new LabourRate().labourRate(labourRate);
    }

    @Override
    public boolean hasValidLabourRate(LabourRate labourRate) {
        return labourRate.getLabourRate().getRoutineLabourRate() != null
                && labourRate.getLabourRate().getRoutineLabourRate() > 0
                && labourRate.getLabourRate().getNonRoutineLabourRate() != null
                && labourRate.getLabourRate().getNonRoutineLabourRate() > 0
                && labourRate.getLabourRate().getEparDiscount() != null
                && labourRate.getLabourRate().getEparDiscount() >= 0
                && labourRate.getLabourRate().getEparDiscount() < 100;
    }

    private LabourRateEntity updateLabourRateEntity(
            Long quotationId,
            LabourRateInput labourRateInput
    ) {

        var labourRateEntity = labourRateRepository.getLabourRateEntityByQuotationId(quotationId)
                .orElseThrow(() -> new EntityNotFoundException(
                        String.format(LABOUR_RATE_ENTITY_NOT_FOUND_ERROR_MESSAGE, quotationId)));

        labourRateEntity = labourRateMapper.labourRateInputToLabourRateEntity(
                labourRateInput,
                labourRateEntity,
                quotationId
        );

        return labourRateRepository.save(labourRateEntity);
    }

    private LabourRateEntity getOrCreateLabourRateEntity(Long quotationId) {
        var labourRateEntityContainer =
                labourRateRepository.getLabourRateEntityByQuotationId(quotationId);

        if (labourRateEntityContainer.isPresent()) {
            return labourRateEntityContainer.get();
        } else {
            var labourRateEntity = new LabourRateEntity(quotationId, null, null, BigDecimal.ZERO);
            return labourRateRepository.save(labourRateEntity);
        }
    }

    private void labourRateInputAssertion(LabourRateInput labourRateInput) throws ValidationException {
        if (labourRateInput.getRoutineLabourRate() != null && labourRateInput.getRoutineLabourRate() < 0) {
            throw new ValidationException(LABOUR_RATE_ROUTINE_VALIDATION_ERROR_MESSAGE);
        }
        if (labourRateInput.getNonRoutineLabourRate() != null && labourRateInput.getNonRoutineLabourRate() < 0) {
            throw new ValidationException(LABOUR_RATE_NON_ROUTINE_VALIDATION_ERROR_MESSAGE);
        }
        if (labourRateInput.getEparDiscount() == null) {
            throw new ValidationException(LABOUR_RATE_EPAR_DISCOUNT_VALIDATION_ERROR_MESSAGE);
        }
    }
}
