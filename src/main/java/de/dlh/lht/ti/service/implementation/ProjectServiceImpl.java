package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.entity.ProjectEntity;
import de.dlh.lht.ti.entity.UserEntity;
import de.dlh.lht.ti.exception.ChangeOwnerNotAllowedException;
import de.dlh.lht.ti.exception.EntityNotFoundException;
import de.dlh.lht.ti.exception.OfferNumberValidationException;
import de.dlh.lht.ti.repository.ProjectRepository;
import de.dlh.lht.ti.service.contract.ProjectService;
import de.dlh.lht.ti.service.contract.UserPermissionService;
import de.dlh.lht.ti.service.contract.UserService;
import jakarta.transaction.Transactional;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;


import static de.dlh.lht.ti.utils.Constants.OFFER_NUMBER_VALIDATION_REGEX;
import static de.dlh.lht.ti.utils.ErrorMessages.CHANGE_OWNER_NOT_ALLOWED_ERROR_MESSAGE;
import static de.dlh.lht.ti.utils.ErrorMessages.OFFER_NUMBER_VALIDATION_ERROR_MESSAGE;
import static de.dlh.lht.ti.utils.ErrorMessages.PROJECT_ENTITY_WITH_OFFER_NUMBER_NOT_FOUND_ERROR_MESSAGE;
import static java.util.function.Function.identity;

@Service
@RequiredArgsConstructor
public class ProjectServiceImpl implements ProjectService {

    private final ProjectRepository projectRepository;

    private final UserService userService;

    private final UserPermissionService userPermissionService;

    @Override
    @Transactional
    public void changeOwner(String offerNumber, Long newOwnerId) {
        validateOfferNumber(offerNumber);
        var projectEntity = getProjectEntityByOfferNumber(offerNumber);
        var currentOwnerUNumber = projectEntity.getCurrentOwner().getUNumber();

        if (!userPermissionService.isUserOwnerOrAdmin(currentOwnerUNumber)) {
            throw new ChangeOwnerNotAllowedException(String.format(CHANGE_OWNER_NOT_ALLOWED_ERROR_MESSAGE, currentOwnerUNumber));
        }
        var newOwner = userService.getUserById(newOwnerId);
        projectEntity.setCurrentOwner(newOwner);
        projectRepository.save(projectEntity);
    }

    @Override
    @Transactional
    public ProjectEntity createProject(String offerNumber, UserEntity owner) {
        var project = new ProjectEntity(offerNumber, owner, owner);
        return projectRepository.save(project);
    }

    @Override
    public Map<String, ProjectEntity> getProjectEntityByOfferNumberMap() {
        return projectRepository.findAll().stream().collect(Collectors.toMap(ProjectEntity::getOfferNumber, identity()));
    }

    private ProjectEntity getProjectEntityByOfferNumber(String offerNumber) {
        return projectRepository.findByOfferNumber(offerNumber)
                .orElseThrow(() -> new EntityNotFoundException(
                        String.format(PROJECT_ENTITY_WITH_OFFER_NUMBER_NOT_FOUND_ERROR_MESSAGE, offerNumber)));
    }

    private void validateOfferNumber(String offerNumber) {
        if (!offerNumber.matches(OFFER_NUMBER_VALIDATION_REGEX)) {
            throw new OfferNumberValidationException(
                    String.format(OFFER_NUMBER_VALIDATION_ERROR_MESSAGE, offerNumber)
            );
        }
    }
}
