package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.dto.RfpModuleDataDto;
import de.dlh.lht.ti.entity.RfpModuleEntity;
import de.dlh.lht.ti.enums.TaskType;
import de.dlh.lht.ti.mapper.RfpModuleMapper;
import de.dlh.lht.ti.model.RfpModuleData;
import de.dlh.lht.ti.model.RfpRequest;
import de.dlh.lht.ti.repository.RfpModuleRepository;
import de.dlh.lht.ti.service.contract.CleaningAndInspectionService;
import de.dlh.lht.ti.service.contract.LabourPricingItemService;
import de.dlh.lht.ti.service.contract.QuotationService;
import de.dlh.lht.ti.service.contract.RfpModuleService;
import de.dlh.lht.ti.service.contract.WorkscopeService;
import de.dlh.lht.ti.utils.Validations;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;


import static de.dlh.lht.ti.utils.ErrorMessages.RFP_MODULE_VALIDATION_ERROR_MESSAGE;

@Service
@RequiredArgsConstructor
public class RfpModuleServiceImpl implements RfpModuleService {

    private final RfpModuleMapper rfpModuleMapper;

    private final RfpModuleRepository rfpModuleRepository;

    private final CleaningAndInspectionService cleaningAndInspectionService;
    private final LabourPricingItemService labourPricingItemService;
    private final QuotationService quotationService;
    private final WorkscopeService workscopeService;

    @Override
    public RfpModuleData getRfpModuleData(Long quotationId) {
        var rfpQuotationDto = quotationService.getRfpQuotationDtoById(quotationId);
        var baseYear = String.valueOf(rfpQuotationDto.getContactStart().getYear());
        var rfpModuleDtoList = getRfpModulesOrCreateIfDoNotExist(quotationId, baseYear);

        var rfpModule = rfpModuleMapper.rfpModuleDataDtoListToRfpModule(
                rfpModuleDtoList,
                rfpQuotationDto.getUsdExchangeRate());

        return rfpModule
                .isCiIncluded(rfpQuotationDto.getIsRfpModuleCiIncluded());
    }

    @Override
    public RfpModuleData saveRfpModuleData(Long quotationId, RfpRequest rfpRequest) {
        Validations.rfpItemInputAssertion(rfpRequest, RFP_MODULE_VALIDATION_ERROR_MESSAGE, TaskType.RFP_MODULE);

        var rfpModuleByIdMap = rfpModuleRepository
                .findAllByQuotationId(quotationId).stream()
                .collect(Collectors.toMap(RfpModuleEntity::getId, Function.identity()));

        var rfpModuleEntityList = rfpRequest.getRfpItemInputs().stream()
                .filter(item -> rfpModuleByIdMap.get(item.getId()) != null)
                .map(item -> rfpModuleMapper.rfpItemInputToRfpModuleEntity(item, rfpModuleByIdMap.get(item.getId())))
                .toList();

        rfpModuleRepository.saveAll(rfpModuleEntityList);

        cleaningAndInspectionService.saveRfpModuleCiIncludedFlag(quotationId, rfpRequest.getCiIncluded());

        return getRfpModuleData(quotationId);
    }

    @Override
    public boolean isRfpModuleDataValid(RfpModuleData rfpModuleData) {
        return rfpModuleData.getWorkscopes().stream()
                .flatMap(workscope -> workscope.getRfpItems().stream())
                .noneMatch(rfpItem -> rfpItem.getPrice() == null);
    }

    private List<RfpModuleDataDto> getRfpModulesOrCreateIfDoNotExist(Long quotationId, String baseYear) {
        var rfpModuleDtoList = rfpModuleRepository.findAllDtosByQuotationIdAndBaseYear(quotationId, baseYear);

        if (rfpModuleDtoList.isEmpty()) {
            var newRfpModules = new ArrayList<RfpModuleEntity>();
            var labourPricingItemIds = labourPricingItemService.getAllRfpModuleLabourPricingItemIdsByQuotationId(quotationId);
            var systemWorkscopeIds = workscopeService.getSystemWorkscopeIdsByQuotationId(quotationId);

            labourPricingItemIds.forEach(labourPricingItemId ->
                    systemWorkscopeIds.forEach(systemWorkscopeId -> {
                        var newRfpModule = new RfpModuleEntity(null, labourPricingItemId, systemWorkscopeId);
                        newRfpModules.add(newRfpModule);
                    })
            );

            rfpModuleRepository.saveAll(newRfpModules);

            rfpModuleDtoList = rfpModuleRepository.findAllDtosByQuotationIdAndBaseYear(quotationId, baseYear);
        }

        return rfpModuleDtoList;
    }
}
