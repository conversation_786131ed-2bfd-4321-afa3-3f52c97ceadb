package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.exception.EntityNotFoundException;
import de.dlh.lht.ti.repository.EparPriceRepository;
import de.dlh.lht.ti.service.contract.EparPriceService;
import java.math.BigDecimal;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;


import static de.dlh.lht.ti.utils.ErrorMessages.EPAR_PRICE_ENTITY_NOT_FOUND_ERROR_MESSAGE;

@Service
@RequiredArgsConstructor
public class EparPriceServiceImpl implements EparPriceService {

    private final EparPriceRepository eparPriceRepository;

    @Override
    public BigDecimal getCleaningAndInspectionEparPriceByEngineIdVersionAndYear(Long engineId, String engineVersion, String year
    ) {
        return eparPriceRepository.findCleaningAndInspectionEparPriceByEngineIdVersionAndYear(engineId, engineVersion, year)
                .orElseThrow(() -> new EntityNotFoundException(
                        String.format(EPAR_PRICE_ENTITY_NOT_FOUND_ERROR_MESSAGE, engineId, engineVersion)));
    }
}
