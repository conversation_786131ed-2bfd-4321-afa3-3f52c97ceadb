package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.dto.CalculationResultDto;
import de.dlh.lht.ti.dto.WorkscopeDto;
import de.dlh.lht.ti.entity.PartMetadataEntity;
import de.dlh.lht.ti.entity.SubcontractMetadataEntity;
import de.dlh.lht.ti.entity.TaskMetadataEntity;
import de.dlh.lht.ti.entity.TestrunMetadataEntity;
import de.dlh.lht.ti.enums.Currency;
import de.dlh.lht.ti.service.contract.PartMetadataService;
import de.dlh.lht.ti.service.contract.ProductionCostService;
import de.dlh.lht.ti.service.contract.SubcontractMetadataService;
import de.dlh.lht.ti.service.contract.TaskMetadataService;
import de.dlh.lht.ti.service.contract.TestrunMetadataService;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;


import static de.dlh.lht.ti.utils.Constants.HUNDRED;
import static de.dlh.lht.ti.utils.Constants.MATH_CONTEXT;

@Service
@RequiredArgsConstructor
public class ProductionCostServiceImpl implements ProductionCostService {

    private final PartMetadataService partMetadataService;
    private final SubcontractMetadataService subcontractMetadataService;
    private final TaskMetadataService taskMetadataService;
    private final TestrunMetadataService testrunMetadataService;

    @Override
    public CalculationResultDto getMaterialProductionCost(List<PartMetadataEntity> metadata) {
        var calculationDto = CalculationResultDto.builder().valueUsd(BigDecimal.ZERO).valueEur(BigDecimal.ZERO).build();
        metadata.forEach(metadataEntity -> {
            addToProductionCost(calculationDto, metadataEntity.getZ1Value(), metadataEntity.getZ1WeightedQuantity(), metadataEntity.getQuantity(), metadataEntity.getZ1Currency());
            addToProductionCost(calculationDto, metadataEntity.getZ2Value(), metadataEntity.getZ2WeightedQuantity(), metadataEntity.getQuantity(), metadataEntity.getZ2Currency());
            addToProductionCost(calculationDto, metadataEntity.getPmaValue(), metadataEntity.getPmaWeightedQuantity(), metadataEntity.getQuantity(), metadataEntity.getPmaCurrency());
            addToProductionCost(calculationDto, metadataEntity.getCsmValue(), metadataEntity.getCsmWeightedQuantity(), metadataEntity.getQuantity(), metadataEntity.getCsmCurrency());
        });

        return calculationDto;
    }

    @Override
    public CalculationResultDto getLabourProductionCost(List<TaskMetadataEntity> metadata) {
        var calculationDto = CalculationResultDto.builder().valueUsd(BigDecimal.ZERO).valueEur(BigDecimal.ZERO).build();
        metadata.forEach(metadataEntity ->
                addToProductionCost(calculationDto, metadataEntity.getValue(), metadataEntity.getWeightedQuantity(), metadataEntity.getQuantity(), metadataEntity.getCurrency()));
        return calculationDto;
    }

    @Override
    public CalculationResultDto getSubContractProductionCost(List<SubcontractMetadataEntity> metadata) {
        var calculationDto = CalculationResultDto.builder().valueUsd(BigDecimal.ZERO).valueEur(BigDecimal.ZERO).build();
        metadata.forEach(metadataEntity ->
                addToProductionCost(calculationDto, metadataEntity.getValue(), metadataEntity.getWeightedQuantity(), metadataEntity.getQuantity(), metadataEntity.getCurrency()));
        return calculationDto;
    }

    @Override
    public CalculationResultDto getTestrunProductionCost(List<TestrunMetadataEntity> metadata) {
        var calculationDto = CalculationResultDto.builder().valueUsd(BigDecimal.ZERO).valueEur(BigDecimal.ZERO).build();
        metadata.forEach(metadataEntity ->
                addToProductionCost(calculationDto, metadataEntity.getValue(), metadataEntity.getWeightedQuantity(), metadataEntity.getQuantity(), metadataEntity.getCurrency()));
        return calculationDto;
    }

    private void addToProductionCost(
            CalculationResultDto calculationResultDto,
            BigDecimal value,
            BigDecimal weightedQuantity,
            int quantity,
            Currency currency
    ) {
        if(value == null || weightedQuantity == null || quantity == 0) {
            return;
        }

        var z1ProductionCost = value
                .multiply(BigDecimal.valueOf(quantity), MATH_CONTEXT)
                .multiply(weightedQuantity.divide(HUNDRED, MATH_CONTEXT), MATH_CONTEXT);
        if(currency == Currency.EUR) {
            var cost = calculationResultDto.getValueEur().add(z1ProductionCost);
            calculationResultDto.setValueEur(cost);
        } else {
            var cost = calculationResultDto.getValueUsd().add(z1ProductionCost);
            calculationResultDto.setValueUsd(cost);
        }
    }

    public Map<WorkscopeDto, CalculationResultDto> getProductionCostsByWorkscopeDtoMap(Long quotationId) {
        var costsByWorksocpeDtoMapList = List.of(
                partMetadataService.getAllProductionCostByWorkscopeDtoByQuotationId(quotationId),
                subcontractMetadataService.getAllProductionCostByWorkscopeDtoByQuotationId(quotationId),
                taskMetadataService.getAllProductionCostByWorkscopeDtoQuotationId(quotationId),
                testrunMetadataService.getAllProductionCostByQuotationId(quotationId));

        var costsByWorkscopeDtoMap = new HashMap<WorkscopeDto, CalculationResultDto>();
        costsByWorksocpeDtoMapList.forEach(map ->
                map.forEach((workscopeDto, calculationResultDto) ->
                        costsByWorkscopeDtoMap.merge(workscopeDto, calculationResultDto, this::sumTwoCalculationResultDtos)));

        return costsByWorkscopeDtoMap;
    }

    private CalculationResultDto sumTwoCalculationResultDtos(
            CalculationResultDto calculationResultDto1,
            CalculationResultDto calculationResultDto2) {
        return CalculationResultDto.builder()
                .valueUsd(calculationResultDto1.getValueUsd().add(calculationResultDto2.getValueUsd()))
                .valueEur(calculationResultDto1.getValueEur().add(calculationResultDto2.getValueEur()))
                .build();
    }
}
