package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.entity.CleaningAndInspectionEntity;
import de.dlh.lht.ti.repository.CleaningAndInspectionRepository;
import de.dlh.lht.ti.service.contract.CleaningAndInspectionService;
import java.util.function.BiConsumer;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class CleaningAndInspectionServiceImpl implements CleaningAndInspectionService {

    private final CleaningAndInspectionRepository cleaningAndInspectionRepository;

    @Override
    public CleaningAndInspectionEntity saveRfpEngineCiIncludedFlag(
            Long quotationId,
            boolean ciIncluded
    ) {
        return saveCleaningAndInspectionFlag(
                quotationId,
                ciIncluded,
                CleaningAndInspectionEntity::setRfpEngineCiIncluded
        );
    }

    @Override
    public CleaningAndInspectionEntity saveRfpModuleCiIncludedFlag(
            Long quotationId,
            boolean ciIncluded
    ) {
        return saveCleaningAndInspectionFlag(
                quotationId,
                ciIncluded,
                CleaningAndInspectionEntity::setRfpModuleCiIncluded
        );
    }

    private CleaningAndInspectionEntity saveCleaningAndInspectionFlag(
            Long quotationId,
            boolean ciIncluded,
            BiConsumer<CleaningAndInspectionEntity, Boolean> updateCallback
    ) {
        var existingRecord = cleaningAndInspectionRepository.findByQuotationId(quotationId);
        CleaningAndInspectionEntity cleaningAndInspectionEntity;
        if (existingRecord.isPresent()) {
            cleaningAndInspectionEntity = existingRecord.get();
        } else {
            cleaningAndInspectionEntity = new CleaningAndInspectionEntity();
            cleaningAndInspectionEntity.setQuotationId(quotationId);
        }
        updateCallback.accept(cleaningAndInspectionEntity, ciIncluded);

        return cleaningAndInspectionRepository.save(cleaningAndInspectionEntity);
    }
}
