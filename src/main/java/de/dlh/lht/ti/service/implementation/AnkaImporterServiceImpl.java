package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.dto.importer.CustomerDto;
import de.dlh.lht.ti.dto.importer.PartDto;
import de.dlh.lht.ti.dto.importer.WorkscopeDto;
import de.dlh.lht.ti.entity.CustomerEntity;
import de.dlh.lht.ti.entity.EngineEntity;
import de.dlh.lht.ti.entity.PartMetadataEntity;
import de.dlh.lht.ti.entity.ProjectEntity;
import de.dlh.lht.ti.entity.UserEntity;
import de.dlh.lht.ti.entity.WorkscopeEntity;
import de.dlh.lht.ti.enums.NonRoutineMaterialSubType;
import de.dlh.lht.ti.exception.EntityNotFoundException;
import de.dlh.lht.ti.importer.model.CustomerRaw;
import de.dlh.lht.ti.importer.model.MaterialPricingItemRaw;
import de.dlh.lht.ti.importer.model.PartType;
import de.dlh.lht.ti.importer.model.ProjectRaw;
import de.dlh.lht.ti.importer.model.QuotationRaw;
import de.dlh.lht.ti.importer.model.WorkscopeRaw;
import de.dlh.lht.ti.service.contract.AnkaImporterService;
import de.dlh.lht.ti.service.contract.CustomerService;
import de.dlh.lht.ti.service.contract.EngineClusterPartService;
import de.dlh.lht.ti.service.contract.EngineService;
import de.dlh.lht.ti.service.contract.LabourPricingItemService;
import de.dlh.lht.ti.service.contract.MaterialPricingItemService;
import de.dlh.lht.ti.service.contract.PartMetadataService;
import de.dlh.lht.ti.service.contract.PartService;
import de.dlh.lht.ti.service.contract.ProjectService;
import de.dlh.lht.ti.service.contract.QuotationEngineService;
import de.dlh.lht.ti.service.contract.QuotationService;
import de.dlh.lht.ti.service.contract.SubcontractMetadataService;
import de.dlh.lht.ti.service.contract.SubcontractPricingItemService;
import de.dlh.lht.ti.service.contract.TaskMetadataService;
import de.dlh.lht.ti.service.contract.TestrunItemService;
import de.dlh.lht.ti.service.contract.TestrunMetadataService;
import de.dlh.lht.ti.service.contract.UserService;
import de.dlh.lht.ti.service.contract.WorkscopeService;
import de.dlh.lht.ti.service.contract.WorkscopeSummaryService;
import jakarta.transaction.Transactional;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;


import static de.dlh.lht.ti.utils.ErrorMessages.ENGINE_ENTITY_WITH_NAME_NOT_FOUND_ERROR_MESSAGE;
import static java.util.stream.Collectors.toMap;

@Service
@RequiredArgsConstructor
public class AnkaImporterServiceImpl implements AnkaImporterService {

    private final CustomerService customerService;
    private final EngineService engineService;
    private final EngineClusterPartService engineClusterPartService;
    private final LabourPricingItemService labourPricingItemService;
    private final MaterialPricingItemService materialPricingItemService;
    private final PartMetadataService partMetadataService;
    private final PartService partService;
    private final ProjectService projectService;
    private final QuotationService quotationService;
    private final QuotationEngineService quotationEngineService;
    private final SubcontractMetadataService subcontractMetadataService;
    private final SubcontractPricingItemService subcontractPricingItemService;
    private final TaskMetadataService taskMetadataService;
    private final TestrunItemService testrunItemService;
    private final TestrunMetadataService testrunMetadataService;
    private final UserService userService;
    private final WorkscopeService workscopeService;
    private final WorkscopeSummaryService workscopeSummaryService;

    @Transactional
    @Override
    public void importQuotation(QuotationRaw quotationRaw) {

        var clusterIdMap = engineClusterPartService.getClusterIdByPartIdByEngineIdMap();
        var customerMap = customerService.getCustomerEntityByCustomerDtoMap();
        var engineMap = engineService.getEngineEntityByEngineNameMap();
        var partIdMap = partService.getPartIdByPartDtoMap();
        var projectMap = projectService.getProjectEntityByOfferNumberMap();
        var userMap = userService.getUserEntityByUNumberMap();
        var workscopeMap = workscopeService.getWorkscopeEntityByWorkscopeDtoMap();

        saveQuotationAndPartMetadata(
                quotationRaw,
                clusterIdMap,
                customerMap,
                engineMap,
                partIdMap,
                projectMap,
                userMap,
                workscopeMap
        );
    }

    private void saveQuotationAndPartMetadata(
            QuotationRaw quotationRaw,
            Map<Long, Map<Long, Long>> clusterIdMap,
            Map<CustomerDto, CustomerEntity> customerMap,
            Map<String, EngineEntity> engineMap,
            Map<PartDto, Long> partIdMap,
            Map<String, ProjectEntity> projectMap,
            Map<String, UserEntity> userMap,
            Map<WorkscopeDto, WorkscopeEntity> workscopeMap) {
        var project = getProjectOrCreateNewIfDoesNotExist(quotationRaw.getProject(), projectMap, userMap);

        if (quotationService.quotationEntityExistsByPositionAndVersionAndScenarioAndProjectId(
                quotationRaw.getPosition(),
                quotationRaw.getVersion(),
                quotationRaw.getScenario(),
                project.getId())) {
            return;
        }

        var customer = getCustomerOrCreateNewIfDoesNotExist(quotationRaw.getCustomer(), customerMap);
        var engine = findEngineEntityByName(quotationRaw.getEngine(), engineMap);
        var quotation = quotationService.createQuotation(quotationRaw, project, customer);
        var workscopes = getWorkscopesOrCreateNewIfDoNotExist(quotationRaw.getWorkscopes(), workscopeMap);
        var quotationEngine = quotationEngineService.createQuotationEngineEntity(quotation, engine, workscopes);
        var workscopeIdsMap = workscopes.stream().collect(toMap(WorkscopeEntity::getName, WorkscopeEntity::getId));
        var partMetadata = createPartMetadata(
                quotationRaw.getMaterialPricingItems(),
                engine.getId(),
                quotationEngine.getId(),
                clusterIdMap,
                partIdMap,
                workscopeIdsMap);
        partMetadata = partMetadataService.createPartMetadata(partMetadata);

        labourPricingItemService.createLabourPricingItems(
                quotationRaw.getLabourPricingItems(),
                quotationEngine.getId(),
                engine.getId()
        );
        var taskMetadata = taskMetadataService.createTaskMetadata(
                quotationRaw.getLabourPricingItems(),
                workscopeIdsMap,
                quotationEngine.getId()
        );

        var subcontractPricingItems = subcontractPricingItemService.createSubcontractPricingItems(
                quotationRaw.getSubcontractPricingItems(),
                engine.getId(),
                quotationEngine.getId(),
                partIdMap,
                clusterIdMap);
        var subContactMetadata = subcontractMetadataService.createSubcontractMetadata(
                quotationRaw.getSubcontractPricingItems(),
                subcontractPricingItems,
                engine.getId(),
                partIdMap,
                clusterIdMap,
                workscopeIdsMap);

        var testrunItem = testrunItemService.createTestrunItem(quotationEngine.getId());
        var testRunMetadata = testrunMetadataService.createTestrunMetadata(
                quotationRaw.getTestrunItems(),
                testrunItem.getId(),
                workscopeIdsMap);

        workscopeSummaryService.saveWorkscopeSummaries(
                quotation.getId(),
                workscopes.stream().map(WorkscopeEntity::getId).toList(),
                quotation.getContractStart().getYear(),
                quotation.getContractEnd().getYear(),
                partMetadata,
                taskMetadata,
                subContactMetadata,
                testRunMetadata
        );
    }

    private List<PartMetadataEntity> createPartMetadata(
            List<MaterialPricingItemRaw> rawMaterialPricingItems,
            Long engineId,
            Long quotationEngineId,
            Map<Long, Map<Long, Long>> clusterIdMap,
            Map<PartDto, Long> partIdMap,
            Map<String, Long> workscopeIdsMap) {
        var newPartMetadata = new ArrayList<PartMetadataEntity>();

        rawMaterialPricingItems.forEach(materialPricingPartData -> {
            var partId = partService.findPartIdByNameAndType(materialPricingPartData.getPart(), partIdMap);
            var clusterId = clusterIdMap.get(engineId).get(partId);
            var materialPricingItem = materialPricingItemService.createMaterialPricingItemEntity(
                    quotationEngineId,
                    clusterId,
                    partId,
                    getNonRoutineSubType(materialPricingPartData.getPart().getType())
            );
            var partMetadata = partMetadataService.partMetadataRawListToPartMetadataEntityListV2(
                    materialPricingPartData.getPartMetadata(),
                    materialPricingItem.getId(),
                    workscopeIdsMap);

            newPartMetadata.addAll(partMetadata);
        });

        return newPartMetadata;
    }

    private NonRoutineMaterialSubType getNonRoutineSubType(PartType type) {
        return switch (type) {
            case REPAIR_A_PART -> NonRoutineMaterialSubType.A_PART;
            case REPAIR_CASE_AND_FRAME -> NonRoutineMaterialSubType.CASE_AND_FRAME;
            case REPAIR_LLP -> NonRoutineMaterialSubType.LLP;
            case REPAIR_COMPONENT -> NonRoutineMaterialSubType.COMPONENT;
            default -> null;
        };
    }

    private CustomerEntity getCustomerOrCreateNewIfDoesNotExist(CustomerRaw customerRaw, Map<CustomerDto, CustomerEntity> customerMap) {
        var customerDto = customerService.customerRawToCustomerDto(customerRaw);
        if (!customerMap.containsKey(customerDto)) {
            var customer = customerService.createCustomerEntity(customerDto);
            customerMap.put(customerDto, customer);
        }
        return customerMap.get(customerDto);
    }

    private ProjectEntity getProjectOrCreateNewIfDoesNotExist(
            ProjectRaw projectRaw,
            Map<String, ProjectEntity> projectMap,
            Map<String, UserEntity> userMap) {
        var offerNumber = projectRaw.getOfferNumber();
        if (!projectMap.containsKey(offerNumber)) {
            var user = getUserOrCreateNewIfDoesNotExist(projectRaw.getOwner(), userMap);
            var project = projectService.createProject(offerNumber, user);
            projectMap.put(offerNumber, project);
        }
        return projectMap.get(offerNumber);
    }

    private UserEntity getUserOrCreateNewIfDoesNotExist(String uNumber, Map<String, UserEntity> userMap) {
        if (!userMap.containsKey(uNumber)) {
            var user = userService.createUserFromUNumber(uNumber);
            userMap.put(uNumber, user);
        }
        return userMap.get(uNumber);
    }

    private List<WorkscopeEntity> getWorkscopesOrCreateNewIfDoNotExist(
            List<WorkscopeRaw> rawWorkscopes,
            Map<WorkscopeDto, WorkscopeEntity> workscopeMap) {
        var workscopes = new ArrayList<WorkscopeEntity>();
        var workscopeDtos = workscopeService.workscopeRawListToWorkscopeDtoList(rawWorkscopes);
        workscopeDtos.forEach(workscopeDto -> {
            if (!workscopeMap.containsKey(workscopeDto)) {
                var workscope = workscopeService.createWorkscope(workscopeDto);
                workscopeMap.put(workscopeDto, workscope);
            }
            workscopes.add(workscopeMap.get(workscopeDto));
        });
        return workscopes;
    }

    private EngineEntity findEngineEntityByName(String engineName, Map<String, EngineEntity> engineMap) {
        var engine = engineMap.get(engineName);
        if (engine == null) {
            throw new EntityNotFoundException(String.format(ENGINE_ENTITY_WITH_NAME_NOT_FOUND_ERROR_MESSAGE, engineName));
        }
        return engine;
    }
}
