package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.dto.LabourPricingItemDto;
import de.dlh.lht.ti.entity.LabourPricingItemEntity;
import de.dlh.lht.ti.importer.model.LabourPricingItemRaw;
import de.dlh.lht.ti.importer.model.TaskMetadataRaw;
import de.dlh.lht.ti.importer.model.TaskType;
import de.dlh.lht.ti.mapper.LabourPricingItemMapper;
import de.dlh.lht.ti.repository.LabourPricingItemRepository;
import de.dlh.lht.ti.service.contract.ClusterService;
import de.dlh.lht.ti.service.contract.LabourPricingItemService;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;


import static de.dlh.lht.ti.enums.TaskType.NON_ROUTINE;
import static de.dlh.lht.ti.enums.TaskType.RFP_MODULE;

@Service
@RequiredArgsConstructor
public class LabourPricingItemServiceImpl implements LabourPricingItemService {

    private final LabourPricingItemRepository labourPricingItemRepository;

    private final LabourPricingItemMapper labourPricingItemMapper;

    private final ClusterService clusterService;

    @Override
    public List<LabourPricingItemDto> getLabourPricingItemsDtos(Long quotationEngineId) {
        var fromStaticClustersItems = labourPricingItemRepository
                .findAllLabourPricingItemsByQuotationEngineId(quotationEngineId);
        var fromDynamicClustersItems = labourPricingItemRepository
                .findAllLabourPricingItemsFromDynamicClusterByQuotationEngineId(quotationEngineId, List.of(RFP_MODULE, NON_ROUTINE));

        var labourPricingItems = new ArrayList<>(fromStaticClustersItems);
        labourPricingItems.addAll(fromDynamicClustersItems);

        return labourPricingItems;
    }

    @Override
    public void createLabourPricingItems(List<LabourPricingItemRaw> rawLabourPricingItems, Long quotationEngineId, Long engineId) {
        var labourPricingItemsForInsertion = new ArrayList<LabourPricingItemEntity>();

        var groupedRawLabourPricingItems = rawLabourPricingItems.stream()
                .collect(Collectors.groupingBy(item -> item.getTask().getType()));
        groupedRawLabourPricingItems.forEach((type, items) -> {
            switch (type) {
                case RFP_ENGINE -> {
                    var rfpEngineItems = extractRfpEngineItems(quotationEngineId, engineId, items);
                    labourPricingItemsForInsertion.addAll(rfpEngineItems);
                }
                case RFP_MODULE -> {
                    var rfpModuleItems = extractedRfpModuleItems(quotationEngineId, items);
                    labourPricingItemsForInsertion.addAll(rfpModuleItems);
                }
                default -> {
                    var uniqueLabourRfpItem =
                            extractLabourPricingItemWithUnifyingCluster(type, quotationEngineId);
                    labourPricingItemsForInsertion.add(uniqueLabourRfpItem);
                }
            }
        });

        labourPricingItemRepository.saveAll(labourPricingItemsForInsertion);
    }

    @Override
    public List<Long> getAllRfpModuleLabourPricingItemIdsByQuotationId(Long quotationId) {
        return labourPricingItemRepository.findAllRfpModuleLabourPricingItemIdsByQuotationId(quotationId);
    }

    private LabourPricingItemEntity extractLabourPricingItemWithUnifyingCluster(TaskType type, Long quotationEngineId) {
        var dynamicClusters = clusterService.createClusters(List.of(type.getValue()));

        var labourPricingItem = new LabourPricingItemEntity();
        labourPricingItem.setQuotationEngineId(quotationEngineId);
        labourPricingItem.setClusterId(dynamicClusters.get(type.getValue()).getId());
        labourPricingItem.setType(de.dlh.lht.ti.enums.TaskType.valueOf(type.getValue()));

        return labourPricingItem;
    }

    private List<LabourPricingItemEntity> extractedRfpModuleItems(Long quotationEngineId, List<LabourPricingItemRaw> items) {
        var groupedByNameLabourPricingItems = items.stream()
                .flatMap(item -> item.getTaskMetadata().stream())
                .map(TaskMetadataRaw::getName)
                .distinct().toList();
        var dynamicClusters =
                clusterService.createClusters(groupedByNameLabourPricingItems);

        var labourPricingRfpModuleItems = items.stream().collect(Collectors.toMap(
                item -> dynamicClusters.get(item.getTask().getName()).getId(),
                item -> item
        ));

        return labourPricingItemMapper.labourPricingRawItemsToEntities(
                labourPricingRfpModuleItems,
                quotationEngineId
        );
    }

    private List<LabourPricingItemEntity> extractRfpEngineItems(
            Long quotationEngineId,
            Long engineId,
            List<LabourPricingItemRaw> labourPricingEngineRawItems
    ) {
        var clusterTasksDtos = clusterService.getClusterTaskDtoByEngineNameMap(engineId);

        return labourPricingItemMapper.labourPricingRawItemsToEntities(
                de.dlh.lht.ti.enums.TaskType.RFP_ENGINE,
                clusterTasksDtos,
                labourPricingEngineRawItems,
                quotationEngineId
        );
    }
}