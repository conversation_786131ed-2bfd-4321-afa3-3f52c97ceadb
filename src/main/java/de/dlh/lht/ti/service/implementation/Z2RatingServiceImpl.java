package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.dto.Z2RatingDto;
import de.dlh.lht.ti.entity.Z2RatingEntity;
import de.dlh.lht.ti.enums.PartType;
import de.dlh.lht.ti.enums.QuotationProgress;
import de.dlh.lht.ti.mapper.Z2RatingMapper;
import de.dlh.lht.ti.model.Z2Ratings;
import de.dlh.lht.ti.repository.Z2RatingRepository;
import de.dlh.lht.ti.service.contract.MaterialPricingItemService;
import de.dlh.lht.ti.service.contract.NavigationItemService;
import de.dlh.lht.ti.service.contract.PartMetadataService;
import de.dlh.lht.ti.service.contract.WorkscopeService;
import de.dlh.lht.ti.service.contract.Z2RatingService;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


import static de.dlh.lht.ti.utils.LoggingMessages.RETRIEVE_Z2_RATINGS;
import static de.dlh.lht.ti.utils.SquashDtoList.squashDtoListByYearAndPartType;
import static java.util.stream.Collectors.groupingBy;

@Service
@RequiredArgsConstructor
@Slf4j
public class Z2RatingServiceImpl implements Z2RatingService {

    private final Z2RatingMapper z2RatingMapper;

    private final Z2RatingRepository z2RatingRepository;

    private final NavigationItemService navigationItemService;
    private final PartMetadataService partMetadataService;
    private final MaterialPricingItemService materialPricingItemService;
    private final WorkscopeService workscopeService;

    @Override
    public Z2Ratings getZ2Ratings(Long quotationId, boolean canCurrentUserEdit) {
        var z2RatingDtoList = getOrCreateZ2RatingEntities(quotationId);
        var progress = navigationItemService.getQuotationProgressByQuotationId(quotationId);
        return z2RatingMapper.z2RatingDtoToZ2Ratings(z2RatingDtoList)
                .progress(progress)
                .canCurrentUserEdit(canCurrentUserEdit);
    }

    @Override
    public Z2Ratings saveZ2Ratings(Long quotationId, Map<Long, Float> z2RatingsInput) {
        long startTime = System.currentTimeMillis();

        var z2RatingEntities = z2RatingRepository.findAllByIdIn(quotationId, z2RatingsInput.keySet());

        z2RatingEntities.forEach(z2RatingEntity ->
                z2RatingEntity.setZ2Rating(z2RatingsInput.get(z2RatingEntity.getId()))
        );
        z2RatingRepository.saveAll(z2RatingEntities);

        log.debug(RETRIEVE_Z2_RATINGS, (System.currentTimeMillis() - startTime));

        var workscopeId = workscopeService.getDeepestWorkscopeIdByQuotationId(quotationId);
        var z2RatingDtos =
                z2RatingRepository.findAllZ2RatingsByQuotationIdAndWorkscopeId(quotationId, workscopeId);

        var isValid = isZ2RatingStepValid(z2RatingDtos);

        navigationItemService.updateProgress(quotationId, QuotationProgress.Z2_RATINGS, isValid);

        var progress = navigationItemService.getQuotationProgressByQuotationId(quotationId);

        return z2RatingMapper.z2RatingDtoToZ2Ratings(z2RatingDtos)
                .progress(progress)
                .canCurrentUserEdit(true);
    }

    private List<Z2RatingEntity> createZ2RatingEntityList(Long materialPricingItemId, List<String> contactYears) {
        return contactYears.stream().map(year -> {
            var z2RatingEntity = new Z2RatingEntity();
            z2RatingEntity.setMaterialPricingItemId(materialPricingItemId);
            z2RatingEntity.setYear(year);
            return z2RatingEntity;
        }).toList();
    }

    private List<Z2RatingDto> saveZ2RatingsData(Long workscopeId, Long quotationId) {
        var materialPricingItemIds =
                materialPricingItemService.findAllMaterialPricingItemsByQuotationId(quotationId);
        var contractYears =
                partMetadataService.findAllUniqueYearsOfPartMetadataByQuotationId(quotationId);

        if (materialPricingItemIds == null || materialPricingItemIds.isEmpty()) {
            return Collections.emptyList();
        }

        var newZ2RatingEntities = new ArrayList<Z2RatingEntity>();

        materialPricingItemIds.forEach(materialPricingItemId -> {
            var z2EntityList = createZ2RatingEntityList(materialPricingItemId, contractYears);
            newZ2RatingEntities.addAll(z2EntityList);
        });

        z2RatingRepository.saveAll(newZ2RatingEntities);

        return z2RatingRepository.findAllZ2RatingsByQuotationIdAndWorkscopeId(quotationId, workscopeId);
    }

    private boolean isZ2RatingStepValid(List<Z2RatingDto> z2RatingDtoList) {
        return z2RatingDtoList != null
                && !z2RatingDtoList.isEmpty()
                && !z2RatingDtoList.stream().map(Z2RatingDto::getZ2Rating).toList().contains(null);
    }

    private Map<String, Map<PartType, List<Z2RatingDto>>> groupZ2RatingDtoListByYearAndPartType(List<Z2RatingDto> z2RatingDtoList) {
        return z2RatingDtoList.stream()
                .collect(
                        groupingBy(Z2RatingDto::getYear,
                                groupingBy(z2RatingDto -> z2RatingDto.getPart().getType())));
    }

    private List<Z2RatingDto> getOrCreateZ2RatingEntities(Long quotationId) {
        var workscopeId = workscopeService.getDeepestWorkscopeIdByQuotationId(quotationId);
        var z2RatingDtos =
                z2RatingRepository.findAllZ2RatingsByQuotationIdAndWorkscopeId(quotationId, workscopeId);

        if (z2RatingDtos == null || z2RatingDtos.isEmpty()) {
            z2RatingDtos = saveZ2RatingsData(workscopeId, quotationId);
        }
        var groupedZ2RatingDtoListByYearAndPartType = groupZ2RatingDtoListByYearAndPartType(z2RatingDtos);

        return squashDtoListByYearAndPartType(groupedZ2RatingDtoListByYearAndPartType);
    }
}
