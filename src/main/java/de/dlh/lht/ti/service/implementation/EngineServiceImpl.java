package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.dto.importer.EngineDto;
import de.dlh.lht.ti.entity.EngineEntity;
import de.dlh.lht.ti.repository.EngineRepository;
import de.dlh.lht.ti.service.contract.EngineService;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;


import static java.util.function.Function.identity;
import static java.util.stream.Collectors.toMap;

@Service
@RequiredArgsConstructor
public class EngineServiceImpl implements EngineService {

    private final EngineRepository engineRepository;

    @Override
    public EngineDto engineNameToEngineDto(String name) {
        return new EngineDto(name);
    }

    @Override
    public Map<String, EngineEntity> getEngineEntityByEngineNameMap() {
        return engineRepository.findAll().stream().collect(toMap(EngineEntity::getName, identity()));
    }

    @Override
    public List<String> getEnginesAsQuotationFilters() {
        return engineRepository.findAllEngineEntitiesNames();
    }

    @Override
    public String getEngineNameByQuotationId(Long quotationId) {
        return engineRepository.findEngineNameByQuotationId(quotationId);
    }

    @Override
    public EngineEntity getEngineByQuotationId(Long quotationId) {
        return engineRepository.findEngineEntityByQuotationId(quotationId);
    }

    @Override
    public Long getEngineIdByQuotationId(Long quotationId) {
        return engineRepository.findEngineIdByQuotationId(quotationId);
    }
}
