package de.dlh.lht.ti.service.contract;

import de.dlh.lht.ti.entity.PartMetadataEntity;
import de.dlh.lht.ti.entity.SubcontractMetadataEntity;
import de.dlh.lht.ti.entity.TaskMetadataEntity;
import de.dlh.lht.ti.entity.TestrunMetadataEntity;
import de.dlh.lht.ti.model.WorkscopeSummary;
import java.util.List;

public interface WorkscopeSummaryService {

    void saveWorkscopeSummaries(
            Long quotationId,
            List<Long> workscopeIds,
            int startYear,
            int endYear,
            List<PartMetadataEntity> partMetadata,
            List<TaskMetadataEntity> taskMetadata,
            List<SubcontractMetadataEntity> subcontractMetadata,
            List<TestrunMetadataEntity> testrunMetadata
    );

    List<WorkscopeSummary> getWorkscopeSummaries(Long quotationId);
}
