package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.dto.importer.WorkscopeDto;
import de.dlh.lht.ti.entity.WorkscopeEntity;
import de.dlh.lht.ti.exception.EntityNotFoundException;
import de.dlh.lht.ti.importer.model.WorkscopeRaw;
import de.dlh.lht.ti.mapper.WorkscopeMapper;
import de.dlh.lht.ti.repository.WorkscopeRepository;
import de.dlh.lht.ti.service.contract.EngineService;
import de.dlh.lht.ti.service.contract.WorkscopeService;
import jakarta.transaction.Transactional;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;


import static de.dlh.lht.ti.utils.ErrorMessages.WORKSCOPE_ENTITY_WITH_NAME_NOT_FOUND_ERROR_MESSAGE;
import static de.dlh.lht.ti.utils.SystemWorkscopesUtil.DEEPEST_WORKSCOPE_PER_ENGINE;
import static java.util.function.Function.identity;
import static java.util.stream.Collectors.toMap;

@Service
@RequiredArgsConstructor
public class WorkscopeServiceImpl implements WorkscopeService {

    private final WorkscopeMapper workscopeMapper;

    private final WorkscopeRepository workscopeRepository;

    private final EngineService engineService;

    @Override
    @Transactional
    public WorkscopeEntity createWorkscope(WorkscopeDto workscopeDto) {
        var workscope = workscopeMapper.workscopeDtoToWorkscopeEntity(workscopeDto);
        return workscopeRepository.save(workscope);
    }

    @Override
    public List<Long> getSystemWorkscopeIdsByQuotationId(Long quotationId) {
        return workscopeRepository.findSystemWorkscopeIdsByQuotationId(quotationId);
    }

    @Override
    public List<WorkscopeEntity> getWorkscopesByQuotationId(Long quotationId) {
        return workscopeRepository.findWorkscopesByQuotationId(quotationId);
    }

    @Override
    public Long getDeepestWorkscopeIdByQuotationId(Long quotationId) {
        var engineName = engineService.getEngineNameByQuotationId(quotationId);
        var deepestWorkscopeName = DEEPEST_WORKSCOPE_PER_ENGINE.get(engineName);
        return getWorkscopeIdByWorkscopeName(deepestWorkscopeName);
    }

    private Long getWorkscopeIdByWorkscopeName(String name) {
        return workscopeRepository.findWorkscopeIdByName(name).orElseThrow(
                () -> new EntityNotFoundException(String.format(
                        WORKSCOPE_ENTITY_WITH_NAME_NOT_FOUND_ERROR_MESSAGE,
                        name
                ))
        );
    }

    @Override
    public List<WorkscopeDto> workscopeRawListToWorkscopeDtoList(List<WorkscopeRaw> rawWorkscopes) {
        return workscopeMapper.workscopeRawListToWorkscopeDtoList(rawWorkscopes);
    }

    @Override
    public Map<WorkscopeDto, WorkscopeEntity> getWorkscopeEntityByWorkscopeDtoMap() {
        return workscopeRepository
                .findAll()
                .stream()
                .collect(toMap(workscopeMapper::workscopeEntityToWorkscopeDto, identity()));
    }
}
