package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.dto.HandlingChargeDto;
import de.dlh.lht.ti.entity.HandlingChargeEntity;
import de.dlh.lht.ti.enums.PartType;
import de.dlh.lht.ti.enums.QuotationProgress;
import de.dlh.lht.ti.mapper.HandlingChargeMapper;
import de.dlh.lht.ti.model.HandlingCharges;
import de.dlh.lht.ti.model.HandlingChargesRequest;
import de.dlh.lht.ti.repository.HandlingChargeRepository;
import de.dlh.lht.ti.service.contract.ClusterService;
import de.dlh.lht.ti.service.contract.HandlingChangeService;
import de.dlh.lht.ti.service.contract.MaterialPricingItemService;
import de.dlh.lht.ti.service.contract.NavigationItemService;
import de.dlh.lht.ti.service.contract.QuotationService;
import de.dlh.lht.ti.service.contract.TestrunItemService;
import de.dlh.lht.ti.service.contract.WorkscopeService;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;


import static de.dlh.lht.ti.utils.Constants.ROUTINE_MATERIAL;
import static de.dlh.lht.ti.utils.SquashDtoList.squashDtoListByPartType;
import static java.util.stream.Collectors.groupingBy;

@Service
@RequiredArgsConstructor
public class HandlingChargeServiceImpl implements HandlingChangeService {

    private final HandlingChargeMapper handlingChargeMapper;

    private final HandlingChargeRepository handlingChargeRepository;

    private final ClusterService clusterService;
    private final MaterialPricingItemService materialPricingItemService;
    private final NavigationItemService navigationItemService;
    private final TestrunItemService testrunItemService;
    private final WorkscopeService workscopeService;
    private final QuotationService quotationService;

    @Override
    public HandlingCharges getHandlingCharges(Long quotationId, boolean canCurrentUserEdit) {
        var handlingChargeDtoList = getHandlingChargesOrCreateIfDoNotExist(quotationId);
        var progress = navigationItemService.getQuotationProgressByQuotationId(quotationId);
        return handlingChargeMapper.handlingChargesDtoListToHandlingCharges(handlingChargeDtoList)
                .progress(progress)
                .canCurrentUserEdit(canCurrentUserEdit);
    }

    @Override
    public HandlingCharges saveHandlingCharges(Long quotationId, HandlingChargesRequest handlingChargesRequest) {
        var handlingChargesDtoList = getHandlingChargesFromRequest(quotationId, handlingChargesRequest);
        var isValid = isHandlingChargeStepValid(handlingChargesDtoList);
        var handlingCharges = handlingChargesDtoList.stream().map(HandlingChargeDto::getHandlingCharge).toList();

        handlingChargeRepository.saveAll(handlingCharges);

        navigationItemService.updateProgress(quotationId, QuotationProgress.HANDLING_CHARGES, isValid);

        var progress = navigationItemService.getQuotationProgressByQuotationId(quotationId);
        return handlingChargeMapper.handlingChargesDtoListToHandlingCharges(handlingChargesDtoList)
                .progress(progress)
                .canCurrentUserEdit(true);
    }

    private Map<PartType, List<HandlingChargeDto>> groupHandlingChargeDtoListByPartType(List<HandlingChargeDto> handlingChargeDtoList) {
        return handlingChargeDtoList
                .stream()
                .collect(groupingBy(handlingChargeDto -> handlingChargeDto.getPart().getType()));
    }

    private List<HandlingChargeDto> getHandlingChargesOrCreateIfDoNotExist(Long quotationId) {
        var workscopeId = workscopeService.getDeepestWorkscopeIdByQuotationId(quotationId);
        var year = quotationService.getQuotationBaseYear(quotationId);

        var handlingChargeDtoList = handlingChargeRepository.findAllByQuotationId(quotationId, workscopeId, year);

        if (handlingChargeDtoList.isEmpty()) {
            var newHandlingCharges = new ArrayList<HandlingChargeEntity>();
            var routineMaterialHandlingCharge = new AtomicReference<HandlingChargeEntity>();
            var routineMaterialClusterId = clusterService.getClusterIdByName(ROUTINE_MATERIAL);
            var materialPricingItems = materialPricingItemService.getAllMaterialPricingItemsByQuotationId(quotationId);

            materialPricingItems.forEach(materialPricingItem -> {
                var newHandlingCharge = HandlingChargeEntity.builder()
                        .materialPricingItemId(materialPricingItem.getId())
                        .build();

                if (materialPricingItem.getClusterId().equals(routineMaterialClusterId)) {
                    routineMaterialHandlingCharge.set(newHandlingCharge);
                }

                newHandlingCharges.add(newHandlingCharge);
            });

            handlingChargeRepository.saveAll(newHandlingCharges);

            testrunItemService.setHandlingChargeId(quotationId, routineMaterialHandlingCharge.get().getId());

            handlingChargeDtoList = handlingChargeRepository.findAllByQuotationId(quotationId, workscopeId, String.valueOf(year));
        }

        var groupedHandlingChargeDtoListByPartType = groupHandlingChargeDtoListByPartType(handlingChargeDtoList);

        return squashDtoListByPartType(groupedHandlingChargeDtoListByPartType);
    }

    private List<HandlingChargeDto> getHandlingChargesFromRequest(Long quotationId, HandlingChargesRequest handlingChargesRequest) {
        var handlingChargesDtoList = getHandlingChargesOrCreateIfDoNotExist(quotationId);

        handlingChargeMapper.handlingChargesRequestToHandlingChargeDtoList(handlingChargesRequest, handlingChargesDtoList);

        return handlingChargesDtoList;
    }

    private boolean isMarginValueValid(Float value) {
        return value != null && value >= 0 && value < 100;
    }

    private boolean isAPartValid(HandlingChargeDto handlingChargeDto) {
        var quantity = handlingChargeDto.getQuantity();
        var handlingChargeEntity = handlingChargeDto.getHandlingCharge();

        var areMarginValuesValid = isMarginValueValid(handlingChargeEntity.getZ1Margin())
                && isMarginValueValid(handlingChargeEntity.getZ2Margin())
                && isMarginValueValid(handlingChargeEntity.getPmaMargin())
                && isMarginValueValid(handlingChargeEntity.getCsmMargin());

        var areCapValuesValid = false;

        if (quantity == 1) {
            areCapValuesValid = handlingChargeEntity.getLineItemCap() == null
                    && (handlingChargeEntity.getOneItemCap() == null
                    || handlingChargeEntity.getOneItemCap() > 0);
        } else if (quantity > 1) {
            areCapValuesValid = handlingChargeEntity.getOneItemCap() == null
                    && (handlingChargeEntity.getLineItemCap() == null
                    || handlingChargeEntity.getLineItemCap() > 0);
        }

        return areMarginValuesValid && areCapValuesValid;
    }

    private boolean isKitComponentPartsPackageValid(HandlingChargeDto handlingChargeDto) {
        var handlingChargeEntity = handlingChargeDto.getHandlingCharge();
        return isMarginValueValid(handlingChargeEntity.getZ1Margin())
                && isMarginValueValid(handlingChargeEntity.getZ2Margin())
                && isMarginValueValid(handlingChargeEntity.getPmaMargin())
                && handlingChargeEntity.getCsmMargin() == null
                && handlingChargeEntity.getOneItemCap() == null
                && handlingChargeEntity.getLineItemCap() == null;
    }

    private boolean isRoutineMaterialRepairMaterialValid(HandlingChargeDto handlingChargeDto) {
        var handlingChargeEntity = handlingChargeDto.getHandlingCharge();
        return isMarginValueValid(handlingChargeEntity.getZ1Margin())
                && handlingChargeEntity.getZ2Margin() == null
                && handlingChargeEntity.getPmaMargin() == null
                && handlingChargeEntity.getCsmMargin() == null
                && handlingChargeEntity.getOneItemCap() == null
                && handlingChargeEntity.getLineItemCap() == null;
    }

    private boolean isHandlingChargeValid(HandlingChargeDto handlingChargeDto) {
        var partType = handlingChargeDto.getPart().getType();
        return switch (partType) {
            case A_PART, LLP, CASE_AND_FRAME -> isAPartValid(handlingChargeDto);
            case KIT, COMPONENT, PARTS_PACKAGE -> isKitComponentPartsPackageValid(handlingChargeDto);
            default -> isRoutineMaterialRepairMaterialValid(handlingChargeDto);
        };
    }

    private boolean isHandlingChargeStepValid(List<HandlingChargeDto> handlingChargeDtoList) {
        var isValid = false;

        for (var handlingCharge : handlingChargeDtoList) {
            isValid = isHandlingChargeValid(handlingCharge);
            if (!isValid) {
                break;
            }
        }

        return isValid;
    }
}
