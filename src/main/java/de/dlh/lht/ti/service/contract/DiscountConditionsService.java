package de.dlh.lht.ti.service.contract;

import de.dlh.lht.ti.entity.MaterialDiscountExpendableEntity;
import de.dlh.lht.ti.entity.MaterialDiscountTypeEntity;
import de.dlh.lht.ti.entity.MaterialDiscountsConditionEntity;
import de.dlh.lht.ti.entity.VolumeBasedDiscountEntity;
import de.dlh.lht.ti.enums.DiscountLabourType;
import de.dlh.lht.ti.enums.PartType;
import java.util.Map;

public interface DiscountConditionsService {

    Map<String, VolumeBasedDiscountEntity> findAllVolumeBasedDiscountsByEngineIdPerYear(Long engineId);

    Map<PartType, Map<DiscountLabourType, MaterialDiscountTypeEntity>> findAllMaterialDiscountsByEngineId(Long engineId);

    Map<String, MaterialDiscountsConditionEntity> findAllMaterialDiscountsConditionsByEngineIdPerYear(Long engineId);

    Map<String, MaterialDiscountExpendableEntity> findAllMaterialDiscountsExpandableByEngineIdPerYear(Long engineId);

}