package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.entity.TestrunItemEntity;
import de.dlh.lht.ti.exception.EntityNotFoundException;
import de.dlh.lht.ti.mapper.TestrunItemMapper;
import de.dlh.lht.ti.model.RfpItem;
import de.dlh.lht.ti.model.RfpItemInput;
import de.dlh.lht.ti.repository.TestrunItemRepository;
import de.dlh.lht.ti.service.contract.ClusterService;
import de.dlh.lht.ti.service.contract.TestrunItemService;

import java.math.BigDecimal;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;


import static de.dlh.lht.ti.utils.Constants.TESTRUN_MATERIAL;
import static de.dlh.lht.ti.utils.ErrorMessages.TESTRUN_ITEM_WITH_QUOTATION_ID_NOT_FOUND_ERROR_MESSAGE;
import static de.dlh.lht.ti.utils.ErrorMessages.TESTRUN_ITEM_WITH_QUOTATION_ID_YEAR_AND_WORKSCOPE_ID_NOT_FOUND_ERROR_MESSAGE;

@Service
@RequiredArgsConstructor
public class TestrunItemServiceImpl implements TestrunItemService {

    private final TestrunItemRepository testrunItemRepository;

    private final ClusterService clusterService;

    private final TestrunItemMapper testrunItemMapper;

    @Override
    public TestrunItemEntity createTestrunItem(Long quotationEngineId) {
        var clusterId = clusterService.getClusterIdByName(TESTRUN_MATERIAL);
        var newTestrunItem = createTestrunItem(quotationEngineId, clusterId);
        return testrunItemRepository.save(newTestrunItem);
    }

    @Override
    public RfpItem getRfpItemByQuotationIdYearAndWorkscopeId(Long quotationId, String year, Long workscopeId, BigDecimal exchangeRate) {
        var testrunItemDto = testrunItemRepository.findDtoByQuotationIdYearAndWorkscopeId(
                quotationId,
                year,
                workscopeId).orElseThrow(() -> new EntityNotFoundException(
                String.format(
                        TESTRUN_ITEM_WITH_QUOTATION_ID_YEAR_AND_WORKSCOPE_ID_NOT_FOUND_ERROR_MESSAGE,
                        quotationId,
                        year,
                        workscopeId)));
        return testrunItemMapper.testrunDtoToRfpItem(testrunItemDto, exchangeRate);
    }

    @Override
    public void setHandlingChargeId(Long quotationId, Long handlingChargeId) {
        var testrunItem = getByQuotationId(quotationId);
        testrunItem.setHandlingChargeId(handlingChargeId);
        testrunItemRepository.save(testrunItem);
    }

    @Override
    public void setPrice(Long quotationId, RfpItemInput rfpItemInput) {
        var testrunItem = getByQuotationId(quotationId);
        if (rfpItemInput.getPrice() != null) {
            testrunItem.setPrice(BigDecimal.valueOf(rfpItemInput.getPrice()));
        } else {
            testrunItem.setPrice(null);
        }

        testrunItemRepository.save(testrunItem);
    }

    private TestrunItemEntity getByQuotationId(Long quotationId) {
        return testrunItemRepository.findByQuotationId(quotationId)
                .orElseThrow(() -> new EntityNotFoundException(
                        String.format(TESTRUN_ITEM_WITH_QUOTATION_ID_NOT_FOUND_ERROR_MESSAGE, quotationId)));
    }

    private TestrunItemEntity createTestrunItem(Long quotationEngineId, Long clusterId) {
        return TestrunItemEntity.builder()
                .quotationEngineId(quotationEngineId)
                .clusterId(clusterId)
                .build();
    }
}
