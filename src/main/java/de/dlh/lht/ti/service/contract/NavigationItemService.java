package de.dlh.lht.ti.service.contract;

import de.dlh.lht.ti.enums.QuotationProgress;
import de.dlh.lht.ti.model.Progress;

public interface NavigationItemService {

    void createNavigationItems(Long quotationId, boolean isRoutineFixedPrices);

    Progress getQuotationProgressByQuotationId(Long quotationId);

    void updateProgress(Long quotationId, QuotationProgress progressStep, Boolean isValid);
}
