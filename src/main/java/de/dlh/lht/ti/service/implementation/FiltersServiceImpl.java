package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.model.QuotationFilters;
import de.dlh.lht.ti.model.QuotationStatus;
import de.dlh.lht.ti.service.contract.CustomerService;
import de.dlh.lht.ti.service.contract.EngineService;
import de.dlh.lht.ti.service.contract.FiltersService;
import de.dlh.lht.ti.service.contract.UserService;
import java.util.Arrays;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class FiltersServiceImpl implements FiltersService {

    private final CustomerService customerService;
    private final EngineService engineService;
    private final UserService userService;

    @Override
    public QuotationFilters getQuotationFilters() {
        var engines = engineService.getEnginesAsQuotationFilters();
        var customers = customerService.getCustomersAsQuotationFilters();
        var owners = userService.getAllQuotationOwnerUsers();

        return new QuotationFilters()
                .engineTypes(engines)
                .customers(customers)
                .owners(owners)
                .quotationStatuses(
                        Arrays.stream(QuotationStatus.values()).map(QuotationStatus::getValue).toList()
                );
    }
}
