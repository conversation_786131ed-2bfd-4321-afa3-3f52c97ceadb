package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.dto.CalculationResultDto;
import de.dlh.lht.ti.dto.discounts.DiscountWorkscopeDto;
import de.dlh.lht.ti.dto.discounts.MaterialDiscountDto;
import de.dlh.lht.ti.dto.WorkscopeDto;
import de.dlh.lht.ti.entity.PartMetadataEntity;
import de.dlh.lht.ti.importer.model.PartMetadataRaw;
import de.dlh.lht.ti.mapper.PartMetadataMapper;
import de.dlh.lht.ti.repository.PartMetadataRepository;
import de.dlh.lht.ti.service.contract.PartMetadataService;
import de.dlh.lht.ti.utils.Calculations;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class PartMetadataServiceImpl implements PartMetadataService {

    private final PartMetadataMapper partMetadataMapper;

    private final PartMetadataRepository partMetadataRepository;

    @Override

    public List<PartMetadataEntity> createPartMetadata(List<PartMetadataEntity> partMetadata) {
        return partMetadataRepository.saveAll(partMetadata);
    }

    @Override
    public List<String> findAllUniqueYearsOfPartMetadataByQuotationId(Long quotationId) {
        return partMetadataRepository.findAllUniqueYearsOfPartMetadataByQuotationId(quotationId);
    }

    @Override
    public List<PartMetadataEntity> partMetadataRawListToPartMetadataEntityListV2(
            List<PartMetadataRaw> partMetadata,
            Long materialPricingItemId,
            Map<String, Long> allEngineWorkscopeIdsByName) {
        return partMetadataMapper.partMetadataRawListToPartMetadataEntityList(
                partMetadata,
                materialPricingItemId,
                allEngineWorkscopeIdsByName);
    }

    @Override
    public Map<WorkscopeDto, CalculationResultDto> getAllProductionCostByWorkscopeDtoByQuotationId(Long quotationId) {
        var productionCostDtos = new ArrayList<>(Stream.of(
                        partMetadataRepository.findAllZ1ProductionCostMetadataByQuotationId(quotationId),
                        partMetadataRepository.findAllZ2ProductionCostMetadataByQuotationId(quotationId),
                        partMetadataRepository.findAllCsmProductionCostMetadataByQuotationId(quotationId),
                        partMetadataRepository.findAllPmaProductionCostMetadataByQuotationId(quotationId))
                .flatMap(List::stream)
                .toList());

        return Calculations.calculateProductionCost(productionCostDtos);
    }

    @Override
    public Map<DiscountWorkscopeDto, Map<String, List<MaterialDiscountDto>>> getAllMaterialDiscountsByQuotationId(
            Long quotationId
    ) {
        var discountMetadata = partMetadataRepository.findAllMaterialDiscountsByQuotationId(quotationId);

        return discountMetadata.stream().collect(Collectors.groupingBy(
                MaterialDiscountDto::getWorkscopeDto,
                Collectors.groupingBy(MaterialDiscountDto::getYear)
        ));
    }
}
