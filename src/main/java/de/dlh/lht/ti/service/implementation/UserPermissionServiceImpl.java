package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.auth.contract.UserPrincipalProvider;
import de.dlh.lht.ti.auth.roles.Role;
import de.dlh.lht.ti.service.contract.UserPermissionService;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;



@Service
@RequiredArgsConstructor
public class UserPermissionServiceImpl implements UserPermissionService {

    private final UserPrincipalProvider userPrincipalProvider;

    @Override
    public boolean isUserOwnerOrAdmin(String currentUNumber) {
        var customPrincipalInfo = userPrincipalProvider.getUserPrincipal();
        var userUNumber = customPrincipalInfo.getUNumber();
        var userRole = userPrincipalProvider.getRoles();

        return Objects.equals(userUNumber, currentUNumber) || userRole.contains(Role.ADMIN_USER);
    }

    @Override
    public boolean isViewOnlyUser() {
        var userRoles = userPrincipalProvider.getRoles();
        return userRoles.contains(Role.VIEW_ONLY_USER);
    }
}
