package de.dlh.lht.ti.service.contract;

import de.dlh.lht.ti.dto.importer.CustomerDto;
import de.dlh.lht.ti.entity.CustomerEntity;
import de.dlh.lht.ti.importer.model.CustomerRaw;
import java.util.List;
import java.util.Map;

public interface CustomerService {

    CustomerEntity createCustomerEntity(CustomerDto customerDto);

    Map<CustomerDto, CustomerEntity> getCustomerEntityByCustomerDtoMap();

    CustomerDto customerRawToCustomerDto(CustomerRaw customer);

    List<String> getCustomersAsQuotationFilters();
}
