package de.dlh.lht.ti.service.contract;

import de.dlh.lht.ti.dto.CalculationResultDto;
import de.dlh.lht.ti.dto.WorkscopeDto;
import de.dlh.lht.ti.entity.PartMetadataEntity;
import de.dlh.lht.ti.entity.SubcontractMetadataEntity;
import de.dlh.lht.ti.entity.TaskMetadataEntity;
import de.dlh.lht.ti.entity.TestrunMetadataEntity;
import java.util.List;
import java.util.Map;

public interface ProductionCostService {

    Map<WorkscopeDto, CalculationResultDto> getProductionCostsByWorkscopeDtoMap(Long quotationId);

    CalculationResultDto getMaterialProductionCost(List<PartMetadataEntity> metadata);

    CalculationResultDto getLabourProductionCost(List<TaskMetadataEntity> metadata);

    CalculationResultDto getSubContractProductionCost(List<SubcontractMetadataEntity> metadata);

    CalculationResultDto getTestrunProductionCost(List<TestrunMetadataEntity> metadata);
}
