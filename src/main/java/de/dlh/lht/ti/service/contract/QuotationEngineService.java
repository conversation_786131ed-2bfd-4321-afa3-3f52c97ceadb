package de.dlh.lht.ti.service.contract;

import de.dlh.lht.ti.dto.QuotationsQueryParametersDto;
import de.dlh.lht.ti.entity.EngineEntity;
import de.dlh.lht.ti.entity.QuotationEngineEntity;
import de.dlh.lht.ti.entity.QuotationEntity;
import de.dlh.lht.ti.entity.WorkscopeEntity;
import de.dlh.lht.ti.model.QuotationDetails;
import de.dlh.lht.ti.model.QuotationsPage;
import java.util.List;

public interface QuotationEngineService {

    QuotationEngineEntity createQuotationEngineEntity(
            QuotationEntity quotationEntity,
            EngineEntity engineEntity,
            List<WorkscopeEntity> workscopeEntities);

    QuotationDetails getQuotationDetails(Long quotationId);

    QuotationsPage getQuotationsPage(QuotationsQueryParametersDto quotationsQueryParametersDto);

    String getQuotationEngineNameByQuotationId(Long quotationId);
}
