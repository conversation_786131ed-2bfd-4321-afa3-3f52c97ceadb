package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.dto.SubcontractDto;
import de.dlh.lht.ti.dto.importer.PartDto;
import de.dlh.lht.ti.entity.SubcontractPricingItemEntity;
import de.dlh.lht.ti.importer.model.SubcontractPricingItemRaw;
import de.dlh.lht.ti.repository.SubcontractPricingItemRepository;
import de.dlh.lht.ti.service.contract.PartService;
import de.dlh.lht.ti.service.contract.SubcontractPricingItemService;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class SubcontractPricingItemServiceImpl implements SubcontractPricingItemService {

    private final SubcontractPricingItemRepository subcontractPricingItemRepository;

    private final PartService partService;

    @Override
    public List<SubcontractPricingItemEntity> createSubcontractPricingItems(
            List<SubcontractPricingItemRaw> rawSubcontractPricingItems,
            Long engineId,
            Long quotationEngineId,
            Map<PartDto, Long> partIdByPartDtoMap,
            Map<Long, Map<Long, Long>> clusterIdByEngineIdByPartIdMap) {
        if (rawSubcontractPricingItems == null || rawSubcontractPricingItems.isEmpty()) {
            return new ArrayList<>();
        }

        var newSubcontractPricingItems = new HashMap<Long, SubcontractPricingItemEntity>();

        rawSubcontractPricingItems.forEach(subcontractPricingItemRaw -> {
            var partId = partService.findPartIdByNameAndType(subcontractPricingItemRaw.getPart(), partIdByPartDtoMap);
            var clusterId = clusterIdByEngineIdByPartIdMap.get(engineId).get(partId);
            var subcontractPricingItem = createSubcontractPricingItem(quotationEngineId, clusterId);
            newSubcontractPricingItems.putIfAbsent(clusterId, subcontractPricingItem);
        });

        return subcontractPricingItemRepository.saveAll(newSubcontractPricingItems.values());
    }

    @Override
    public Map<Long, SubcontractPricingItemEntity> getSubcontractPricingItemsByIdMapByQuotationId(Long quotationId) {
        return subcontractPricingItemRepository
                .findAllByQuotationId(quotationId)
                .stream()
                .collect(Collectors.toMap(SubcontractPricingItemEntity::getId, Function.identity()));
    }

    @Override
    public List<SubcontractDto> getAllSubcontractDtosByQuotationId(Long quotationId) {
        return subcontractPricingItemRepository.findAllSubcontractDtosByQuotationId(quotationId);
    }

    @Override
    public void updateSubcontractPricingItems(List<SubcontractPricingItemEntity> subcontractPricingItemEntities) {
        subcontractPricingItemRepository.saveAll(subcontractPricingItemEntities);
    }

    private SubcontractPricingItemEntity createSubcontractPricingItem(Long quotationEngineId, Long clusterId) {
        return SubcontractPricingItemEntity.builder()
                .quotationEngineId(quotationEngineId)
                .clusterId(clusterId)
                .build();
    }
}
