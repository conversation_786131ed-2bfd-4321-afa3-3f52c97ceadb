package de.dlh.lht.ti.service.contract;

import de.dlh.lht.ti.dto.CalculationResultDto;
import de.dlh.lht.ti.dto.discounts.DiscountWorkscopeDto;
import de.dlh.lht.ti.dto.discounts.MaterialDiscountDto;
import de.dlh.lht.ti.dto.WorkscopeDto;
import de.dlh.lht.ti.entity.PartMetadataEntity;
import de.dlh.lht.ti.importer.model.PartMetadataRaw;
import java.util.List;
import java.util.Map;

public interface PartMetadataService {

    List<PartMetadataEntity> createPartMetadata(List<PartMetadataEntity> partMetadata);

    List<String> findAllUniqueYearsOfPartMetadataByQuotationId(Long quotationId);

    List<PartMetadataEntity> partMetadataRawListToPartMetadataEntityListV2(
            List<PartMetadataRaw> partMetadata,
            Long materialPricingItemId,
            Map<String, Long> allEngineWorkscopeIdsByName);

    Map<WorkscopeDto, CalculationResultDto> getAllProductionCostByWorkscopeDtoByQuotationId(Long quotationId);

    Map<DiscountWorkscopeDto, Map<String, List<MaterialDiscountDto>>> getAllMaterialDiscountsByQuotationId(Long quotationId);
}
