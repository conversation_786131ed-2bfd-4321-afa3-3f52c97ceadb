package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.service.contract.FileService;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class FileServiceImpl implements FileService {

    @Override
    public String readFromFile(String filePath) {
        var path = Paths.get(filePath);
        try {
            var fileContentBytes = Files.readAllBytes(path);
            return new String(fileContentBytes);
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    @Override
    public void deleteFile(String filePath) {
        try {
            var path = Paths.get(filePath);
            Files.delete(path);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}