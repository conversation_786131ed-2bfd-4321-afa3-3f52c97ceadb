package de.dlh.lht.ti.service.contract;

import de.dlh.lht.ti.dto.importer.WorkscopeDto;
import de.dlh.lht.ti.entity.WorkscopeEntity;
import de.dlh.lht.ti.importer.model.WorkscopeRaw;
import java.util.List;
import java.util.Map;

public interface WorkscopeService {

    WorkscopeEntity createWorkscope(WorkscopeDto workscopeDto);

    List<Long> getSystemWorkscopeIdsByQuotationId(Long quotationId);

    List<WorkscopeEntity> getWorkscopesByQuotationId(Long quotationId);

    Long getDeepestWorkscopeIdByQuotationId(Long quotationId);

    Map<WorkscopeDto, WorkscopeEntity> getWorkscopeEntityByWorkscopeDtoMap();

    List<WorkscopeDto> workscopeRawListToWorkscopeDtoList(List<WorkscopeRaw> rawWorkscopes);
}
