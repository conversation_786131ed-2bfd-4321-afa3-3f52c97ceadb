package de.dlh.lht.ti.service.contract;

import de.dlh.lht.ti.dto.SubcontractDto;
import de.dlh.lht.ti.dto.importer.PartDto;
import de.dlh.lht.ti.entity.SubcontractPricingItemEntity;
import de.dlh.lht.ti.importer.model.SubcontractPricingItemRaw;
import java.util.List;
import java.util.Map;

public interface SubcontractPricingItemService {

    List<SubcontractPricingItemEntity> createSubcontractPricingItems(
            List<SubcontractPricingItemRaw> rawSubcontractPricingItems,
            Long engineId,
            Long quotationEngineId,
            Map<PartDto, Long> partIdByPartDtoMap,
            Map<Long, Map<Long, Long>> clusterIdByEngineIdByPartIdMap);

    Map<Long, SubcontractPricingItemEntity> getSubcontractPricingItemsByIdMapByQuotationId(Long quotationId);

    List<SubcontractDto> getAllSubcontractDtosByQuotationId(Long quotationId);

    void updateSubcontractPricingItems(List<SubcontractPricingItemEntity> subcontractPricingItemEntities);
}
