package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.entity.MaterialDiscountExpendableEntity;
import de.dlh.lht.ti.entity.MaterialDiscountTypeEntity;
import de.dlh.lht.ti.entity.MaterialDiscountsConditionEntity;
import de.dlh.lht.ti.entity.VolumeBasedDiscountEntity;
import de.dlh.lht.ti.enums.DiscountLabourType;
import de.dlh.lht.ti.enums.DiscountPartType;
import de.dlh.lht.ti.enums.PartType;
import de.dlh.lht.ti.repository.MaterialDiscountExpendableRepository;
import de.dlh.lht.ti.repository.MaterialDiscountRepository;
import de.dlh.lht.ti.repository.MaterialDiscountsConditionRepository;
import de.dlh.lht.ti.repository.VolumeBasedDiscountsRepository;
import de.dlh.lht.ti.service.contract.DiscountConditionsService;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class DiscountConditionsServiceImpl implements DiscountConditionsService {

    private final MaterialDiscountRepository materialDiscountRepository;
    private final MaterialDiscountsConditionRepository materialDiscountsConditionRepository;
    private final MaterialDiscountExpendableRepository materialDiscountExpendableRepository;
    private final VolumeBasedDiscountsRepository volumeBasedDiscountsRepository;

    @Override
    public Map<String, VolumeBasedDiscountEntity> findAllVolumeBasedDiscountsByEngineIdPerYear(Long engineId) {
        return volumeBasedDiscountsRepository.findAllByEngineId(engineId).stream().collect(Collectors.toMap(
                VolumeBasedDiscountEntity::getYear,
                Function.identity(),
                (entityOne, entityTwo) -> entityOne
        ));
    }

    @Override
    public Map<PartType, Map<DiscountLabourType, MaterialDiscountTypeEntity>> findAllMaterialDiscountsByEngineId(Long engineId) {
        return materialDiscountRepository.findAllByEngineId(engineId)
                .stream().collect(Collectors.groupingBy(
                        materialDiscountTypeEntity -> mapDiscountPartTypeToPartType(
                                materialDiscountTypeEntity.getDiscountLabourType(),
                                materialDiscountTypeEntity.getDiscountPartType()
                        ),
                        Collectors.toMap(
                                MaterialDiscountTypeEntity::getDiscountLabourType,
                                Function.identity(),
                                (discountOne, discountTwo) -> discountOne
                        )
        ));
    }

    @Override
    public Map<String, MaterialDiscountsConditionEntity> findAllMaterialDiscountsConditionsByEngineIdPerYear(Long engineId) {
        return materialDiscountsConditionRepository.findAllByEngineId(engineId)
                .stream().collect(Collectors.toMap(
                        MaterialDiscountsConditionEntity::getYear,
                        Function.identity(),
                        (entity1, entity2) -> entity1
                ));
    }

    @Override
    public Map<String, MaterialDiscountExpendableEntity> findAllMaterialDiscountsExpandableByEngineIdPerYear(Long engineId) {
        return materialDiscountExpendableRepository.findAllByEngineId(engineId)
                .stream().collect(Collectors.toMap(
                        MaterialDiscountExpendableEntity::getYear,
                        Function.identity(),
                        (entity1, entity2) -> entity1
                ));
    }

    private PartType mapDiscountPartTypeToPartType(
            DiscountLabourType discountLabourType,
            DiscountPartType discountPartType
    ) {
        if (discountLabourType == DiscountLabourType.ROUTINE) {
            return PartType.ROUTINE_MATERIAL;
        }
        if (discountLabourType == DiscountLabourType.REPAIR) {
            return PartType.NON_ROUTINE_MATERIAL;
        }

        return switch (discountPartType) {
            case A_PART -> PartType.A_PART;
            case CASE_FRAME -> PartType.CASE_AND_FRAME;
            case LLP -> PartType.LLP;
            case PARTS_PACKAGE -> PartType.PARTS_PACKAGE;
            case MK_16_KIT -> PartType.KIT;
            case COMPONENT_PACKAGE -> PartType.COMPONENT;
            case ROUTINE_MATERIAL -> PartType.ROUTINE_MATERIAL;
        };
    }
}