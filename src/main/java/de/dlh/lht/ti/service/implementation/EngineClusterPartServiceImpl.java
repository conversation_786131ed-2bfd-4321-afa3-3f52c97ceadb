package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.dto.importer.EngineClusterPartDto;
import de.dlh.lht.ti.repository.EngineClusterPartRepository;
import de.dlh.lht.ti.service.contract.EngineClusterPartService;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;


import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toMap;

@Service
@RequiredArgsConstructor
public class EngineClusterPartServiceImpl implements EngineClusterPartService {

    private final EngineClusterPartRepository engineClusterPartRepository;

    @Override
    public Map<Long, Map<Long, Long>> getClusterIdByPartIdByEngineIdMap() {
        return engineClusterPartRepository
                .findAllEngineClusterPartDtos()
                .stream()
                .collect(groupingBy(EngineClusterPartDto::getEngineId,
                        toMap(EngineClusterPartDto::getPartId, EngineClusterPartDto::getClusterId)));
    }
}
