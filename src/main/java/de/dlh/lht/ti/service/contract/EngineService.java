package de.dlh.lht.ti.service.contract;

import de.dlh.lht.ti.dto.importer.EngineDto;
import de.dlh.lht.ti.entity.EngineEntity;
import java.util.List;
import java.util.Map;

public interface EngineService {

    EngineDto engineNameToEngineDto(String name);

    Map<String, EngineEntity> getEngineEntityByEngineNameMap();

    List<String> getEnginesAsQuotationFilters();

    String getEngineNameByQuotationId(Long quotationId);

    EngineEntity getEngineByQuotationId(Long quotationId);

    Long getEngineIdByQuotationId(Long quotationId);
}
