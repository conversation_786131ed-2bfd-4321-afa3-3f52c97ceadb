package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.dto.EscalationPricingDto;
import de.dlh.lht.ti.entity.EscalationPricingEntity;
import de.dlh.lht.ti.enums.QuotationProgress;
import de.dlh.lht.ti.exception.EntityNotFoundException;
import de.dlh.lht.ti.exception.RfpContractNotSelectedException;
import de.dlh.lht.ti.exception.SaveEscalationPricingNotAllowedException;
import de.dlh.lht.ti.mapper.EscalationMapper;
import de.dlh.lht.ti.model.*;
import de.dlh.lht.ti.repository.EscalationPricingRepository;
import de.dlh.lht.ti.service.contract.*;
import de.dlh.lht.ti.utils.ContractYearsUtil;
import de.dlh.lht.ti.utils.Validations;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

import static de.dlh.lht.ti.utils.ErrorMessages.*;
import static de.dlh.lht.ti.utils.EscalationConstants.*;

@Service
@RequiredArgsConstructor
public class EscalationPricingServiceImpl implements EscalationPricingService {

    private final EscalationMapper escalationMapper;

    private final EscalationPricingRepository escalationPricingRepository;

    private final EngineService engineService;
    private final EscalationPricingDefaultsService escalationPricingDefaultsService;
    private final NavigationItemService navigationItemService;
    private final UserPermissionService userPermissionService;
    private final UserService userService;
    private final QuotationService quotationService;

    @Override
    public EscalationsPricing getEscalationPricing(Long quotationId) {
        var canCurrentUserEdit = canCurrentUserEdit(quotationId);
        var isRfpContractSelected = quotationService.isRfpContractSelected(quotationId);
        var escalations = getOrCreateEscalationPricing(quotationId, isRfpContractSelected);

        var areEscalationsValid = areEscalationsValid(escalations, isRfpContractSelected);
        navigationItemService.updateProgress(quotationId, QuotationProgress.PRICING_ESCALATION, areEscalationsValid);
        var progress = navigationItemService.getQuotationProgressByQuotationId(quotationId);

        return new EscalationsPricing()
                .progress(progress)
                .isRfpContractSelected(isRfpContractSelected)
                .canCurrentUserEdit(canCurrentUserEdit)
                .escalationsPricing(escalations);
    }

    @Override
    public EscalationsPricing saveEscalationPricing(Long quotationId, EscalationPricingRequest escalationPricing) {
        var isRfpContractSelected = quotationService.isRfpContractSelected(quotationId);
        Validations.escalationInputAssertion(escalationPricing, isRfpContractSelected);

        var currentOwnerUNumber = userService.getCurrentOwnerUNumberByQuotationId(quotationId);
        if (!userPermissionService.isUserOwnerOrAdmin(currentOwnerUNumber)) {
            throw new SaveEscalationPricingNotAllowedException(
                    String.format(SAVE_ESCALATION_PRICING_NOT_ALLOWED_ERROR_MESSAGE, currentOwnerUNumber));
        }

        var escalationPricingInputs = escalationPricing.getEscalationInputs();

        updateEscalationPricing(escalationPricingInputs, quotationId, isRfpContractSelected);

        var escalations = getOrCreateEscalationPricing(quotationId, isRfpContractSelected);

        var areEscalationsValid = areEscalationsValid(escalations, isRfpContractSelected);

        navigationItemService.updateProgress(quotationId, QuotationProgress.PRICING_ESCALATION, areEscalationsValid);

        var progress = navigationItemService.getQuotationProgressByQuotationId(quotationId);

        return new EscalationsPricing()
                .progress(progress)
                .isRfpContractSelected(isRfpContractSelected)
                .canCurrentUserEdit(true)
                .escalationsPricing(escalations);
    }

    private List<Escalation> getOrCreateEscalationPricing(Long quotationId, boolean isRfpContractSelected) {
        var escalationPriceDtoList = escalationPricingRepository.findAllWithMaterialDefaultsByQuotationId(quotationId, isRfpContractSelected);

        if (escalationPriceDtoList == null || escalationPriceDtoList.isEmpty()) {
            escalationPriceDtoList = createEscalationPricingEntityList(quotationId);
        }

        escalationPricingDefaultsService.setDefaultValues(escalationPriceDtoList, isRfpContractSelected);

        return escalationMapper.escalationPricingDtoListToEscalationList(escalationPriceDtoList);
    }

    private List<EscalationPricingDto> createEscalationPricingEntityList(Long quotationId) {
        var quotation = quotationService.getQuotationEntityById(quotationId);
        var engineId = engineService.getEngineByQuotationId(quotationId).getId();

        var contractStart = quotation.getContractStart();
        var contractEnd = quotation.getContractEnd();
        var contractYears = ContractYearsUtil.extractYearsFromContract(contractStart, contractEnd, true);

        var escalationPricingEntityList = contractYears.stream().map(year -> createEscalationPriceEntity(year, engineId, quotationId)).toList();

        var savedEntities = escalationPricingRepository.saveAll(escalationPricingEntityList);

        return savedEntities.stream()
                .map(entity -> escalationPricingDefaultsService.mapEntityToDtoWithMaterialPricesDefaultValue(entity, engineId))
                .toList();
    }

    private EscalationPricingEntity createEscalationPriceEntity(String year, Long engineId, Long quotationId) {
        var materialPricingDefaultValue = escalationPricingDefaultsService.getEscalationsMaterialPricesDefault(year, engineId);
        var hcSubcontractPricesDefaultValue = escalationPricingDefaultsService.getEscalationsSubcontractPricesDefault(year);
        var isRfpContractSelected = quotationService.isRfpContractSelected(quotationId);

        var escalationPricingEntity = new EscalationPricingEntity();
        escalationPricingEntity.setLabourPrices(LABOUR_PRICE_ESCALATION_DEFAULT);
        escalationPricingEntity.setEparPrices(EPAR_PRICE_ESCALATION_DEFAULT);
        escalationPricingEntity.setHcMaterialPrices(materialPricingDefaultValue);
        escalationPricingEntity.setRfpLabour(isRfpContractSelected ? LABOUR_PRICE_ESCALATION_DEFAULT : null);
        escalationPricingEntity.setHcSubcontractPrices(hcSubcontractPricesDefaultValue);

        escalationPricingEntity.setQuotationId(quotationId);
        escalationPricingEntity.setYear(year);

        return escalationPricingEntity;
    }

    private void updateEscalationPricing(List<EscalationPricingInput> escalationPricingInputs, Long quotationId, Boolean isRfpContractSelected) {
        var escalationPricingEntityList = escalationPricingRepository.findAllByQuotationId(quotationId);

        var entityMap = escalationPricingEntityList.stream()
                .collect(Collectors.toMap(EscalationPricingEntity::getId, Function.identity()));

        var updatedEscalationPricingEntityList = escalationPricingInputs.stream()
                .map(escalationInput -> {
                    var entity = entityMap.get(escalationInput.getId());
                    if (entity == null) {
                        throw new EntityNotFoundException(String.format(ESCALATION_ENTITY_NOT_FOUND_ERROR_MESSAGE, escalationInput.getId(), quotationId));
                    }
                    if (!isRfpContractSelected && escalationInput.getRfpLabour() != null) {
                        throw new SaveEscalationPricingNotAllowedException(String.format(ESCALATION_PRICING_RFP_CONTRACT_NOT_SELECTED_ERROR_MESSAGE, quotationId));
                    }
                    return escalationMapper.escalationPricingInputToEscalationPricingEntity(escalationInput, entity);
                })
                .collect(Collectors.toList());


        escalationPricingRepository.saveAll(updatedEscalationPricingEntityList);
    }

    private boolean areEscalationsValid(List<Escalation> escalations, boolean isRfpContractSelected) {
        return escalations.stream().allMatch(escalation -> isEscalationValid(escalation, isRfpContractSelected));
    }

    private boolean isEscalationValid(Escalation escalation, boolean isRfpContractSelected) {
        var values = escalation.getValues();
        return values != null
                && values.getEparPrices().getValue() != null
                && values.getLabourPrices().getValue() != null
                && values.getHcMaterialPrices().getValue() != null
                && values.getHcSubcontractPrices().getValue() != null
                && ((isRfpContractSelected && values.getRfpLabour().getValue() != null) || (!isRfpContractSelected && values.getRfpLabour().getValue() == null));
    }

    private boolean canCurrentUserEdit(Long quotationId) {
        var currentOwnerUNumber = userService.getCurrentOwnerUNumberByQuotationId(quotationId);
        return userPermissionService.isUserOwnerOrAdmin(currentOwnerUNumber);
    }
}
