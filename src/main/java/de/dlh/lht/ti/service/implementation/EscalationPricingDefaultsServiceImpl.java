package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.dto.EscalationPricingDto;
import de.dlh.lht.ti.entity.EscalationPricingEntity;
import de.dlh.lht.ti.exception.EscalationDefaultValueMissingException;
import de.dlh.lht.ti.mapper.EscalationMapper;
import de.dlh.lht.ti.repository.EscalationsMaterialPricesDefaultsEntityRepository;
import de.dlh.lht.ti.service.contract.*;
import de.dlh.lht.ti.utils.SubcontractEscalationsDefaults;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

import static de.dlh.lht.ti.utils.ErrorMessages.ESCALATION_PRICING_SUBCONTRACT_MISSING_DEFAULT_VALUE_ERROR_MESSAGE;
import static de.dlh.lht.ti.utils.EscalationConstants.*;

@Service
@RequiredArgsConstructor
public class EscalationPricingDefaultsServiceImpl implements EscalationPricingDefaultsService {

    private final EscalationMapper escalationMapper;
    
    private final EscalationsMaterialPricesDefaultsEntityRepository escalationsMaterialPricesDefaultsEntityRepository;

    private final SubcontractEscalationsDefaults subcontractEscalationsDefaults = new SubcontractEscalationsDefaults();

    @Override
    public EscalationPricingDto mapEntityToDtoWithMaterialPricesDefaultValue(EscalationPricingEntity entity, Long engineId) {
        var defaultValue = escalationsMaterialPricesDefaultsEntityRepository.findByYearAndEngineId(entity.getYear(), engineId);
        var dto = escalationMapper.escalationPricingEntityToEscalationPricingDto(entity);
        dto.setHcMaterialPricesDefault(defaultValue);
        return dto;
    }

    @Override
    public BigDecimal getEscalationsMaterialPricesDefault(String year, Long engineId) {
        return escalationsMaterialPricesDefaultsEntityRepository
                .findByYearAndEngineId(year, engineId);
    }

    @Override
    public BigDecimal getEscalationsSubcontractPricesDefault(String year) {
        return subcontractEscalationsDefaults.getPercentageByYear(year)
                .orElseThrow(() -> new EscalationDefaultValueMissingException(ESCALATION_PRICING_SUBCONTRACT_MISSING_DEFAULT_VALUE_ERROR_MESSAGE + year));
    }

    @Override
    public void setDefaultValues(List<EscalationPricingDto> escalationPriceDtoList, Boolean isRfpContractSelected) {
        escalationPriceDtoList.forEach(escalationDto -> {
            var year = escalationDto.getYear();
            var subcontractEscalationsDefault = getEscalationsSubcontractPricesDefault(year);
            if (subcontractEscalationsDefault == null) {
                throw new EscalationDefaultValueMissingException(ESCALATION_PRICING_SUBCONTRACT_MISSING_DEFAULT_VALUE_ERROR_MESSAGE);
            }
            escalationDto.setHcSubcontractPricesDefault(subcontractEscalationsDefault);
            escalationDto.setLabourPricesDefault(LABOUR_PRICE_ESCALATION_DEFAULT);
            escalationDto.setEparPricesDefault(EPAR_PRICE_ESCALATION_DEFAULT);
            escalationDto.setRfpLabourDefault(isRfpContractSelected ? RFP_LABOUR_PRICE_ESCALATION_DEFAULT : null);
        });
    }
}
