package de.dlh.lht.ti.service.contract;

import de.dlh.lht.ti.dto.ContractTypesDto;
import de.dlh.lht.ti.dto.QuotationsQueryParametersDto;
import de.dlh.lht.ti.dto.RfpQuotationDto;
import de.dlh.lht.ti.entity.CustomerEntity;
import de.dlh.lht.ti.entity.ProjectEntity;
import de.dlh.lht.ti.entity.QuotationEntity;
import de.dlh.lht.ti.importer.model.QuotationRaw;
import de.dlh.lht.ti.model.QuotationDetails;
import de.dlh.lht.ti.model.QuotationsPage;
import java.math.BigDecimal;

public interface QuotationService {

    QuotationEntity createQuotation(QuotationRaw quotationRaw, ProjectEntity project, CustomerEntity customer);

    String getQuotationBaseYear(Long quotationId);

    QuotationEntity getQuotationEntityById(Long quotationId);

    RfpQuotationDto getRfpQuotationDtoById(Long quotationId);

    QuotationsPage getQuotations(QuotationsQueryParametersDto quotationsQueryParametersDto);

    QuotationDetails getQuotationDetails(Long quotationId);

    BigDecimal getQuotationUsdExchangeRate(Long quotationId);

    QuotationDetails beginQuotation(Long importerQuotationId, ContractTypesDto contractTypesDto);

    boolean quotationEntityExistsByPositionAndVersionAndScenarioAndProjectId(int position, int version, int scenario, Long projectId);

    boolean isRfpContractSelected(Long quotationId);
}
