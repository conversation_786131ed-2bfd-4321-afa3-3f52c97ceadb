package de.dlh.lht.ti.service.contract;

import de.dlh.lht.ti.dto.CalculationResultDto;
import de.dlh.lht.ti.dto.WorkscopeDto;
import de.dlh.lht.ti.dto.importer.PartDto;
import de.dlh.lht.ti.entity.SubcontractMetadataEntity;
import de.dlh.lht.ti.entity.SubcontractPricingItemEntity;
import de.dlh.lht.ti.importer.model.SubcontractPricingItemRaw;
import java.util.List;
import java.util.Map;

public interface SubcontractMetadataService {

    List<SubcontractMetadataEntity> createSubcontractMetadata(
            List<SubcontractPricingItemRaw> rawSubcontractPricingItems,
            List<SubcontractPricingItemEntity> subcontractPricingItems,
            Long engineId,
            Map<PartDto, Long> partIdByPartDtoMap,
            Map<Long, Map<Long, Long>> clusterIdByEngineIdByPartIdMap,
            Map<String, Long> workscopeIdsByNameMap);

    Map<WorkscopeDto, CalculationResultDto> getAllProductionCostByWorkscopeDtoByQuotationId(Long quotationId);
}
