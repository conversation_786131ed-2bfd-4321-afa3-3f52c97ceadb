package de.dlh.lht.ti.service.contract;

import de.dlh.lht.ti.entity.CleaningAndInspectionEntity;

public interface CleaningAndInspectionService {

    CleaningAndInspectionEntity saveRfpEngineCiIncludedFlag(
            Long quotationId,
            boolean ciIncluded
    );

    CleaningAndInspectionEntity saveRfpModuleCiIncludedFlag(
            Long quotationId,
            boolean ciIncluded
    );
}
