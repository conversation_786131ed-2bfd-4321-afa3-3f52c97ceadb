package de.dlh.lht.ti.service.contract;

import de.dlh.lht.ti.entity.MaterialPricingItemEntity;
import de.dlh.lht.ti.enums.NonRoutineMaterialSubType;
import java.util.List;

public interface MaterialPricingItemService {

    MaterialPricingItemEntity createMaterialPricingItemEntity(
            Long quotationEngineId,
            Long clusterId,
            Long partId,
            NonRoutineMaterialSubType nonRoutineMaterialSubType
    );

    List<Long> findAllMaterialPricingItemsByQuotationId(Long quotationId);

    List<MaterialPricingItemEntity> getAllMaterialPricingItemsByQuotationId(Long quotationId);
}
