package de.dlh.lht.ti.service.implementation;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import de.dlh.lht.ti.exception.ObjectParsingException;
import de.dlh.lht.ti.importer.model.QuotationRaw;
import de.dlh.lht.ti.importer.model.QuotationUploadEvent;
import de.dlh.lht.ti.service.contract.AnkaImporterService;
import de.dlh.lht.ti.service.contract.FileService;
import de.dlh.lht.ti.service.contract.MessageConsumerService;
import de.dlh.lht.ti.service.contract.MessageProducerService;
import lombok.RequiredArgsConstructor;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;

import static de.dlh.lht.ti.utils.ErrorMessages.PARSING_QUOTATION_RESOURCE_TO_QUOTATION_RAW_ERROR_MESSAGE;
import static de.dlh.lht.ti.utils.ErrorMessages.PARSING_QUOTATION_UPLOAD_EVENT_ERROR_MESSAGE;

@Service
@RequiredArgsConstructor
public class MessageConsumerServiceImpl implements MessageConsumerService {

    private final ObjectMapper mapper = new ObjectMapper();

    private final FileService fileService;
    private final MessageProducerService messageProducerService;
    private final AnkaImporterService ankaImporterService;

    @KafkaListener(topics = "${messaging.consumer.quotation-upload}", groupId = "${spring.kafka.consumer.group-id}")
    @Override
    public void consumeQuotationUploadMessage(String message) {
        QuotationUploadEvent event;
        try {
            event = mapper.readValue(message, QuotationUploadEvent.class);
        } catch (JsonProcessingException e) {
            throw new ObjectParsingException(PARSING_QUOTATION_UPLOAD_EVENT_ERROR_MESSAGE);
        }

        // todo error handling TBD
        try {
            var quotationResource = fileService.readFromFile(event.getFilePath());
            if (quotationResource == null) {
                messageProducerService.sendQuotationFetchErrorEvent(event);
            } else {
                var quotationRaw = mapper.readValue(quotationResource, QuotationRaw.class);
                ankaImporterService.importQuotation(quotationRaw);
                messageProducerService.sendQuotationFetchSuccessEvent(event);
            }
        } catch (JsonProcessingException e) {
            messageProducerService.sendQuotationFetchErrorEvent(event);
            throw new ObjectParsingException(String.format(
                    PARSING_QUOTATION_RESOURCE_TO_QUOTATION_RAW_ERROR_MESSAGE,
                    event.getQuotationId()
            ));
        } finally {
            fileService.deleteFile(event.getFilePath());
        }
    }
}