package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.dto.importer.CustomerDto;
import de.dlh.lht.ti.entity.CustomerEntity;
import de.dlh.lht.ti.importer.model.CustomerRaw;
import de.dlh.lht.ti.mapper.CustomerMapper;
import de.dlh.lht.ti.repository.CustomerRepository;
import de.dlh.lht.ti.service.contract.CustomerService;
import jakarta.transaction.Transactional;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;


import static java.util.function.Function.identity;
import static java.util.stream.Collectors.toMap;

@Service
@RequiredArgsConstructor
public class CustomerServiceImpl implements CustomerService {

    private final CustomerMapper customerMapper;

    private final CustomerRepository customerRepository;

    @Override
    @Transactional
    public CustomerEntity createCustomerEntity(CustomerDto customerDto) {
        var customer = customerMapper.customerDtoToCustomerEntity(customerDto);
        return customerRepository.save(customer);
    }

    @Override
    public Map<CustomerDto, CustomerEntity> getCustomerEntityByCustomerDtoMap() {
        return customerRepository
                .findAll()
                .stream()
                .collect(toMap(customerMapper::customerEntityToCustomerDto, identity()));
    }

    @Override
    public CustomerDto customerRawToCustomerDto(CustomerRaw customer) {
        return customerMapper.customerRawToCustomerDto(customer);
    }

    @Override
    public List<String> getCustomersAsQuotationFilters() {
        return customerRepository.findAllCustomerEntitiesNames();
    }
}
