package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.entity.MaterialPricingItemEntity;
import de.dlh.lht.ti.enums.NonRoutineMaterialSubType;
import de.dlh.lht.ti.repository.MaterialPricingItemRepository;
import de.dlh.lht.ti.service.contract.MaterialPricingItemService;
import jakarta.transaction.Transactional;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class MaterialPricingItemServiceImpl implements MaterialPricingItemService {

    private final MaterialPricingItemRepository materialPricingItemRepository;

    @Override
    @Transactional
    public MaterialPricingItemEntity createMaterialPricingItemEntity(
            Long quotationEngineId,
            Long clusterId,
            Long partId,
            NonRoutineMaterialSubType nonRoutineMaterialSubType
    ) {
        var materialPricingItem =
                new MaterialPricingItemEntity(quotationEngineId, clusterId, partId, nonRoutineMaterialSubType);
        return materialPricingItemRepository.save(materialPricingItem);
    }

    @Override
    public List<Long> findAllMaterialPricingItemsByQuotationId(Long quotationId) {
        return materialPricingItemRepository.findAllMaterialPricingItemsIdsByQuotationId(quotationId);
    }

    @Override
    public List<MaterialPricingItemEntity> getAllMaterialPricingItemsByQuotationId(Long quotationId) {
        return materialPricingItemRepository.findAllIdsByQuotationId(quotationId);
    }
}
