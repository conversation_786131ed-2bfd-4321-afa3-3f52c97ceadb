package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.auth.UserPrincipal;
import de.dlh.lht.ti.auth.contract.UserPrincipalProvider;
import de.dlh.lht.ti.auth.roles.Role;
import de.dlh.lht.ti.entity.UserEntity;
import de.dlh.lht.ti.exception.EntityNotFoundException;
import de.dlh.lht.ti.mapper.UserMapper;
import de.dlh.lht.ti.model.QuotationOwner;
import de.dlh.lht.ti.model.UserDetails;
import de.dlh.lht.ti.repository.UserRepository;
import de.dlh.lht.ti.service.contract.UserPermissionService;
import de.dlh.lht.ti.service.contract.UserService;
import jakarta.transaction.Transactional;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;


import static de.dlh.lht.ti.utils.ErrorMessages.UNUMBER_BY_QUOTATION_ID_NOT_FOUND_ERROR_MESSAGE;
import static de.dlh.lht.ti.utils.ErrorMessages.USER_ENTITY_WITH_ID_NOT_FOUND_ERROR_MESSAGE;
import static de.dlh.lht.ti.utils.LoggingMessages.GET_USER_DETAILS_REQUEST;
import static java.util.function.Function.identity;
import static java.util.stream.Collectors.toMap;

@Log4j2
@Service
@RequiredArgsConstructor
public class UserServiceImpl implements UserService {

    private final UserMapper userMapper;

    private final UserRepository userRepository;

    private final UserPrincipalProvider userPrincipalProvider;

    private final UserPermissionService userPermissionService;

    @Override
    @Transactional
    public UserEntity createUserFromUNumber(String uNumber) {
        var user = new UserEntity();
        user.setUNumber(uNumber);
        return userRepository.save(user);
    }

    @Override
    public List<QuotationOwner> getAllUsers() {
        var userRoles = userPrincipalProvider.getRoles();
        if (userRoles.isEmpty() || userRoles.contains(Role.VIEW_ONLY_USER)) {
            return Collections.emptyList();
        }

        var userEntities = userRepository.findAll();
        return userMapper.entityUsersToOwnerRawList(userEntities);
    }

    @Override
    public List<QuotationOwner> getAllQuotationOwnerUsers() {
        var users = userRepository.findAllQuotationOwners();
        return userMapper.entityUsersToOwnerRawList(users);
    }

    @Override
    public String getCurrentOwnerUNumberByQuotationId(Long quotationId) {
        return userRepository.findCurrentOwnerUNumberByQuotationId(quotationId)
                .orElseThrow(() -> new EntityNotFoundException(
                        String.format(UNUMBER_BY_QUOTATION_ID_NOT_FOUND_ERROR_MESSAGE, quotationId)));
    }

    @Override
    public UserEntity getUserById(Long id) {
        return userRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException(
                        String.format(USER_ENTITY_WITH_ID_NOT_FOUND_ERROR_MESSAGE, id)));
    }

    @Override
    public Map<String, UserEntity> getUserEntityByUNumberMap() {
        return userRepository.findAll().stream().collect(toMap(UserEntity::getUNumber, identity()));
    }

    @Override
    public UserDetails getUserDetails() {
        var customPrincipalInfo = userPrincipalProvider.getUserPrincipal();
        var uNumber = customPrincipalInfo.getUNumber();

        log.debug(GET_USER_DETAILS_REQUEST, uNumber);

        var permissions = userPrincipalProvider.getPermissions();
        var userEntity = fetchUserDBRepresentation(customPrincipalInfo);

        var userId = userEntity == null ? null : userEntity.getId();

        return new UserDetails()
                .id(userId)
                .name(customPrincipalInfo.getName())
                .email(customPrincipalInfo.getEmail())
                .username(customPrincipalInfo.getUNumber())
                .permissions(permissions);
    }

    private UserEntity fetchUserDBRepresentation(UserPrincipal userPrincipalInfo) {
        var uNumber = userPrincipalInfo.getUNumber();
        var userEntity = uNumber == null ?
                null : userRepository.findByuNumber(uNumber).orElse(null);
        if (userPermissionService.isViewOnlyUser()) {
            if (userEntity != null) {
                userRepository.delete(userEntity);
            }

            return null;
        }

        if (userEntity == null) {
            userEntity = userMapper.userPrincipalToUserEntity(userPrincipalInfo);
        } else {
            userEntity = userMapper.userPrincipalToUserEntity(userPrincipalInfo, userEntity);
        }

        return userRepository.save(userEntity);
    }
}
