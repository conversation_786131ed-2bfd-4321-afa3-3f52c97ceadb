package de.dlh.lht.ti.service.implementation;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import de.dlh.lht.ti.exception.ObjectParsingException;
import de.dlh.lht.ti.importer.model.QuotationUploadEvent;
import de.dlh.lht.ti.importer.model.QuotationUploadResultEvent;
import de.dlh.lht.ti.service.contract.MessageProducerService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;


import static de.dlh.lht.ti.utils.ErrorMessages.PARSING_QUOTATION_FETCHED_ERROR_EVENT_ERROR_MESSAGE;
import static de.dlh.lht.ti.utils.ErrorMessages.PARSING_QUOTATION_FETCHED_SUCCESS_EVENT_ERROR_MESSAGE;

@Service
@RequiredArgsConstructor
public class MessageProducerServiceImpl implements MessageProducerService {

    private final ObjectMapper mapper = new ObjectMapper();

    @Value("${messaging.publisher.quotation-fetched-result}")
    public String quotationFetchedSuccessTopic;

    private final KafkaTemplate<String, String> kafkaTemplate;

    @Override
    public void sendQuotationFetchSuccessEvent(QuotationUploadEvent event) {
        var result = getResultEvent(event, QuotationUploadResultEvent.StatusEnum.SUCCESS);
        try {
            var resultMessage = mapper.writeValueAsString(result);
            kafkaTemplate.send(quotationFetchedSuccessTopic, resultMessage);
        } catch (JsonProcessingException e) {
            throw new ObjectParsingException(String.format(
                    PARSING_QUOTATION_FETCHED_SUCCESS_EVENT_ERROR_MESSAGE,
                    event.getQuotationId()
            ));
        }
    }

    @Override
    public void sendQuotationFetchErrorEvent(QuotationUploadEvent event) {
        var result = getResultEvent(event, QuotationUploadResultEvent.StatusEnum.FAILURE);
        try {
            var resultMessage = mapper.writeValueAsString(result);
            kafkaTemplate.send(quotationFetchedSuccessTopic, resultMessage);
        } catch (JsonProcessingException e) {
            throw new ObjectParsingException(String.format(
                    PARSING_QUOTATION_FETCHED_ERROR_EVENT_ERROR_MESSAGE,
                    event.getQuotationId()
            ));
        }
    }

    private QuotationUploadResultEvent getResultEvent(
            QuotationUploadEvent event,
            QuotationUploadResultEvent.StatusEnum status
    ) {
        return new QuotationUploadResultEvent(event.getQuotationId(), status);
    }
}