package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.dto.ContractTypesDto;
import de.dlh.lht.ti.dto.QuotationsQueryParametersDto;
import de.dlh.lht.ti.dto.RfpQuotationDto;
import de.dlh.lht.ti.entity.CustomerEntity;
import de.dlh.lht.ti.entity.ProjectEntity;
import de.dlh.lht.ti.entity.QuotationEntity;
import de.dlh.lht.ti.enums.QuotationStatus;
import de.dlh.lht.ti.exception.BeginQuotationNotAllowedException;
import de.dlh.lht.ti.exception.EntityNotFoundException;
import de.dlh.lht.ti.exception.QuotationAlreadyBegunException;
import de.dlh.lht.ti.importer.model.QuotationRaw;
import de.dlh.lht.ti.mapper.QuotationMapper;
import de.dlh.lht.ti.model.QuotationDetails;
import de.dlh.lht.ti.model.QuotationsPage;
import de.dlh.lht.ti.repository.QuotationRepository;
import de.dlh.lht.ti.service.contract.NavigationItemService;
import de.dlh.lht.ti.service.contract.QuotationEngineService;
import de.dlh.lht.ti.service.contract.QuotationService;
import de.dlh.lht.ti.service.contract.UserPermissionService;
import jakarta.transaction.Transactional;
import java.math.BigDecimal;
import java.time.ZonedDateTime;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;


import static de.dlh.lht.ti.utils.ErrorMessages.BEGIN_QUOTATION_NOT_ALLOWED_ERROR_MESSAGE;
import static de.dlh.lht.ti.utils.ErrorMessages.QUOTATION_ENTITY_WITH_ID_NOT_FOUND_ERROR_MESSAGE;
import static de.dlh.lht.ti.utils.ErrorMessages.QUOTATION_EXCHANGE_RATE_NOT_FOUND_ERROR_MESSAGE;
import static de.dlh.lht.ti.utils.ErrorMessages.QUOTATION_WITH_ID_ALREADY_BEGUN_ERROR_MESSAGE;

@Service
@RequiredArgsConstructor
public class QuotationServiceImpl implements QuotationService {

    private final QuotationMapper quotationMapper;

    private final QuotationRepository quotationRepository;

    private final NavigationItemService navigationItemService;
    private final QuotationEngineService quotationEngineService;
    private final UserPermissionService userPermissionService;

    @Override
    @Transactional
    public QuotationEntity createQuotation(QuotationRaw quotationRaw, ProjectEntity project, CustomerEntity customer) {
        //todo handle this on importer side trim all strings that will eventually be converted into BigDecimal objects
        quotationRaw.setUsdExchangeRate(quotationRaw.getUsdExchangeRate().trim());

        var quotation = quotationMapper.quotationRawToQuotationEntity(quotationRaw, project, customer);
        return quotationRepository.save(quotation);
    }

    @Override
    public String getQuotationBaseYear(Long quotationId) {
        var contractStart = quotationRepository.findContractStartById(quotationId).orElseThrow(
                () -> new EntityNotFoundException(
                        String.format(QUOTATION_ENTITY_WITH_ID_NOT_FOUND_ERROR_MESSAGE, quotationId)));

        return String.valueOf(contractStart.getYear());
    }

    @Override
    public QuotationEntity getQuotationEntityById(Long id) {
        return quotationRepository.findById(id).orElseThrow(
                () -> new EntityNotFoundException(
                        String.format(QUOTATION_ENTITY_WITH_ID_NOT_FOUND_ERROR_MESSAGE, id)));
    }

    @Override
    public RfpQuotationDto getRfpQuotationDtoById(Long quotationId) {
        return quotationRepository.findRfpQuotationDtoById(quotationId).orElseThrow(
                () -> new EntityNotFoundException(
                        String.format(QUOTATION_ENTITY_WITH_ID_NOT_FOUND_ERROR_MESSAGE, quotationId)));
    }

    @Override
    public QuotationsPage getQuotations(QuotationsQueryParametersDto quotationsQueryParametersDto) {
        return quotationEngineService.getQuotationsPage(quotationsQueryParametersDto);
    }

    @Override
    public QuotationDetails getQuotationDetails(Long quotationId) {
        return quotationEngineService.getQuotationDetails(quotationId);
    }

    @Override
    public BigDecimal getQuotationUsdExchangeRate(Long quotationId) {
        return quotationRepository.findUsdExchangeRateByQuotationId(quotationId).orElseThrow(
                () -> new EntityNotFoundException(
                        String.format(QUOTATION_EXCHANGE_RATE_NOT_FOUND_ERROR_MESSAGE, quotationId)));
    }

    @Override
    @Transactional
    public QuotationDetails beginQuotation(Long quotationId, ContractTypesDto contractTypesDto) {
        var quotationEntity = getQuotationEntityById(quotationId);
        var quotationStatus = QuotationStatus.values()[quotationEntity.getStatus()];
        var currentOwnerUNumber = quotationEntity.getProject().getCurrentOwner().getUNumber();

        if (!userPermissionService.isUserOwnerOrAdmin(currentOwnerUNumber)) {
            throw new BeginQuotationNotAllowedException(String.format(BEGIN_QUOTATION_NOT_ALLOWED_ERROR_MESSAGE, currentOwnerUNumber));
        }

        if (quotationStatus.equals(QuotationStatus.ANKA_VALIDATED)) {
            quotationEntity.setRoutineFixedPrices(contractTypesDto.isRoutineFixedPrices());
            quotationEntity.setStatus(QuotationStatus.IN_PROGRESS.ordinal());
            quotationEntity.setStatusLastUpdated(ZonedDateTime.now());

            quotationRepository.save(quotationEntity);
            navigationItemService.createNavigationItems(quotationId, contractTypesDto.isRoutineFixedPrices());

            return getQuotationDetails(quotationId);
        } else {
            throw new QuotationAlreadyBegunException(String.format(QUOTATION_WITH_ID_ALREADY_BEGUN_ERROR_MESSAGE, quotationId));
        }
    }

    @Override
    public boolean quotationEntityExistsByPositionAndVersionAndScenarioAndProjectId(
            int position,
            int version,
            int scenario,
            Long projectId) {
        return quotationRepository.existsByPositionAndVersionAndScenarioAndProjectId(position, version, scenario, projectId);
    }

    @Override
    public boolean isRfpContractSelected(Long quotationId) {
        return getQuotationDetails(quotationId).getRoutineFixedPrices();
    }
}
