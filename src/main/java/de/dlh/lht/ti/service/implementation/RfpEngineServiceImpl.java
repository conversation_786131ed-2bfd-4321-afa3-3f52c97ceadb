package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.dto.RfpEngineItemDto;
import de.dlh.lht.ti.dto.RfpEngineMetadataDto;
import de.dlh.lht.ti.dto.RfpQuotationDto;
import de.dlh.lht.ti.entity.RfpEngineItemEntity;
import de.dlh.lht.ti.enums.TaskType;
import de.dlh.lht.ti.exception.ValidationException;
import de.dlh.lht.ti.mapper.RfpEngineItemMapper;
import de.dlh.lht.ti.model.RfpEngineData;
import de.dlh.lht.ti.model.RfpItemInput;
import de.dlh.lht.ti.model.RfpRequest;
import de.dlh.lht.ti.repository.RfpEngineItemRepository;
import de.dlh.lht.ti.service.contract.CleaningAndInspectionService;
import de.dlh.lht.ti.service.contract.QuotationService;
import de.dlh.lht.ti.service.contract.RfpEngineService;
import de.dlh.lht.ti.service.contract.TaskMetadataService;
import de.dlh.lht.ti.service.contract.TestrunItemService;
import de.dlh.lht.ti.service.contract.WorkscopeService;
import de.dlh.lht.ti.utils.Calculations;
import de.dlh.lht.ti.utils.comparators.RfpEngineItemComparator;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;


import static de.dlh.lht.ti.utils.ErrorMessages.LABOUR_RFP_ENGINE_VALIDATION_ERROR_MESSAGE;
import static de.dlh.lht.ti.utils.Validations.rfpItemInputAssertion;

@Service
@RequiredArgsConstructor
public class RfpEngineServiceImpl implements RfpEngineService {

    private final RfpEngineItemMapper rfpEngineItemMapper;

    private final RfpEngineItemRepository rfpEngineItemRepository;

    private final CleaningAndInspectionService cleaningAndInspectionService;
    private final TaskMetadataService taskMetadataService;
    private final TestrunItemService testrunItemService;
    private final QuotationService quotationService;
    private final WorkscopeService workscopeService;

    @Override
    public RfpEngineData getRfpEngineData(Long quotationId) {
        var quotationDto = quotationService.getRfpQuotationDtoById(quotationId);
        var rfpEnginesDtos = rfpEngineItemRepository.findAllDtosByQuotationId(quotationId);
        var year = String.valueOf(quotationDto.getContactStart().getYear());
        var workscopeId = workscopeService.getDeepestWorkscopeIdByQuotationId(quotationId);
        if (rfpEnginesDtos.isEmpty()) {
            var taskMetadata = getTaskMetadataByLabourPricingItemId(quotationId, year, workscopeId);

            rfpEnginesDtos = createRfpEngineItems(quotationId, quotationDto, taskMetadata);
        }

        var rfpEngineItems = rfpEngineItemMapper.toApiModelList(rfpEnginesDtos);
        var rpfTestrunItem = testrunItemService.getRfpItemByQuotationIdYearAndWorkscopeId(
                quotationId,
                year,
                workscopeId,
                quotationDto.getUsdExchangeRate());
        rfpEngineItems.add(rpfTestrunItem);
        rfpEngineItems.sort(new RfpEngineItemComparator());

        return new RfpEngineData()
                .ciIncluded(quotationDto.getIsRfpEngineCiIncluded())
                .rfpEngineItems(rfpEngineItems);
    }

    @Override
    public RfpEngineData saveRfpEngineData(Long quotationId, RfpRequest rfpEngineData)
            throws ValidationException {
        rfpItemInputAssertion(rfpEngineData, LABOUR_RFP_ENGINE_VALIDATION_ERROR_MESSAGE, TaskType.RFP_ENGINE);

        var testrunItemRfpItemInput = rfpEngineData.getRfpItemInputs().stream()
                .filter(RfpItemInput::getIsTestrunMaterial)
                .findFirst();
        testrunItemRfpItemInput.ifPresent(rfpItemInput ->
                testrunItemService.setPrice(quotationId, rfpItemInput));

        var rfpEnginesMappedByLabourPricingItemId = rfpEngineItemRepository
                .findAllByQuotationId(quotationId).stream()
                .collect(Collectors.toMap(RfpEngineItemEntity::getLabourPricingItemId, Function.identity()));

        var entitiesForUpdate = rfpEngineData.getRfpItemInputs().stream()
                .map(item -> {
                            var entity = rfpEnginesMappedByLabourPricingItemId.get(item.getId());
                            if (entity == null) {
                                return null;
                            }

                            return rfpEngineItemMapper.toEntity(item, entity);
                        }
                )
                .filter(Objects::nonNull)
                .toList();
        rfpEngineItemRepository.saveAll(entitiesForUpdate);

        cleaningAndInspectionService
                .saveRfpEngineCiIncludedFlag(quotationId, rfpEngineData.getCiIncluded());

        return getRfpEngineData(quotationId);
    }

    @Override
    public boolean isRfpEngineValid(RfpEngineData rfpEngineData) {
        return rfpEngineData.getRfpEngineItems().stream()
                .noneMatch(item -> item.getPrice() == null);
    }

    private Map<Long, List<RfpEngineMetadataDto>> getTaskMetadataByLabourPricingItemId(
            Long quotationId,
            String year,
            Long workscopeId
    ) {
        return taskMetadataService.findAllByQuotationIdWorkscopeIdYearAndTaskType(
                        quotationId,
                        TaskType.RFP_ENGINE,
                        workscopeId,
                        year
                )
                .stream()
                .collect(Collectors.groupingBy(RfpEngineMetadataDto::getLabourPricingItemId));
    }

    private RfpEngineItemEntity createRfpEngineItem(
            BigDecimal usdExchangeRate,
            Long labourPricingItemId,
            List<RfpEngineMetadataDto> taskMetadataDtos
    ) {
        if (labourPricingItemId == null || taskMetadataDtos.isEmpty()) {
            return null;
        }

        var rfpCostDto = Calculations.calculateTaskCostSum(taskMetadataDtos, usdExchangeRate);

        var entity = new RfpEngineItemEntity();
        entity.setLabourPricingItemId(labourPricingItemId);
        entity.setRfpEngineBaseCost(rfpCostDto.getCost());
        entity.setRfpEngineCiHoursCost(rfpCostDto.getCiHoursCost());

        return entity;
    }

    private List<RfpEngineItemDto> createRfpEngineItems(
            Long quotationId,
            RfpQuotationDto quotation,
            Map<Long, List<RfpEngineMetadataDto>> taskMetadata
    ) {
        var entitiesFroInsertion = taskMetadata.entrySet().stream()
                .map(entry -> createRfpEngineItem(
                        quotation.getUsdExchangeRate(),
                        entry.getKey(),
                        entry.getValue())
                )
                .toList();
        rfpEngineItemRepository.saveAll(entitiesFroInsertion);

        return rfpEngineItemRepository.findAllDtosByQuotationId(quotationId);
    }
}
