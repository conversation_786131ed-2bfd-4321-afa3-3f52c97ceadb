package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.dto.CalculationResultDto;
import de.dlh.lht.ti.dto.LabourPricingItemDto;
import de.dlh.lht.ti.dto.RfpEngineMetadataDto;
import de.dlh.lht.ti.dto.WorkscopeDto;
import de.dlh.lht.ti.entity.TaskMetadataEntity;
import de.dlh.lht.ti.enums.TaskType;
import de.dlh.lht.ti.importer.model.LabourPricingItemRaw;
import de.dlh.lht.ti.mapper.TaskMetadataMapper;
import de.dlh.lht.ti.repository.TaskMetadataRepository;
import de.dlh.lht.ti.service.contract.LabourPricingItemService;
import de.dlh.lht.ti.service.contract.TaskMetadataService;
import de.dlh.lht.ti.utils.Calculations;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class TaskMetadataServiceImpl implements TaskMetadataService {

    private final TaskMetadataRepository taskMetadataRepository;

    private final TaskMetadataMapper taskMetadataMapper;

    private final LabourPricingItemService labourPricingItemService;

    @Override
    public List<TaskMetadataEntity> createTaskMetadata(
            List<LabourPricingItemRaw> labourPricingItems,
            Map<String, Long> workscopeIdsMap,
            Long quotationEngineId
    ) {
        if (labourPricingItems.isEmpty() || workscopeIdsMap.isEmpty() || quotationEngineId == null) {
            return Collections.emptyList();
        }

        var labourPricingItemsDtos =
                labourPricingItemService.getLabourPricingItemsDtos(quotationEngineId).stream()
                        .collect(Collectors.toMap(
                                LabourPricingItemDto::getLabel,
                                LabourPricingItemDto::getId
                        ));

        var taskMetadataForInsertion = createTaskMetadata(
                labourPricingItems,
                workscopeIdsMap,
                labourPricingItemsDtos
        );

        return taskMetadataRepository.saveAll(taskMetadataForInsertion);
    }

    @Override
    public List<RfpEngineMetadataDto> findAllByQuotationIdWorkscopeIdYearAndTaskType(
            Long quotationId,
            TaskType type,
            Long workscopeId,
            String year
    ) {
        return taskMetadataRepository
                .findAllByQuotationIdWorkscopeIdYearAndTaskType(quotationId, type, workscopeId, year);
    }

    @Override
    public Map<WorkscopeDto, CalculationResultDto> getAllProductionCostByWorkscopeDtoQuotationId(Long quotationId) {
        var productionCostDtos = taskMetadataRepository.findAllProductionCostMetadataByQuotationId(quotationId);

        return Calculations.calculateProductionCost(productionCostDtos);
    }

    private List<TaskMetadataEntity> createTaskMetadata(
            List<LabourPricingItemRaw> labourPricingItems,
            Map<String, Long> workscopeIdsMap,
            Map<String, Long> labourPricingItemsDtos) {
        var taskMetadataForInsertion = new ArrayList<TaskMetadataEntity>();

        labourPricingItems.stream().flatMap(item -> item.getTaskMetadata().stream())
                .forEach(taskMetadataRaw -> {
                    var labourPricingItemId = labourPricingItemsDtos.get(taskMetadataRaw.getName());
                    if (labourPricingItemId == null) {
                        return;
                    }

                    var taskMetadataEntity = taskMetadataMapper.mapToEntity(taskMetadataRaw);
                    taskMetadataEntity.setWorkscopeId(workscopeIdsMap.get(taskMetadataRaw.getWorkscope()));
                    taskMetadataEntity.setLabourPricingItemId(labourPricingItemId);
                    taskMetadataForInsertion.add(taskMetadataEntity);
                });

        return taskMetadataForInsertion;
    }
}