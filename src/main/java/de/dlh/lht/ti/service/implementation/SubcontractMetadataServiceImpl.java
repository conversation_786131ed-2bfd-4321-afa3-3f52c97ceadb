package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.dto.CalculationResultDto;
import de.dlh.lht.ti.dto.WorkscopeDto;
import de.dlh.lht.ti.dto.importer.PartDto;
import de.dlh.lht.ti.entity.SubcontractMetadataEntity;
import de.dlh.lht.ti.entity.SubcontractPricingItemEntity;
import de.dlh.lht.ti.importer.model.SubcontractPricingItemRaw;
import de.dlh.lht.ti.mapper.SubcontractMetadataMapper;
import de.dlh.lht.ti.repository.SubcontractMetadataRepository;
import de.dlh.lht.ti.service.contract.PartService;
import de.dlh.lht.ti.service.contract.SubcontractMetadataService;
import de.dlh.lht.ti.utils.Calculations;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;


import static java.util.stream.Collectors.toMap;

@Service
@RequiredArgsConstructor
public class SubcontractMetadataServiceImpl implements SubcontractMetadataService {

    private final SubcontractMetadataMapper subcontractMetadataMapper;

    private final SubcontractMetadataRepository subcontractMetadataRepository;

    private final PartService partService;

    @Override
    public List<SubcontractMetadataEntity> createSubcontractMetadata(
            List<SubcontractPricingItemRaw> rawSubcontractPricingItems,
            List<SubcontractPricingItemEntity> subcontractPricingItems,
            Long engineId,
            Map<PartDto, Long> partIdByPartDtoMap,
            Map<Long, Map<Long, Long>> clusterIdByEngineIdByPartIdMap,
            Map<String, Long> workscopeIdsByNameMap) {
        if (rawSubcontractPricingItems == null
                || rawSubcontractPricingItems.isEmpty()
                || subcontractPricingItems == null
                || subcontractPricingItems.isEmpty()) {
            return Collections.emptyList();
        }
        var newSubcontractMetadata = new ArrayList<SubcontractMetadataEntity>();

        var subcontractPricingItemIdByClusterIdMap = subcontractPricingItems.stream()
                .collect(toMap(SubcontractPricingItemEntity::getClusterId, SubcontractPricingItemEntity::getId));

        rawSubcontractPricingItems.forEach(subcontractPricingItemRaw -> {
            var partId = partService.findPartIdByNameAndType(subcontractPricingItemRaw.getPart(), partIdByPartDtoMap);
            var clusterId = clusterIdByEngineIdByPartIdMap.get(engineId).get(partId);
            var subcontractPricingItemId = subcontractPricingItemIdByClusterIdMap.get(clusterId);

            subcontractPricingItemRaw.getSubcontractMetadata().forEach(subcontractMetadataRaw -> {
                var workscopeId = workscopeIdsByNameMap.get(subcontractMetadataRaw.getWorkscope());
                var subcontractMetadata = subcontractMetadataMapper.subcontractMetadataRawToSubcontractMetadataEntity(
                        subcontractMetadataRaw,
                        subcontractPricingItemId,
                        workscopeId);
                newSubcontractMetadata.add(subcontractMetadata);
            });
        });

        return subcontractMetadataRepository.saveAll(newSubcontractMetadata);
    }

    @Override
    public Map<WorkscopeDto, CalculationResultDto> getAllProductionCostByWorkscopeDtoByQuotationId(Long quotationId) {
        var productionCostDtos = subcontractMetadataRepository.findAllProductionCostMetadataByQuotationId(quotationId);

        return Calculations.calculateProductionCost(productionCostDtos);
    }
}
