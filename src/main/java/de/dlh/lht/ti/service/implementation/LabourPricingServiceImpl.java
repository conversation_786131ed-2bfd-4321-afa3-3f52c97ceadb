package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.enums.QuotationProgress;
import de.dlh.lht.ti.exception.RfpContractNotSelectedException;
import de.dlh.lht.ti.exception.SaveLabourPricingNotAllowedException;
import de.dlh.lht.ti.exception.ValidationException;
import de.dlh.lht.ti.model.LabourRate;
import de.dlh.lht.ti.model.LabourRateInput;
import de.dlh.lht.ti.model.RfpEngine;
import de.dlh.lht.ti.model.RfpModule;
import de.dlh.lht.ti.model.RfpRequest;
import de.dlh.lht.ti.service.contract.LabourPricingService;
import de.dlh.lht.ti.service.contract.LabourRateService;
import de.dlh.lht.ti.service.contract.NavigationItemService;
import de.dlh.lht.ti.service.contract.QuotationService;
import de.dlh.lht.ti.service.contract.RfpEngineService;
import de.dlh.lht.ti.service.contract.RfpModuleService;
import de.dlh.lht.ti.service.contract.UserPermissionService;
import de.dlh.lht.ti.service.contract.UserService;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;


import static de.dlh.lht.ti.utils.ErrorMessages.RFP_CONTRACT_IS_NOT_SELECTED_ERROR_MESSAGE;
import static de.dlh.lht.ti.utils.ErrorMessages.SAVE_LABOUR_RATE_NOT_ALLOWED_ERROR_MESSAGE;
import static de.dlh.lht.ti.utils.ErrorMessages.SAVE_LABOUR_RFP_ENGINE_NOT_ALLOWED_ERROR_MESSAGE;
import static de.dlh.lht.ti.utils.ErrorMessages.SAVE_RFP_MODULE_NOT_ALLOWED_ERROR_MESSAGE;

@Service
@RequiredArgsConstructor
public class LabourPricingServiceImpl implements LabourPricingService {

    private final LabourRateService labourRateService;
    private final RfpEngineService rfpEngineService;
    private final NavigationItemService navigationItemService;
    private final QuotationService quotationService;
    private final RfpModuleService rfpModuleService;
    private final UserPermissionService userPermissionService;
    private final UserService userService;

    @Override
    @Transactional
    public LabourRate getLabourRates(Long quotationId) {
        var currentOwnerUNumber = userService.getCurrentOwnerUNumberByQuotationId(quotationId);
        var canCurrentUserEdit = canCurrentUserEdit(currentOwnerUNumber);
        var progress = navigationItemService.getQuotationProgressByQuotationId(quotationId);

        var labourRate = labourRateService.getLabourRates(quotationId);

        return labourRate
                .canCurrentUserEdit(canCurrentUserEdit)
                .progress(progress);
    }

    @Override
    @Transactional
    public LabourRate saveLabourRates(Long quotationId, LabourRateInput labourRateInput)
            throws SaveLabourPricingNotAllowedException, ValidationException {
        var currentOwnerUNumber = userService.getCurrentOwnerUNumberByQuotationId(quotationId);
        if (!canCurrentUserEdit(currentOwnerUNumber)) {
            throw new SaveLabourPricingNotAllowedException(
                    String.format(
                            SAVE_LABOUR_RATE_NOT_ALLOWED_ERROR_MESSAGE,
                            currentOwnerUNumber
                    )
            );
        }

        var labourRate = labourRateService.saveLabourRates(quotationId, labourRateInput);

        var isLabourRateStepValid = labourRateService.hasValidLabourRate(labourRate);
        navigationItemService.updateProgress(
                quotationId,
                QuotationProgress.LABOUR_RATE_AND_EPAR,
                isLabourRateStepValid
        );

        var progress = navigationItemService.getQuotationProgressByQuotationId(quotationId);

        return labourRate
                .canCurrentUserEdit(true)
                .progress(progress);
    }

    @Override
    @Transactional
    public RfpEngine getRfpEngineData(Long quotationId) {
        validateRfpContractSelected(quotationId);
        var currentOwnerUNumber = userService.getCurrentOwnerUNumberByQuotationId(quotationId);
        var canCurrentUserEdit = canCurrentUserEdit(currentOwnerUNumber);

        var progress = navigationItemService.getQuotationProgressByQuotationId(quotationId);

        var rfpEngineData = rfpEngineService.getRfpEngineData(quotationId);

        return new RfpEngine()
                .rfpEngineData(rfpEngineData)
                .canCurrentUserEdit(canCurrentUserEdit)
                .progress(progress);
    }

    @Override
    @Transactional
    public RfpEngine saveRfpEngineData(Long quotationId, RfpRequest rfpEngineData)
            throws SaveLabourPricingNotAllowedException, ValidationException, RfpContractNotSelectedException {
        validateRfpContractSelected(quotationId);
        var currentOwnerUNumber = userService.getCurrentOwnerUNumberByQuotationId(quotationId);
        if (!canCurrentUserEdit(currentOwnerUNumber)) {
            throw new SaveLabourPricingNotAllowedException(
                    String.format(
                            SAVE_LABOUR_RFP_ENGINE_NOT_ALLOWED_ERROR_MESSAGE,
                            currentOwnerUNumber
                    )
            );
        }

        var labourRfpEngineData = rfpEngineService.saveRfpEngineData(quotationId, rfpEngineData);
        var isLabourRfpEngineValid = rfpEngineService.isRfpEngineValid(labourRfpEngineData);

        navigationItemService.updateProgress(
                quotationId,
                QuotationProgress.RFP_ENGINE,
                isLabourRfpEngineValid
        );

        var progress = navigationItemService.getQuotationProgressByQuotationId(quotationId);

        return new RfpEngine()
                .rfpEngineData(labourRfpEngineData)
                .progress(progress)
                .canCurrentUserEdit(true);
    }

    @Override
    @Transactional
    public RfpModule getRfpModule(Long quotationId) {
        validateRfpContractSelected(quotationId);
        var currentOwnerUNumber = userService.getCurrentOwnerUNumberByQuotationId(quotationId);
        var canCurrentUserEdit = canCurrentUserEdit(currentOwnerUNumber);
        var progress = navigationItemService.getQuotationProgressByQuotationId(quotationId);

        var rfpModuleData = rfpModuleService.getRfpModuleData(quotationId);

        return new RfpModule()
                .rfpModuleData(rfpModuleData)
                .progress(progress)
                .canCurrentUserEdit(canCurrentUserEdit);
    }

    @Override
    @Transactional
    public RfpModule saveRfpModule(Long quotationId, RfpRequest rfpRequest) {
        validateRfpContractSelected(quotationId);
        var currentOwnerUNumber = userService.getCurrentOwnerUNumberByQuotationId(quotationId);
        if (!canCurrentUserEdit(currentOwnerUNumber)) {
            throw new SaveLabourPricingNotAllowedException(
                    String.format(SAVE_RFP_MODULE_NOT_ALLOWED_ERROR_MESSAGE, currentOwnerUNumber));
        }

        var rfpModuleData = rfpModuleService.saveRfpModuleData(quotationId, rfpRequest);
        var isRfpModuleValid = rfpModuleService.isRfpModuleDataValid(rfpModuleData);

        navigationItemService.updateProgress(quotationId, QuotationProgress.RFP_MODULE, isRfpModuleValid);

        var progress = navigationItemService.getQuotationProgressByQuotationId(quotationId);

        return new RfpModule()
                .rfpModuleData(rfpModuleData)
                .progress(progress)
                .canCurrentUserEdit(true);
    }

    private boolean canCurrentUserEdit(String currentOwnerUNumber) {
        return userPermissionService.isUserOwnerOrAdmin(currentOwnerUNumber);
    }

    private void validateRfpContractSelected(Long quotationId) {
        var isRfpContractSelected = quotationService.isRfpContractSelected(quotationId);
        if (!isRfpContractSelected) {
            throw new RfpContractNotSelectedException(
                    String.format(
                            RFP_CONTRACT_IS_NOT_SELECTED_ERROR_MESSAGE,
                            quotationId
                    )
            );
        }
    }
}
