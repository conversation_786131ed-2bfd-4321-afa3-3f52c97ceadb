package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.dto.CalculationResultDto;
import de.dlh.lht.ti.dto.discounts.DiscountWorkscopeDto;
import de.dlh.lht.ti.dto.discounts.MaterialDiscountDto;
import de.dlh.lht.ti.entity.MaterialDiscountExpendableEntity;
import de.dlh.lht.ti.entity.MaterialDiscountTypeEntity;
import de.dlh.lht.ti.entity.MaterialDiscountsConditionEntity;
import de.dlh.lht.ti.entity.VolumeBasedDiscountEntity;
import de.dlh.lht.ti.enums.Currency;
import de.dlh.lht.ti.enums.DiscountLabourType;
import de.dlh.lht.ti.enums.ExpandableDiscountType;
import de.dlh.lht.ti.enums.PartType;
import de.dlh.lht.ti.enums.WorkscopeClass;
import de.dlh.lht.ti.service.contract.DiscountConditionsService;
import de.dlh.lht.ti.service.contract.DiscountService;
import de.dlh.lht.ti.service.contract.EngineService;
import de.dlh.lht.ti.service.contract.PartMetadataService;
import de.dlh.lht.ti.service.contract.SubcontractMetadataService;
import de.dlh.lht.ti.utils.Discounts;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;


import static de.dlh.lht.ti.utils.Constants.HUNDRED;
import static de.dlh.lht.ti.utils.Constants.MATH_CONTEXT;
import static de.dlh.lht.ti.utils.Discounts.EXPANDABLE_DISCOUNT_MAP;

@Service
@RequiredArgsConstructor
public class DiscountServiceImpl implements DiscountService {

    private final DiscountConditionsService discountConditionsService;
    private final EngineService engineService;
    private final PartMetadataService partMetadataService;
    private final SubcontractMetadataService subcontractMetadataService;

    @Override
    public Map<Long, Map<String, CalculationResultDto>> getDiscounts(Long quotationId) {
        var engineId = engineService.getEngineIdByQuotationId(quotationId);
        var materialDiscounts = getMaterialDiscounts(quotationId, engineId);
        var subcontractDiscounts = getSubcontractDiscounts(quotationId, engineId);

        return mergeDiscounts(materialDiscounts, subcontractDiscounts);
    }

    private Map<Long, Map<String, CalculationResultDto>> mergeDiscounts(
            Map<Long, Map<String, CalculationResultDto>> materialDiscounts,
            Map<Long, Map<String, CalculationResultDto>> subcontractDiscounts
    ) {

        return Stream.concat(materialDiscounts.entrySet().stream(), subcontractDiscounts.entrySet().stream())
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (materialMapPerYear, subContactMapPerYear) -> Stream.concat(
                                materialMapPerYear.entrySet().stream(),
                                subContactMapPerYear.entrySet().stream()
                        ).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, this::mergeDiscount))
                ));
    }

    private CalculationResultDto mergeDiscount(
            CalculationResultDto materialDiscount,
            CalculationResultDto subcontractDiscount
    ) {
        return CalculationResultDto.builder()
                .valueUsd(materialDiscount.getValueUsd()
                        .add(subcontractDiscount.getValueUsd(), MATH_CONTEXT)
                )
                .valueEur(materialDiscount.getValueEur()
                        .add(subcontractDiscount.getValueEur(), MATH_CONTEXT)
                )
                .build();
    }

    private Map<Long, Map<String, CalculationResultDto>> getSubcontractDiscounts(Long quotationId, Long engineId) {
        //todo implement subcontract discounts
        return Collections.emptyMap();
    }

    private Map<Long, Map<String, CalculationResultDto>> getMaterialDiscounts(Long quotationId, Long engineId) {
        var discountMetadata = partMetadataService.getAllMaterialDiscountsByQuotationId(quotationId);
        var discountConditions =
                discountConditionsService.findAllMaterialDiscountsConditionsByEngineIdPerYear(engineId);
        var discountExpandableMap =
                discountConditionsService.findAllMaterialDiscountsExpandableByEngineIdPerYear(engineId);
        var materialDiscountTypes = discountConditionsService.findAllMaterialDiscountsByEngineId(engineId);

        var volumeDiscounts = discountConditionsService.findAllVolumeBasedDiscountsByEngineIdPerYear(engineId);

        var discounts = new HashMap<Long, Map<String, CalculationResultDto>>();
        discountMetadata.forEach((workscope, metadataPerWorkscope) -> {
            var discountsPerWorkscope = new HashMap<String, CalculationResultDto>();
            metadataPerWorkscope.forEach((year, metadataPerYear) -> {
                var volumeDiscount = getVolumeDiscountPerWorkscopeClass(volumeDiscounts, workscope, year);

                var discount = createMaterialDiscount(
                        materialDiscountTypes,
                        discountConditions.get(year),
                        discountExpandableMap.get(year),
                        volumeDiscount,
                        metadataPerYear
                );
                discountsPerWorkscope.put(year, discount);
            });
            discounts.put(workscope.getWorkscopeId(), discountsPerWorkscope);
        });
        return discounts;
    }

    private VolumeBasedDiscountEntity getVolumeDiscountPerWorkscopeClass(
            Map<String, VolumeBasedDiscountEntity> volumeDiscounts,
            DiscountWorkscopeDto workscope,
            String year
    ) {
        return workscope.getWorkscopeClass() == WorkscopeClass.A
                ? volumeDiscounts.get(year)
                : null;
    }

    private CalculationResultDto createMaterialDiscount(
            Map<PartType, Map<DiscountLabourType, MaterialDiscountTypeEntity>> materialDiscountTypes,
            MaterialDiscountsConditionEntity materialDiscountsCondition,
            MaterialDiscountExpendableEntity materialDiscountExpendable,
            VolumeBasedDiscountEntity volumeBasedDiscountEntity,
            List<MaterialDiscountDto> metadata) {
        var groupedMetadataPerCurrency =
                metadata.stream().collect(Collectors.groupingBy(MaterialDiscountDto::getZ1Currency));

        return CalculationResultDto.builder()
                .valueEur(calculateDiscount(
                        materialDiscountTypes,
                        materialDiscountsCondition,
                        materialDiscountExpendable,
                        volumeBasedDiscountEntity,
                        groupedMetadataPerCurrency.getOrDefault(Currency.EUR, Collections.emptyList()))
                )
                .valueUsd(calculateDiscount(
                        materialDiscountTypes,
                        materialDiscountsCondition,
                        materialDiscountExpendable,
                        volumeBasedDiscountEntity,
                        groupedMetadataPerCurrency.getOrDefault(Currency.USD, Collections.emptyList()))
                )
                .build();
    }

    private BigDecimal calculateDiscount(
            Map<PartType, Map<DiscountLabourType, MaterialDiscountTypeEntity>> materialDiscountTypes,
            MaterialDiscountsConditionEntity materialDiscountsCondition,
            MaterialDiscountExpendableEntity materialDiscountExpendable,
            VolumeBasedDiscountEntity volumeBasedDiscountEntity,
            List<MaterialDiscountDto> metadata
    ) {

        var discount = BigDecimal.ZERO;
        var groupedMetadataPerPartType =
                metadata.stream().collect(Collectors.groupingBy(MaterialDiscountDto::getPartType));
        for (var entry : groupedMetadataPerPartType.entrySet()) {
            var materialType = entry.getKey();
            var materialMetadata = entry.getValue();

            var productionCost = calculateProductionCost(materialMetadata);
            var materialDiscount = getMaterialDiscount(
                    materialType,
                    productionCost,
                    materialDiscountsCondition,
                    materialDiscountExpendable,
                    volumeBasedDiscountEntity,
                    materialDiscountTypes
            );
            discount = discount.add(materialDiscount, MATH_CONTEXT);
        }

        return discount;
    }

    private BigDecimal getMaterialDiscount(
            PartType materialType,
            BigDecimal productionCost,
            MaterialDiscountsConditionEntity materialDiscountsCondition,
            MaterialDiscountExpendableEntity materialDiscountExpendable,
            VolumeBasedDiscountEntity volumeBasedDiscountEntity,
            Map<PartType, Map<DiscountLabourType, MaterialDiscountTypeEntity>> materialDiscountTypes
    ) {
        var discountType =
                materialDiscountTypes.get(materialType).get(getDiscountLabourTypeFromPartType(materialType));

        BigDecimal nonExpandableDiscount =
                getNonExpandableDiscount(materialType, productionCost, materialDiscountsCondition, discountType);

        BigDecimal expandableDiscount =
                getExpandableDiscount(productionCost, materialDiscountExpendable, discountType);

        var volumeBasedDiscount = getVolumeBasedDiscount(volumeBasedDiscountEntity, productionCost);

        return nonExpandableDiscount
                .add(expandableDiscount, MATH_CONTEXT)
                .add(volumeBasedDiscount, MATH_CONTEXT);
    }

    private BigDecimal getExpandableDiscount(
            BigDecimal productionCost,
            MaterialDiscountExpendableEntity materialDiscountExpendable,
            MaterialDiscountTypeEntity discountType
    ) {
        var expendablePercentPortion = discountType.getExpendablePortion();

        var expandablePercent =
                getExpandablePercent(productionCost, expendablePercentPortion, materialDiscountExpendable);

        return productionCost.multiply(expandablePercent.divide(HUNDRED, MATH_CONTEXT), MATH_CONTEXT);
    }

    private BigDecimal getNonExpandableDiscount(PartType materialType, BigDecimal productionCost, MaterialDiscountsConditionEntity materialDiscountsCondition, MaterialDiscountTypeEntity discountType) {
        var nonExtendableDiscountPortionByPartType = getDiscountPortionByPartType(materialType, discountType);
        var nonExpendablePercent = getDiscountTypePerPartType(materialType, materialDiscountsCondition)
                .multiply(nonExtendableDiscountPortionByPartType.divide(HUNDRED, MATH_CONTEXT), MATH_CONTEXT);

        return productionCost
                .multiply(nonExpendablePercent.divide(HUNDRED, MATH_CONTEXT), MATH_CONTEXT);
    }

    private BigDecimal getVolumeBasedDiscount(
            VolumeBasedDiscountEntity volumeBasedDiscountEntity,
            BigDecimal productionCost
    ) {
        if(volumeBasedDiscountEntity == null) {
            return BigDecimal.ZERO;
        }

        if(productionCost.compareTo(volumeBasedDiscountEntity.getMinimumMaterialCost()) < 0) {
            return BigDecimal.ZERO;
        } else if (productionCost.compareTo(volumeBasedDiscountEntity.getTargetMaterialCost()) >= 0) {
            return volumeBasedDiscountEntity.getMaximumVolumeBasedDiscount();
        } else {
            return calculateVolumeBasedDiscount(volumeBasedDiscountEntity, productionCost);
        }
    }

    private BigDecimal calculateVolumeBasedDiscount(
            VolumeBasedDiscountEntity volumeBasedDiscountEntity,
            BigDecimal productionCost
    ) {
        return productionCost
                .divide(volumeBasedDiscountEntity.getTargetMaterialCost(), MATH_CONTEXT)
                .subtract(Discounts.VOLUME_BASED_DISCOUNT_BASELINE, MATH_CONTEXT)
                .divide(Discounts.VOLUME_BASED_DISCOUNT_SCALING_FACTOR, MATH_CONTEXT)
                .multiply(volumeBasedDiscountEntity.getMaximumVolumeBasedDiscount(), MATH_CONTEXT);
    }

    private BigDecimal getExpandablePercent(
            BigDecimal productionCost,
            BigDecimal expendablePercentPortion,
            MaterialDiscountExpendableEntity materialDiscountExpendable
    ) {
        var expandablePercentType = ExpandableDiscountType.LEVEL_ZERO;
        for (var entry : EXPANDABLE_DISCOUNT_MAP.entrySet()) {
            if (productionCost.compareTo(entry.getValue()) >= 0) {
                expandablePercentType = entry.getKey();
            } else {
                break;
            }
        }
        var expandablePercent =
                Discounts.getExpandableDiscount(expandablePercentType, materialDiscountExpendable);

        return expandablePercent
                .multiply(expendablePercentPortion.divide(HUNDRED, MATH_CONTEXT), MATH_CONTEXT);
    }

    private BigDecimal calculateProductionCost(List<MaterialDiscountDto> metadata) {
        return metadata.stream()
                .map(MaterialDiscountDto::getProductionCost)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private BigDecimal getDiscountPortionByPartType(
            PartType partType,
            MaterialDiscountTypeEntity materialDiscountTypeEntity
    ) {
        return switch (partType) {
            case LLP -> materialDiscountTypeEntity.getLlpPortion();
            default -> materialDiscountTypeEntity.getNonExpendableNonLlpPortion();
        };
    }

    //todo fix NON_ROUTINE_MATERIAL - > LLP case TBD with PO
    private BigDecimal getDiscountTypePerPartType(
            PartType partType,
            MaterialDiscountsConditionEntity materialDiscountsCondition
    ) {
        return switch (partType) {
            case LLP -> materialDiscountsCondition.getLlpEscalated()
                    .add(materialDiscountsCondition.getLlpTiered(), MATH_CONTEXT);
            default -> materialDiscountsCondition.getNonLlpEscalated()
                    .add(materialDiscountsCondition.getNonLlpTiered(), MATH_CONTEXT);
        };
    }

    private DiscountLabourType getDiscountLabourTypeFromPartType(PartType partType) {
        return switch (partType) {
            case A_PART, CASE_AND_FRAME, LLP, PARTS_PACKAGE, KIT, COMPONENT -> DiscountLabourType.SCRAP_REPLACEMENT;
            case NON_ROUTINE_MATERIAL -> DiscountLabourType.REPAIR;
            case ROUTINE_MATERIAL -> DiscountLabourType.ROUTINE;
        };
    }
}