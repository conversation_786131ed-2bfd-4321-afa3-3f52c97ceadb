package de.dlh.lht.ti.exception.handler;

import com.auth0.jwt.exceptions.TokenExpiredException;
import com.cleverpine.cpspringerrorutil.handler.BaseGlobalExceptionHandler;
import com.cleverpine.cpspringerrorutil.mapper.ExceptionTypeMapper;
import com.cleverpine.cpspringerrorutil.model.ErrorResponseModel;
import com.cleverpine.viravaspringhelper.error.exception.InvalidTokenAuthenticationException;
import com.cleverpine.viravaspringhelper.error.exception.ViravaAuthenticationException;
import de.dlh.lht.ti.exception.AnkaImporterServiceException;
import de.dlh.lht.ti.exception.AuthenticationException;
import de.dlh.lht.ti.exception.BeginQuotationNotAllowedException;
import de.dlh.lht.ti.exception.ChangeOwnerNotAllowedException;
import de.dlh.lht.ti.exception.CurrencyNotSupportedException;
import de.dlh.lht.ti.exception.EntityNotFoundException;
import de.dlh.lht.ti.exception.EscalationDefaultValueMissingException;
import de.dlh.lht.ti.exception.OfferNumberValidationException;
import de.dlh.lht.ti.exception.PartQuantityException;
import de.dlh.lht.ti.exception.QuotationAlreadyBegunException;
import de.dlh.lht.ti.exception.QuotationStatusException;
import de.dlh.lht.ti.exception.SaveMaterialPricingNotAllowedException;
import de.dlh.lht.ti.exception.ValidationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.multipart.support.MissingServletRequestPartException;

@Slf4j
@ControllerAdvice
public class GlobalExceptionHandler extends BaseGlobalExceptionHandler {

    @Autowired
    public GlobalExceptionHandler(
            ExceptionTypeMapper exceptionTypeMapper,
            ExceptionHandlerLogger baseLogger,
            @Value("${spring.profiles.active:dev}") String activeProfile) {
        super(exceptionTypeMapper, baseLogger, false);
    }

    @ExceptionHandler(Exception.class)
    public ResponseEntity<ErrorResponseModel> handleException(Exception e) {
        log.error(e.getMessage(), e);
        return createErrorResponse(e, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @ExceptionHandler(AccessDeniedException.class)
    public ResponseEntity<ErrorResponseModel> handleAccessDeniedException(AccessDeniedException ex) {
        log.error(ex.getMessage(), ex);
        return createErrorResponse(ex, HttpStatus.FORBIDDEN);
    }

    @ExceptionHandler(MissingServletRequestParameterException.class)
    public ResponseEntity<ErrorResponseModel> handleMissingServletRequestParameterException(
            MissingServletRequestParameterException exception) {
        log.error(exception.getMessage(), exception);
        return createErrorResponse(exception, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(MissingServletRequestPartException.class)
    public ResponseEntity<ErrorResponseModel> handleMissingServletRequestPartException(MissingServletRequestPartException ex) {
        log.error(ex.getMessage(), ex);
        return createErrorResponse(ex, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<ErrorResponseModel> handleIllegalArgumentException(IllegalArgumentException exception) {
        log.error(exception.getMessage(), exception);
        return createErrorResponse(exception, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(EntityNotFoundException.class)
    public ResponseEntity<ErrorResponseModel> handleNotFoundEntityException(EntityNotFoundException exception) {
        log.error(exception.getMessage(), exception);
        return createErrorResponse(exception, HttpStatus.NOT_FOUND);
    }

    @ExceptionHandler(AnkaImporterServiceException.class)
    public ResponseEntity<ErrorResponseModel> handleAnkaImporterServiceException(AnkaImporterServiceException exception) {
        log.error(exception.getMessage(), exception);
        return createErrorResponse(exception, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @ExceptionHandler(IllegalStateException.class)
    public ResponseEntity<ErrorResponseModel> handleIllegalStateException(IllegalStateException ex) {
        log.error(ex.getMessage(), ex);
        return createErrorResponse(ex, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(AuthenticationException.class)
    public ResponseEntity<ErrorResponseModel> handleAuthenticationException(AuthenticationException ex) {
        log.error(ex.getMessage(), ex);
        return createErrorResponse(ex, HttpStatus.UNAUTHORIZED);
    }

    @ExceptionHandler(PartQuantityException.class)
    public ResponseEntity<ErrorResponseModel> handlePartQuantityException(PartQuantityException ex) {
        log.error(ex.getMessage(), ex);
        return createErrorResponse(ex, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(QuotationAlreadyBegunException.class)
    public ResponseEntity<ErrorResponseModel> handleQuotationAlreadyBegunException(QuotationAlreadyBegunException ex) {
        log.error(ex.getMessage(), ex);
        return createErrorResponse(ex, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(QuotationStatusException.class)
    public ResponseEntity<ErrorResponseModel> handleQuotationStatusException(QuotationStatusException ex) {
        log.error(ex.getMessage(), ex);
        return createErrorResponse(ex, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(ValidationException.class)
    public ResponseEntity<ErrorResponseModel> handleValidationException(ValidationException ex) {
        log.error(ex.getMessage(), ex);
        return createErrorResponse(ex, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(ViravaAuthenticationException.class)
    public ResponseEntity<ErrorResponseModel> handleViravaAuthenticationException(ViravaAuthenticationException ex) {
        log.error(ex.getMessage(), ex);
        return createErrorResponse(ex, HttpStatus.UNAUTHORIZED);
    }

    @ExceptionHandler(InvalidTokenAuthenticationException.class)
    public ResponseEntity<ErrorResponseModel> handleInvalidTokenAuthenticationException(InvalidTokenAuthenticationException ex) {
        log.error(ex.getMessage(), ex);
        return createErrorResponse(ex, HttpStatus.UNAUTHORIZED);
    }

    @ExceptionHandler(TokenExpiredException.class)
    public ResponseEntity<ErrorResponseModel> handleTokenExpiredException(TokenExpiredException ex) {
        log.error(ex.getMessage(), ex);
        return createErrorResponse(ex, HttpStatus.UNAUTHORIZED);
    }

    @ExceptionHandler(BeginQuotationNotAllowedException.class)
    public ResponseEntity<ErrorResponseModel> handleBeginQuotationNotAllowedException(BeginQuotationNotAllowedException ex) {
        log.error(ex.getMessage(), ex);
        return createErrorResponse(ex, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(OfferNumberValidationException.class)
    public ResponseEntity<ErrorResponseModel> handleOfferNumberNotValidException(OfferNumberValidationException ex) {
        log.error(ex.getMessage(), ex);
        return createErrorResponse(ex, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(ChangeOwnerNotAllowedException.class)
    public ResponseEntity<ErrorResponseModel> handleChangeOwnerNotAllowedException(ChangeOwnerNotAllowedException ex) {
        log.error(ex.getMessage(), ex);
        return createErrorResponse(ex, HttpStatus.FORBIDDEN);
    }

    @ExceptionHandler(SaveMaterialPricingNotAllowedException.class)
    public ResponseEntity<ErrorResponseModel> handleSaveMaterialPricingNotAllowedException(SaveMaterialPricingNotAllowedException ex) {
        log.error(ex.getMessage(), ex);
        return createErrorResponse(ex, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ErrorResponseModel> handleMethodArgumentNotValidException(MethodArgumentNotValidException ex) {
        log.error(ex.getMessage(), ex);
        return createErrorResponse(ex, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(EscalationDefaultValueMissingException.class)
    public ResponseEntity<ErrorResponseModel> handleEscalationDefaultValueMissing(EscalationDefaultValueMissingException ex) {
        log.error(ex.getMessage(), ex);
        return createErrorResponse(ex, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(CurrencyNotSupportedException.class)
    public ResponseEntity<ErrorResponseModel> handleNotSupportedCurrencyException(CurrencyNotSupportedException ex) {
        log.error(ex.getMessage(), ex);
        return createErrorResponse(ex, HttpStatus.BAD_REQUEST);
    }
}
