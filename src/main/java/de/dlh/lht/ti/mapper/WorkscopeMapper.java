package de.dlh.lht.ti.mapper;

import de.dlh.lht.ti.dto.importer.WorkscopeDto;
import de.dlh.lht.ti.entity.WorkscopeEntity;
import de.dlh.lht.ti.enums.WorkscopeClass;
import de.dlh.lht.ti.importer.model.WorkscopeRaw;
import de.dlh.lht.ti.model.QuotationWorkscope;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper
public abstract class WorkscopeMapper {

    @Mapping(source = "workscopeClass", target = "propertyClass")
    public abstract QuotationWorkscope workscopeEntityToQuotationWorkscope(WorkscopeEntity workscopeEntity);

    public abstract WorkscopeDto workscopeRawToWorkscopeDto(WorkscopeRaw workscopeRaw);

    public abstract WorkscopeEntity workscopeDtoToWorkscopeEntity(WorkscopeDto workscopeDto);

    public abstract WorkscopeDto workscopeEntityToWorkscopeDto(WorkscopeEntity workscopeEntity);

    public abstract List<WorkscopeDto> workscopeRawListToWorkscopeDtoList(List<WorkscopeRaw> workscopeRawList);

    protected String workScopeClassToString(WorkscopeClass workscopeClass) {
        return workscopeClass.name();
    }
}
