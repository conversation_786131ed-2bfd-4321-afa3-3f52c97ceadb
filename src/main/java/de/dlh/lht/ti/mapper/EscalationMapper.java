package de.dlh.lht.ti.mapper;

import de.dlh.lht.ti.dto.EscalationPricingDto;
import de.dlh.lht.ti.entity.EscalationPricingEntity;
import de.dlh.lht.ti.model.Escalation;
import de.dlh.lht.ti.model.EscalationPricingInput;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

import java.util.List;

@Mapper
public abstract class EscalationMapper {

    @Mapping(target = "values.labourPrices.value", source = "labourPrices")
    @Mapping(target = "values.eparPrices.value", source = "eparPrices")
    @Mapping(target = "values.rfpLabour.value", source = "rfpLabour")
    @Mapping(target = "values.hcMaterialPrices.value", source = "hcMaterialPrices")
    @Mapping(target = "values.hcSubcontractPrices.value", source = "hcSubcontractPrices")
    @Mapping(target = "values.labourPrices.defaultValue", source = "labourPricesDefault")
    @Mapping(target = "values.eparPrices.defaultValue", source = "eparPricesDefault")
    @Mapping(target = "values.rfpLabour.defaultValue", source = "rfpLabourDefault")
    @Mapping(target = "values.hcMaterialPrices.defaultValue", source = "hcMaterialPricesDefault")
    @Mapping(target = "values.hcSubcontractPrices.defaultValue", source = "hcSubcontractPricesDefault")
    public abstract Escalation escalationEntityToEscalationPricingDto(EscalationPricingDto escalationPricingEntity);

    public abstract List<Escalation> escalationPricingDtoListToEscalationList(List<EscalationPricingDto> escalationPricingDtoList);

    public abstract EscalationPricingDto escalationPricingEntityToEscalationPricingDto(EscalationPricingEntity escalationPricingEntity);

    public abstract EscalationPricingEntity escalationPricingInputToEscalationPricingEntity(
            EscalationPricingInput escalationPricingInput,
            @MappingTarget EscalationPricingEntity escalationPricingEntity);

}
