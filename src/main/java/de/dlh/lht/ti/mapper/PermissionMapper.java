package de.dlh.lht.ti.mapper;

import com.cleverpine.viravaspringhelper.core.BaseResource;
import de.dlh.lht.ti.auth.roles.Role;
import de.dlh.lht.ti.model.Permission;
import de.dlh.lht.ti.model.ScopeHolder;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper
public interface PermissionMapper {

    @Mapping(target = "resource.resource", source = "resource")
    Permission convertPermission(com.cleverpine.viravaspringhelper.dto.Permission permission);

    default List<Role> convertListOfRolesFromResources(List<String> rolesRaw) {
        if(rolesRaw == null || rolesRaw.isEmpty()) {
            return Collections.emptyList();
        }

        return rolesRaw.stream().map(this::convertRoleFromResources).filter(Objects::nonNull).toList();
    }

    default Role convertRoleFromResources(String roleRaw) {
        return Arrays.stream(Role.values())
                .filter(role -> role.getRoleName().equals(roleRaw))
                .findFirst().orElse(null);
    }

    default String convertResource(BaseResource resource) {
        return resource.resource();
    }

    default ScopeHolder convertScopeHolder(com.cleverpine.viravaspringhelper.dto.ScopeHolder scopeHolder) {
        return new ScopeHolder()
                .create(scopeHolder.canCreate())
                .read(scopeHolder.canRead())
                .update(scopeHolder.canUpdate())
                .delete(scopeHolder.canDelete());
    }
}
