package de.dlh.lht.ti.mapper;

import de.dlh.lht.ti.dto.TestrunItemDto;
import de.dlh.lht.ti.model.RfpItem;
import de.dlh.lht.ti.utils.Calculations;
import java.math.BigDecimal;
import org.mapstruct.Mapper;

@Mapper
public interface TestrunItemMapper {

    default RfpItem testrunDtoToRfpItem(
            TestrunItemDto testrunItemDto,
            BigDecimal exchangeRate) {
        var cost = Calculations.calculateCost(testrunItemDto.getMetadata(), exchangeRate);
        var price = testrunItemDto.getPrice() != null ? testrunItemDto.getPrice().doubleValue() : null;

        return new RfpItem()
                .id(testrunItemDto.getId())
                .name(testrunItemDto.getCluster().getName())
                .cost(cost.doubleValue())
                .ciHoursCost(0.0)
                .price(price)
                .isTestrunMaterial(true)
                .order(testrunItemDto.getCluster().getOrder());
    }
}
