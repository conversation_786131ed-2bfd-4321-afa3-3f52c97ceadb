package de.dlh.lht.ti.mapper;

import de.dlh.lht.ti.model.Z2RatingsRequest;
import java.util.HashMap;
import java.util.Map;
import org.mapstruct.Mapper;

@Mapper
public interface MaterialPricingControllerMapper {

    default Map<Long, Float> mapZ2RatingsToRawDataInput(Z2RatingsRequest z2RatingsRequest) {
        var z2RatingsInputMap = new HashMap<Long, Float>();
        z2RatingsRequest.getZ2ratingsInput().forEach(z2RatingInput ->
                z2RatingsInputMap.put(z2RatingInput.getId(), z2RatingInput.getZ2RatingInput())
        );
        return z2RatingsInputMap;
    }
}
