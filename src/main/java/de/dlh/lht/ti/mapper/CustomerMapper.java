package de.dlh.lht.ti.mapper;

import de.dlh.lht.ti.dto.importer.CustomerDto;
import de.dlh.lht.ti.entity.CustomerEntity;
import de.dlh.lht.ti.importer.model.CustomerRaw;
import org.mapstruct.Mapper;

@Mapper
public interface CustomerMapper {

    CustomerDto customerRawToCustomerDto(CustomerRaw customerRaw);

    CustomerEntity customerDtoToCustomerEntity(CustomerDto customerDto);

    CustomerDto customerEntityToCustomerDto(CustomerEntity customerEntity);
}
