package de.dlh.lht.ti.mapper;

import de.dlh.lht.ti.dto.importer.PartDto;
import de.dlh.lht.ti.entity.PartEntity;
import de.dlh.lht.ti.importer.model.PartRaw;
import de.dlh.lht.ti.model.PartType;
import org.mapstruct.Mapper;

@Mapper
public abstract class PartMapper {

    public abstract PartDto partRawToPartDto(PartRaw partRaw);

    public abstract PartDto partEntityToPartDto(PartEntity partEntity);

    public PartType enumsPartTypeToModelPartType(de.dlh.lht.ti.enums.PartType partType) {
        switch (partType) {
            case LLP, CASE_AND_FRAME -> {
                return PartType.A_PART;
            }
            default -> {
                return PartType.valueOf(partType.name());
            }
        }
    }

    protected de.dlh.lht.ti.enums.PartType getPartType(de.dlh.lht.ti.importer.model.PartType partType) {
        return switch (partType) {
            case REPAIR_A_PART, REPAIR_CASE_AND_FRAME, REPAIR_LLP, REPAIR_COMPONENT -> de.dlh.lht.ti.enums.PartType.NON_ROUTINE_MATERIAL;
            default -> de.dlh.lht.ti.enums.PartType.valueOf(partType.name());
        };
    }
}
