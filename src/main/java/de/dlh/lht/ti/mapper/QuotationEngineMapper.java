package de.dlh.lht.ti.mapper;

import de.dlh.lht.ti.entity.EngineEntity;
import de.dlh.lht.ti.entity.NavigationItemEntity;
import de.dlh.lht.ti.entity.QuotationEngineEntity;
import de.dlh.lht.ti.entity.QuotationEntity;
import de.dlh.lht.ti.entity.WorkscopeEntity;
import de.dlh.lht.ti.exception.QuotationStatusException;
import de.dlh.lht.ti.model.QuotationDetails;
import de.dlh.lht.ti.model.QuotationLight;
import de.dlh.lht.ti.model.QuotationStatus;
import de.dlh.lht.ti.model.QuotationWorkscope;
import de.dlh.lht.ti.service.contract.UserPermissionService;
import de.dlh.lht.ti.utils.Conversions;
import java.sql.Timestamp;
import java.time.ZonedDateTime;
import java.util.List;
import lombok.Setter;
import org.mapstruct.Context;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.springframework.beans.factory.annotation.Autowired;


import static de.dlh.lht.ti.utils.ErrorMessages.QUOTATION_STATUS_WITH_INDEX_DOES_NOT_EXISTS_ERROR_MESSAGE;

@Mapper
public abstract class QuotationEngineMapper {

    @Setter(onMethod = @__({@Autowired}))
    private WorkscopeMapper workscopeMapper;

    @Mapping(target = "offerNumber", source = "quotation.project.offerNumber")
    @Mapping(target = "version", source = "quotation.version")
    @Mapping(target = "position", source = "quotation.position")
    @Mapping(target = "scenario", source = "quotation.scenario")
    @Mapping(target = "customer", source = "quotation.customer.threeLetterCode")
    @Mapping(target = "engineType", source = "engine.name")
    @Mapping(target = "lastUpdate", source = "quotation.statusLastUpdated")
    @Mapping(target = "owner", source = "quotation.project.currentOwner")
    @Mapping(target = "status", source = "quotation.status")
    @Mapping(target = "canCurrentUserEdit", expression = "java(userPermissionService.isUserOwnerOrAdmin(entity.getQuotation().getProject().getCurrentOwner().getUNumber()))")
    public abstract QuotationLight quotationEngineEntityToQuotationLight(
            QuotationEngineEntity entity,
            @Context UserPermissionService userPermissionService);

    @Mapping(target = "id", source = "quotationEntity.id")
    @Mapping(target = "engine", source = "engineEntity")
    @Mapping(target = "project.offerNumber", source = "quotationEntity.project.offerNumber")
    @Mapping(target = "project.owner", source = "quotationEntity.project.currentOwner")
    @Mapping(target = "customer.name", source = "quotationEntity.customer.threeLetterCode")
    @Mapping(target = "customer.type", source = "quotationEntity.customer.type")
    @Mapping(target = "status", source = "quotationEntity.status")
    @Mapping(target = "lastUpdate", source = "quotationEntity.statusLastUpdated")
    @Mapping(target = "contractStart", source = "quotationEntity.contractStart")
    @Mapping(target = "contractEnd", source = "quotationEntity.contractEnd")
    @Mapping(target = "workscopes", source = "workscopeEntityList")
    public abstract QuotationDetails quotationEngineEntityToQuotationDetails(
            QuotationEntity quotationEntity,
            EngineEntity engineEntity,
            List<WorkscopeEntity> workscopeEntityList);

    public abstract List<QuotationLight> quotationEngineEntityListToQuotationLightList(List<QuotationEngineEntity> entities,
            @Context UserPermissionService userPermissionService);

    protected QuotationStatus dbStatusToQuotationStatus(int status) {
        try {
            return QuotationStatus.values()[status];
        } catch (IndexOutOfBoundsException e) {
            throw new QuotationStatusException(String.format(QUOTATION_STATUS_WITH_INDEX_DOES_NOT_EXISTS_ERROR_MESSAGE, status));
        }
    }

    protected long zonedDateTimeToLong(ZonedDateTime zonedDateTime) {
        return Conversions.zonedDateTimeToLong(zonedDateTime);
    }

    protected long timestampToLong(Timestamp timestamp) {
        return Conversions.timestampToLong(timestamp);
    }

    protected QuotationWorkscope workscopeEntityToQuotationWorkscope(WorkscopeEntity workscopeEntity) {
        return workscopeMapper.workscopeEntityToQuotationWorkscope(workscopeEntity);
    }
}
