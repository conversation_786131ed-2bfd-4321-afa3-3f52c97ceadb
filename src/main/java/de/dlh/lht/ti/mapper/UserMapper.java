package de.dlh.lht.ti.mapper;

import de.dlh.lht.ti.auth.UserPrincipal;
import de.dlh.lht.ti.entity.UserEntity;
import de.dlh.lht.ti.model.QuotationOwner;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper
public interface UserMapper {

    @Mapping(target = "username", source = "UNumber")
    QuotationOwner entityToQuotationOwner(UserEntity entity);

    List<QuotationOwner> entityUsersToOwnerRawList(List<UserEntity> users);

    UserEntity userPrincipalToUserEntity(UserPrincipal userPrincipal);

    UserEntity userPrincipalToUserEntity(UserPrincipal userPrincipal, @MappingTarget UserEntity entity);
}