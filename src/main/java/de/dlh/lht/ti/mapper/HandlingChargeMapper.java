package de.dlh.lht.ti.mapper;

import de.dlh.lht.ti.dto.HandlingChargeDto;
import de.dlh.lht.ti.dto.HandlingChargeValuesSetsDto;
import de.dlh.lht.ti.dto.api.ClusterDto;
import de.dlh.lht.ti.entity.HandlingChargeEntity;
import de.dlh.lht.ti.model.HandlingCharges;
import de.dlh.lht.ti.model.HandlingChargesCluster;
import de.dlh.lht.ti.model.HandlingChargesClusterRequest;
import de.dlh.lht.ti.model.HandlingChargesGlobalValues;
import de.dlh.lht.ti.model.HandlingChargesPart;
import de.dlh.lht.ti.model.HandlingChargesPartRequest;
import de.dlh.lht.ti.model.HandlingChargesRequest;
import de.dlh.lht.ti.model.PartType;
import de.dlh.lht.ti.utils.comparators.HandlingChargesClusterComparator;
import de.dlh.lht.ti.utils.comparators.HandlingChargesPartComparator;
import java.util.ArrayList;
import java.util.List;
import lombok.Setter;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.springframework.beans.factory.annotation.Autowired;


import static de.dlh.lht.ti.utils.GeneralValues.collectHandlingChargesGeneralValues;
import static de.dlh.lht.ti.utils.GeneralValues.getHandlingChargesGeneralValues;
import static java.util.function.Function.identity;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toMap;

@Mapper
public abstract class HandlingChargeMapper {

    @Setter(onMethod = @__({@Autowired}))
    private PartMapper partMapper;

    public HandlingCharges handlingChargesDtoListToHandlingCharges(List<HandlingChargeDto> handlingChargeDtoList) {
        var handlingChargesGroupedByClusters =
                handlingChargeDtoList.parallelStream().collect(groupingBy(HandlingChargeDto::getCluster));

        var handlingChargesEngineClusters = new ArrayList<HandlingChargesCluster>();
        var handlingChargeGlobalValuesSetsDto = new HandlingChargeValuesSetsDto();

        handlingChargesGroupedByClusters.forEach((clusterDto, handlingCharges) -> {
            var handlingChargesEngineCluster = clusterDtoToHandlingChargesCluster(
                    clusterDto,
                    handlingCharges,
                    handlingChargeGlobalValuesSetsDto);
            handlingChargesEngineClusters.add(handlingChargesEngineCluster);
        });

        handlingChargesEngineClusters.sort(new HandlingChargesClusterComparator());
        var handlingChargeValuesDto = getHandlingChargesGeneralValues(handlingChargeGlobalValuesSetsDto);

        var handlingChargesGlobalValues = new HandlingChargesGlobalValues()
                .baseCase(handlingChargeValuesDto.getBaseCase())
                .z1(handlingChargeValuesDto.getZ1())
                .z2(handlingChargeValuesDto.getZ2())
                .pma(handlingChargeValuesDto.getPma())
                .csm(handlingChargeValuesDto.getCsm())
                .oneItemCap(handlingChargeValuesDto.getOneItemCap())
                .lineItemCap(handlingChargeValuesDto.getLineItemCap());

        return new HandlingCharges()
                .globalValue(handlingChargesGlobalValues)
                .clusters(handlingChargesEngineClusters);
    }

    public void handlingChargesRequestToHandlingChargeDtoList(
            HandlingChargesRequest handlingChargesRequest,
            @MappingTarget List<HandlingChargeDto> handlingChargeDtoList) {
        var handlingChargeDtoByIdMap = handlingChargeDtoList
                .stream()
                .collect(toMap(handlingChargeDto -> handlingChargeDto.getHandlingCharge().getId(), identity()));

        handlingChargesRequest.getClusters()
                .stream()
                .map(HandlingChargesClusterRequest::getParts)
                .flatMap(List::stream)
                .forEach(handlingChargesPartRequest -> {
                    var handlingChargeDto = handlingChargeDtoByIdMap.get(handlingChargesPartRequest.getId());
                    if (handlingChargeDto == null) {
                        System.out.println();
                    }
                    handlingChargesPartRequestToHandlingChargeEntity(handlingChargesPartRequest, handlingChargeDto.getHandlingCharge());
                });
    }

    protected PartType enumsPartTypeToModelPartType(de.dlh.lht.ti.enums.PartType partType) {
        return partMapper.enumsPartTypeToModelPartType(partType);
    }

    @Mapping(target = "z1Margin", source = "z1")
    @Mapping(target = "z2Margin", source = "z2")
    @Mapping(target = "pmaMargin", source = "pma")
    @Mapping(target = "csmMargin", source = "csm")
    protected abstract void handlingChargesPartRequestToHandlingChargeEntity(
            HandlingChargesPartRequest handlingChargesPartRequest,
            @MappingTarget HandlingChargeEntity handlingCharge);

    @Mapping(target = ".", source = "handlingCharge")
    @Mapping(target = "name", source = "part.name")
    @Mapping(target = "order", source = "part.order")
    @Mapping(target = "type", source = "part.type")
    @Mapping(target = "quantity", source = "quantity")
    @Mapping(target = "z1", source = "handlingCharge.z1Margin")
    @Mapping(target = "z2", source = "handlingCharge.z2Margin")
    @Mapping(target = "pma", source = "handlingCharge.pmaMargin")
    @Mapping(target = "csm", source = "handlingCharge.csmMargin")
    @Mapping(target = "oneItemCap", source = "handlingCharge.oneItemCap")
    @Mapping(target = "lineItemCap", source = "handlingCharge.lineItemCap")
    protected abstract HandlingChargesPart handlingChargeDtoToHandlingChargesPart(HandlingChargeDto handlingChargeDto);

    private HandlingChargesCluster clusterDtoToHandlingChargesCluster(
            ClusterDto clusterDto,
            List<HandlingChargeDto> handlingChargeDtoList,
            @MappingTarget HandlingChargeValuesSetsDto handlingChargeGlobalValuesSetsDto) {
        var handlingChargesParts = new ArrayList<HandlingChargesPart>();
        var handlingChargeValuesSetsDto = new HandlingChargeValuesSetsDto();

        for (var handlingChargeDto : handlingChargeDtoList) {
            var handlingChargesPart = handlingChargeDtoToHandlingChargesPart(handlingChargeDto);
            handlingChargesParts.add(handlingChargesPart);
            collectHandlingChargesGeneralValues(handlingChargesPart, handlingChargeValuesSetsDto);
            collectHandlingChargesGeneralValues(handlingChargesPart, handlingChargeGlobalValuesSetsDto);
        }

        handlingChargesParts.sort(new HandlingChargesPartComparator());

        var partsType = partMapper.enumsPartTypeToModelPartType(handlingChargeDtoList.get(0).getPart().getType());
        var handlingChargeValuesDto = getHandlingChargesGeneralValues(handlingChargeValuesSetsDto);

        return new HandlingChargesCluster()
                .id(clusterDto.getId())
                .z1(handlingChargeValuesDto.getZ1())
                .z2(handlingChargeValuesDto.getZ2())
                .pma(handlingChargeValuesDto.getPma())
                .csm(handlingChargeValuesDto.getCsm())
                .oneItemCap(handlingChargeValuesDto.getOneItemCap())
                .lineItemCap(handlingChargeValuesDto.getLineItemCap())
                .partsType(partsType)
                .name(clusterDto.getName())
                .order(clusterDto.getOrder())
                .parts(handlingChargesParts);
    }
}
