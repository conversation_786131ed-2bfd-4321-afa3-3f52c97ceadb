package de.dlh.lht.ti.mapper;

import de.dlh.lht.ti.entity.NavigationItemEntity;
import de.dlh.lht.ti.model.NavigationStep;
import de.dlh.lht.ti.model.NavigationSteps;
import de.dlh.lht.ti.model.Progress;
import de.dlh.lht.ti.model.ProgressStep;
import de.dlh.lht.ti.utils.comparators.NavigationStepComparator;
import de.dlh.lht.ti.utils.comparators.ProgressStepComparator;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;


import static java.util.stream.Collectors.groupingBy;

@Mapper
public abstract class ProgressMapper {

    private static Optional<Boolean> checkEqualField(List<NavigationItemEntity> list) {
        var isValidList = list.stream().map(NavigationItemEntity::getIsValid).toList();

        if (isValidList.contains(null)) {
            return Optional.empty();
        }

        return Optional.of(isValidList.stream().allMatch(isValid -> isValid));
    }

    @Mapping(target = "name", source = "navigationItemEntity.progressStep.name")
    @Mapping(target = "id", source = "navigationItemEntity.progressStep.id")
    protected abstract ProgressStep navigationItemEntityToProgressStep(NavigationItemEntity navigationItemEntity);

    protected abstract List<ProgressStep> navigationItemEntityListToProgressStepList(List<NavigationItemEntity> navigationItemEntityList);

    protected abstract NavigationSteps navigationStepsToNavigationStepsModel(de.dlh.lht.ti.enums.NavigationSteps navigationSteps);

    public Progress navigationItemEntityListToProgress(List<NavigationItemEntity> navigationItemEntities) {
        if (navigationItemEntities == null || navigationItemEntities.isEmpty()) {
            return new Progress();
        }

        var navigationStepsListMap = navigationItemEntities.parallelStream()
                .collect(groupingBy(NavigationItemEntity::getNavigationStep));

        var navigationSteps = new ArrayList<NavigationStep>();

        navigationStepsListMap.forEach((navigationStepEntity, navigationItems) -> {
            var areAllProgressStepsValid = checkEqualField(navigationItems).orElse(null);
            var progressSteps = navigationItemEntityListToProgressStepList(navigationItems);
            progressSteps.sort(new ProgressStepComparator());

            var name = navigationStepsToNavigationStepsModel(navigationStepEntity.getName());
            var navigationStep = new NavigationStep()
                    .name(name)
                    .areAllProgressStepsValid(areAllProgressStepsValid)
                    .progressSteps(progressSteps)
                    .id(navigationStepEntity.getId());
            navigationSteps.add(navigationStep);
        });

        navigationSteps.sort(new NavigationStepComparator());

        return new Progress().navigationSteps(navigationSteps);
    }
}
