package de.dlh.lht.ti.mapper;

import de.dlh.lht.ti.entity.PartMetadataEntity;
import de.dlh.lht.ti.importer.model.PartMetadataRaw;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.mapstruct.Mapper;

@Mapper
public abstract class PartMetadataMapper {

    public List<PartMetadataEntity> partMetadataRawListToPartMetadataEntityList(
            List<PartMetadataRaw> rawPartMetadata,
            Long materialPricingItemId,
            Map<String, Long> allEngineWorkscopeIdsByName) {
        var newPartMetadata = new ArrayList<PartMetadataEntity>();
        rawPartMetadata.forEach(partMetadataRaw -> {
            var workscopeId = allEngineWorkscopeIdsByName.get(partMetadataRaw.getWorkscope());
            var partMetadata = partMetadataRawToPartMetadataEntity(partMetadataRaw);
            partMetadata.setMaterialPricingItemId(materialPricingItemId);
            partMetadata.setWorkscopeId(workscopeId);
            newPartMetadata.add(partMetadata);
        });
        return newPartMetadata;
    }

    protected abstract PartMetadataEntity partMetadataRawToPartMetadataEntity(PartMetadataRaw partMetadataRaw);
}
