package de.dlh.lht.ti.mapper;

import de.dlh.lht.ti.dto.RfpEngineItemDto;
import de.dlh.lht.ti.entity.RfpEngineItemEntity;
import de.dlh.lht.ti.model.RfpItem;
import de.dlh.lht.ti.model.RfpItemInput;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper
public interface RfpEngineItemMapper {

    @Mapping(target = "rfpEnginePrice", source = "price")
    @Mapping(target = "id", ignore = true)
    RfpEngineItemEntity toEntity(
            RfpItemInput priceInput,
            @MappingTarget RfpEngineItemEntity entity
    );

    List<RfpItem> toApiModelList(List<RfpEngineItemDto> rfpEngineItemDtos);

    @Mapping(target = "id", source = "labourPricingItemId")
    @Mapping(target = "name", source = "cluster.name")
    @Mapping(target = "cost", source = "rfpEngineBaseCost")
    @Mapping(target = "ciHoursCost", source = "rfpEngineCiHoursCost")
    @Mapping(target = "price", source = "rfpEnginePrice")
    @Mapping(target = "isTestrunMaterial", constant = "false")
    @Mapping(target = "order", source = "cluster.order")
    RfpItem toApiModel(RfpEngineItemDto rfpEngineItemDto);
}