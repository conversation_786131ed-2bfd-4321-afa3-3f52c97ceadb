package de.dlh.lht.ti.mapper;

import de.dlh.lht.ti.dto.SubcontractDto;
import de.dlh.lht.ti.entity.SubcontractPricingItemEntity;
import de.dlh.lht.ti.model.Subcontract;
import de.dlh.lht.ti.model.SubcontractInput;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper
public abstract class SubcontractPricingMapper {

    public abstract List<Subcontract> subcontractDtoListToSubcontractList(List<SubcontractDto> subcontractDtoList);

    public abstract SubcontractPricingItemEntity subcontractInputToSubcontractPricingItemEntity(
            SubcontractInput subcontractInput,
            @MappingTarget SubcontractPricingItemEntity subcontractPricingItemEntity);

    @Mapping(target = "name", source = "cluster.name")
    @Mapping(target = "order", source = "cluster.order")
    protected abstract Subcontract subcontractDtoToSubcontract(SubcontractDto subcontractDto);

    protected BigDecimal floatToBigDecimal(Float value) {
        return value != null ? BigDecimal.valueOf(value).setScale(1, RoundingMode.HALF_UP) : null;
    }
}
