package de.dlh.lht.ti.mapper;

import de.dlh.lht.ti.entity.LabourRateEntity;
import de.dlh.lht.ti.model.LabourRateInput;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;

@Mapper
public interface LabourRateMapper {

    LabourRateInput toApiModel(LabourRateEntity labourRateEntity);

    LabourRateEntity labourRateInputToLabourRateEntity(
            LabourRateInput labourRateInput,
            @MappingTarget LabourRateEntity entity,
            Long quotationId
    );
}
