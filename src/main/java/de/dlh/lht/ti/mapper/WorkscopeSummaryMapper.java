package de.dlh.lht.ti.mapper;

import de.dlh.lht.ti.dto.summary.WorkscopeSummaryDto;
import de.dlh.lht.ti.model.WorkscopeSummary;
import de.dlh.lht.ti.model.WorkscopeSummaryItem;
import de.dlh.lht.ti.utils.Calculations;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import org.mapstruct.Mapper;


import static java.util.stream.Collectors.groupingBy;

@Mapper
public abstract class WorkscopeSummaryMapper {

    public List<WorkscopeSummary> workscopeSummaryDtoListToWorkscopeSummaryList(
            List<WorkscopeSummaryDto> workscopeSummaryDtoList,
            BigDecimal usdExchangeRate) {
        if (workscopeSummaryDtoList == null || workscopeSummaryDtoList.isEmpty()) {
            return List.of();
        }

        var workscopeSummaries = new ArrayList<WorkscopeSummary>();

        var workscopeSummaryDtoByYearByWorkscopeDtoMap = workscopeSummaryDtoList.stream()
                .collect(groupingBy(WorkscopeSummaryDto::getWorkscope,
                        groupingBy(WorkscopeSummaryDto::getYear)));

        workscopeSummaryDtoByYearByWorkscopeDtoMap.forEach((workscopeDto, workscopeSummaryDtoByYearMap) -> {
            var workscopeSummary = new WorkscopeSummary()
                    .id(workscopeDto.getId())
                    .name(workscopeDto.getName());

            var workscopeSummaryItems = new ArrayList<WorkscopeSummaryItem>();
            workscopeSummaryDtoByYearMap.forEach((year, groupedWorkscopeSummaryDtoList) -> {
                if (groupedWorkscopeSummaryDtoList.size() != 1) {
                    return;
                }
                var workscopeSummaryItem = createWorkscopeSummaryItem(
                        groupedWorkscopeSummaryDtoList.get(0),
                        usdExchangeRate);
                workscopeSummaryItems.add(workscopeSummaryItem);
            });

            workscopeSummaryItems.sort(Comparator.comparing(WorkscopeSummaryItem::getYear));

            workscopeSummary.workscopeSummaryItems(workscopeSummaryItems);
            workscopeSummaries.add(workscopeSummary);
        });

        return workscopeSummaries;
    }

    private WorkscopeSummaryItem createWorkscopeSummaryItem(WorkscopeSummaryDto workscopeSummaryDto, BigDecimal usdExchangeRate) {
        var productionCost = Calculations.calculateUsdPlusEur(
                workscopeSummaryDto.getProductionCostUsd(),
                workscopeSummaryDto.getProductionCostEur(),
                usdExchangeRate);

        var workscopeSummaryItem = new WorkscopeSummaryItem()
                .year(workscopeSummaryDto.getYear())
                .productionCost(productionCost.intValue());

        workscopeSummaryItem.setDb2(12);
        workscopeSummaryItem.setDb2Percentage(12.00f);
        workscopeSummaryItem.setRevenue(5110355);
        workscopeSummaryItem.setDiscount(-26499);
        workscopeSummaryItem.setSurchargesCost(258360);
        workscopeSummaryItem.setProductionCostAfterDiscountAndSurcharges(4258483);
        workscopeSummaryItem.setDb2(851872);
        workscopeSummaryItem.setDb2Percentage(16.67f);
        workscopeSummaryItem.setEbit(638949);
        workscopeSummaryItem.setEbitPercentage(12.50f);
        workscopeSummaryItem.setEatPercentage(9.38f);
        workscopeSummaryItem.setNetMargin(7.20f);
        return workscopeSummaryItem;
    }
}
