package de.dlh.lht.ti.mapper;

import de.dlh.lht.ti.dto.Z2RatingDto;
import de.dlh.lht.ti.model.PartType;
import de.dlh.lht.ti.model.Z2Cluster;
import de.dlh.lht.ti.model.Z2Part;
import de.dlh.lht.ti.model.Z2RatingYear;
import de.dlh.lht.ti.model.Z2Ratings;
import de.dlh.lht.ti.utils.comparators.Z2RatingClusterComparator;
import de.dlh.lht.ti.utils.comparators.Z2RatingPartComparator;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.Setter;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.springframework.beans.factory.annotation.Autowired;


import static de.dlh.lht.ti.utils.Constants.HUNDRED;
import static de.dlh.lht.ti.utils.Constants.MATH_CONTEXT;
import static de.dlh.lht.ti.utils.Constants.ZERO;

@Mapper
public abstract class Z2RatingMapper {

    @Setter(onMethod = @__({@Autowired}))
    private PartMapper partMapper;

    public Z2Ratings z2RatingDtoToZ2Ratings(List<Z2RatingDto> z2RatingDtos) {
        if (z2RatingDtos == null || z2RatingDtos.isEmpty()) {
            return null;
        }

        var z2RatingsEntitiesGroupedByYear = z2RatingDtos.stream()
                .collect(Collectors.groupingBy(Z2RatingDto::getYear));
        z2RatingsEntitiesGroupedByYear = new TreeMap<>(z2RatingsEntitiesGroupedByYear);

        var z2RatingsPerYears = extractZ2RatingYears(z2RatingsEntitiesGroupedByYear);

        if (z2RatingsPerYears.isEmpty()) {
            return null;
        }

        var globalValue = extractGlobalValuePerObjectCollection(z2RatingsPerYears, Z2RatingYear::getZ2RatingInput);

        return new Z2Ratings()
                .globalZ2RatingInput(globalValue)
                .years(z2RatingsPerYears);
    }

    @Mapping(target = "name", source = "part.name")
    @Mapping(target = "type", source = "part.type")
    @Mapping(target = "order", source = "part.order")
    @Mapping(target = "currency", source = "oemZ1PriceCurrency")
    @Mapping(target = "clp", source = "oemZ1Price")
    @Mapping(target = "clpPercentage", source = ".")
    @Mapping(target = "z2RatingInput", source = "z2Rating")
    public abstract Z2Part z2RatingDtoToZ2Part(Z2RatingDto z2RatingDto);

    protected PartType enumsPartTypeToModelPartType(de.dlh.lht.ti.enums.PartType partType) {
        return partMapper.enumsPartTypeToModelPartType(partType);
    }

    protected abstract List<Z2Part> z2RatingDtoListToZ2PartList(List<Z2RatingDto> z2RatingDtos);

    protected Float calculateClpPercentage(Z2RatingDto z2RatingDto) {
        if (z2RatingDto == null
                || z2RatingDto.getOemZ1Price() == null
                || z2RatingDto.getOemZ1Price().equals(ZERO)
                || z2RatingDto.getZ2Cost() == null) {
            return null;
        }

        return (z2RatingDto.getZ2Cost().divide(z2RatingDto.getOemZ1Price(), MATH_CONTEXT)).multiply(HUNDRED)
                .floatValue();
    }

    private List<Z2RatingYear> extractZ2RatingYears(Map<String, List<Z2RatingDto>> z2RatingData) {
        return z2RatingData.entrySet().stream()
                .map(entry -> extractZ2RatingYear(entry.getKey(), entry.getValue())).toList();
    }

    private <T> Float extractGlobalValuePerObjectCollection(
            List<T> globalValueHolders,
            Function<T, Float> globalValueExtractor
    ) {
        if (globalValueHolders == null || globalValueHolders.isEmpty()) {
            return null;
        }
        var firstZ2rating = globalValueHolders.get(0);

        var globalValuePerYear = globalValueExtractor.apply(firstZ2rating);
        if (globalValuePerYear == null) {
            return null;
        }

        for (var holder : globalValueHolders) {
            if (!globalValuePerYear.equals(globalValueExtractor.apply(holder))) {
                return null;
            }
        }

        return globalValuePerYear;
    }

    private Z2RatingYear extractZ2RatingYear(String year, List<Z2RatingDto> z2RatingDtoList) {

        var globalValuePerYear =
                extractGlobalValuePerObjectCollection(z2RatingDtoList, (Z2RatingDto::getZ2Rating));
        var clusters = extractZ2ClustersData(z2RatingDtoList);

        return new Z2RatingYear()
                .z2RatingInput(globalValuePerYear)
                .year(year)
                .clusters(clusters);
    }

    private List<Z2Cluster> extractZ2ClustersData(List<Z2RatingDto> z2RatingDtoList) {
        var z2RatingsGroupedByClusters = z2RatingDtoList.stream()
                .collect(Collectors.groupingBy(Z2RatingDto::getCluster));
        var z2Clusters = new ArrayList<Z2Cluster>();

        z2RatingsGroupedByClusters.forEach(((cluster, z2Ratings) -> {
            if (z2Ratings == null || z2Ratings.isEmpty()) {
                return;
            }

            var globalClusterValue = extractGlobalValuePerObjectCollection(z2Ratings, (Z2RatingDto::getZ2Rating));
            var partsType = partMapper.enumsPartTypeToModelPartType(z2Ratings.get(0).getPart().getType());
            var z2Parts = z2RatingDtoListToZ2PartList(z2Ratings);
            z2Parts.sort(new Z2RatingPartComparator());

            var z2Cluster = new Z2Cluster()
                    .id(cluster.getId())
                    .name(cluster.getName())
                    .order(cluster.getOrder())
                    .z2RatingInput(globalClusterValue)
                    .partsType(partsType)
                    .parts(z2Parts);

            z2Clusters.add(z2Cluster);
        }));

        z2Clusters.sort(new Z2RatingClusterComparator());

        return z2Clusters;
    }
}
