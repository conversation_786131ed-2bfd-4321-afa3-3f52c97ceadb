package de.dlh.lht.ti.mapper;

import de.dlh.lht.ti.dto.RfpModuleDataDto;
import de.dlh.lht.ti.dto.RfpModuleDto;
import de.dlh.lht.ti.dto.RfpWorkscopeDto;
import de.dlh.lht.ti.dto.TaskMetadataDto;
import de.dlh.lht.ti.entity.RfpModuleEntity;
import de.dlh.lht.ti.model.RfpItem;
import de.dlh.lht.ti.model.RfpItemInput;
import de.dlh.lht.ti.model.RfpModuleData;
import de.dlh.lht.ti.model.RfpModuleWorkscope;
import de.dlh.lht.ti.utils.Calculations;
import de.dlh.lht.ti.utils.comparators.RfpModuleItemComparator;
import de.dlh.lht.ti.utils.comparators.RfpModuleWorkscopeComparator;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;


import static java.util.stream.Collectors.groupingBy;

@Mapper
public interface RfpModuleMapper {

    default RfpModuleData rfpModuleDataDtoListToRfpModule(
            List<RfpModuleDataDto> rfpModuleDataDtoList,
            BigDecimal exchangeRate) {
        var rfpModuleWorkscopeList = new ArrayList<RfpModuleWorkscope>();
        var rfpModuleDataDtoListByWorkscope = rfpModuleDataDtoList.stream().collect(groupingBy(RfpModuleDataDto::getWorkscope));

        rfpModuleDataDtoListByWorkscope.forEach((workscope, rfpModuleDataDtos) ->
                rfpModuleWorkscopeList.add(
                        rfpModuleDtoListToRfpModuleWorkscope(workscope, rfpModuleDataDtos, exchangeRate)));

        rfpModuleWorkscopeList.sort(new RfpModuleWorkscopeComparator());

        return new RfpModuleData()
                .workscopes(rfpModuleWorkscopeList);
    }

    RfpModuleEntity rfpItemInputToRfpModuleEntity(RfpItemInput rfpItemInput, @MappingTarget RfpModuleEntity rfpModuleEntity);

    private RfpModuleWorkscope rfpModuleDtoListToRfpModuleWorkscope(
            RfpWorkscopeDto workscope,
            List<RfpModuleDataDto> rfpModuleDataDtoList,
            BigDecimal exchangeRate) {
        var rfpItems = rfpModuleDataDtoListToRfpItemList(rfpModuleDataDtoList, exchangeRate);

        return new RfpModuleWorkscope()
                .id(workscope.getId())
                .name(workscope.getName())
                .rfpItems(rfpItems);
    }

    private List<RfpItem> rfpModuleDataDtoListToRfpItemList(
            List<RfpModuleDataDto> rfpModuleDataDtoList,
            BigDecimal exchangeRate) {
        var rfpItems = new ArrayList<RfpItem>();
        var rfpModuleDtoListMap = rfpModuleDataDtoList.stream().collect(groupingBy(RfpModuleDataDto::getRfpModuleDto));

        rfpModuleDtoListMap.forEach((rfpModuleDto, rfpModuleDataDtos) -> {
            var taskMetadataDtoList = rfpModuleDataDtos.stream().map(RfpModuleDataDto::getTask).toList();
            rfpItems.add(createRfpItem(rfpModuleDto, taskMetadataDtoList, exchangeRate));
        });

        rfpItems.sort(new RfpModuleItemComparator());

        return rfpItems;
    }

    private RfpItem createRfpItem(
            RfpModuleDto rfpModuleDto,
            List<TaskMetadataDto> taskMetadataDtoList,
            BigDecimal exchangeRate) {
        var rfpCostDto = Calculations.calculateTaskCostSum(taskMetadataDtoList, exchangeRate);
        var price = rfpModuleDto.getPrice() != null ? rfpModuleDto.getPrice().doubleValue() : null;
        return new RfpItem()
                .id(rfpModuleDto.getId())
                .name(rfpModuleDto.getCluster().getName())
                .cost(rfpCostDto.getCost().doubleValue())
                .ciHoursCost(rfpCostDto.getCiHoursCost().doubleValue())
                .price(price);
    }
}
