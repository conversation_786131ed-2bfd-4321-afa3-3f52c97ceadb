package de.dlh.lht.ti.mapper;

import de.dlh.lht.ti.entity.SubcontractMetadataEntity;
import de.dlh.lht.ti.importer.model.SubcontractMetadataRaw;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper
public interface SubcontractMetadataMapper {

    @Mapping(target = ".", source = "subcontractMetadataRaw")
    @Mapping(target = "subcontractPricingItemId", source = "subcontractPricingItemId")
    @Mapping(target = "workscopeId", source = "workscopeId")
    SubcontractMetadataEntity subcontractMetadataRawToSubcontractMetadataEntity(
            SubcontractMetadataRaw subcontractMetadataRaw,
            Long subcontractPricingItemId,
            Long workscopeId);
}
