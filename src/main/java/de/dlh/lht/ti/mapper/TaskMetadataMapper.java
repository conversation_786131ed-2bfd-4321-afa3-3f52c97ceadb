package de.dlh.lht.ti.mapper;

import de.dlh.lht.ti.entity.TaskMetadataEntity;
import de.dlh.lht.ti.importer.model.TaskMetadataRaw;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper
public interface TaskMetadataMapper {

    @Mapping(target = "eparCataloguePart", source = "isEpar")
    @Mapping(target = "repairTask", source = "isRepair")
    @Mapping(target = "routine", source = "isRoutine")
    @Mapping(target = "manHours", source = "duration")
    @Mapping(target = "name", source = "name")
    TaskMetadataEntity mapToEntity(TaskMetadataRaw source);

}