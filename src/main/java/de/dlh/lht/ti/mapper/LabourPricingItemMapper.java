package de.dlh.lht.ti.mapper;

import de.dlh.lht.ti.dto.ClusterTaskDto;
import de.dlh.lht.ti.entity.LabourPricingItemEntity;
import de.dlh.lht.ti.enums.TaskType;
import de.dlh.lht.ti.importer.model.LabourPricingItemRaw;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import org.mapstruct.Mapper;

@Mapper
public abstract class LabourPricingItemMapper {

    public List<LabourPricingItemEntity> labourPricingRawItemsToEntities(
            Map<Long, LabourPricingItemRaw> labourPricingItemRawMap,
            Long quotationEngineId
    ) {
        return labourPricingItemRawMap.entrySet().stream()
                .map(entry -> labourPricingRawItemToEntity(
                        entry.getValue(),
                        entry.getKey(),
                        quotationEngineId
                )).toList();
    }

    public List<LabourPricingItemEntity> labourPricingRawItemsToEntities(
            TaskType type,
            Map<String, ClusterTaskDto> clusterTasksDtos,
            List<LabourPricingItemRaw> labourPricingItemRaw,
            Long quotationEngineId
    ) {
        var clusterIdList = labourPricingItemRaw.stream()
                .map(item -> extractClusterId(item, clusterTasksDtos)).distinct().toList();

        return clusterIdList.stream()
                .map(clusterId -> labourPricingRawItemToEntity(type, quotationEngineId, clusterId))
                .filter(Objects::nonNull)
                .toList();
    }

    public LabourPricingItemEntity labourPricingRawItemToEntity(
            LabourPricingItemRaw source,
            Long clusterId,
            Long quotationEngineId
    ) {
        if (source == null || clusterId == null || quotationEngineId == null) {
            return null;
        }

        var labourPricingItemEntity = new LabourPricingItemEntity();

        labourPricingItemEntity.setType(mapTaskType(source));
        labourPricingItemEntity.setQuotationEngineId(quotationEngineId);
        labourPricingItemEntity.setClusterId(clusterId);

        return labourPricingItemEntity;
    }

    protected LabourPricingItemEntity labourPricingRawItemToEntity(
            TaskType type,
            Long quotationEngineId,
            Long clusterId
    ) {
        if (type == null || quotationEngineId == null || clusterId == null) {
            return null;
        }

        var labourPricingItem = new LabourPricingItemEntity();
        labourPricingItem.setClusterId(clusterId);
        labourPricingItem.setQuotationEngineId(quotationEngineId);
        labourPricingItem.setType(type);

        return labourPricingItem;
    }

    protected TaskType mapTaskType(LabourPricingItemRaw source) {
        if (source == null) {
            return null;
        }

        var sourceType = source.getTask().getType();

        return TaskType.valueOf(sourceType.name());
    }

    protected Long extractClusterId(
            LabourPricingItemRaw source,
            Map<String, ClusterTaskDto> clusterTaskDtoMap
    ) {
        if (source == null || clusterTaskDtoMap == null) {
            return null;
        }

        var clusterTaskDto = clusterTaskDtoMap.get(source.getTask().getName());
        if (clusterTaskDto == null) {
            return null;
        }

        return clusterTaskDto.getClusterId();
    }
}