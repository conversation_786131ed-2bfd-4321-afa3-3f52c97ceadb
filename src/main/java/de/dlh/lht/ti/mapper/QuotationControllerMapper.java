package de.dlh.lht.ti.mapper;

import de.dlh.lht.ti.dto.ContractTypesDto;
import de.dlh.lht.ti.dto.QuotationsQueryParametersDto;
import de.dlh.lht.ti.model.BeginQuotationRequest;
import de.dlh.lht.ti.model.QuotationsQueryParameters;
import org.mapstruct.Mapper;

@Mapper
public interface QuotationControllerMapper {

    QuotationsQueryParametersDto quotationsQueryParametersToQuotationsQueryParametersDto(
            QuotationsQueryParameters quotationsQueryParameters);

    ContractTypesDto beginQuotationRequestToContractTypesDto(BeginQuotationRequest beginQuotationRequest);
}
