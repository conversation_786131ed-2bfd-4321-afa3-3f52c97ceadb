package de.dlh.lht.ti.mapper;

import de.dlh.lht.ti.entity.TestrunMetadataEntity;
import de.dlh.lht.ti.importer.model.TestrunMetadataRaw;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper
public interface TestrunMetadataMapper {

    @Mapping(target = ".", source = "testrunMetadataRaw")
    @Mapping(target = "testrunItemId", source = "testrunItemId")
    @Mapping(target = "workscopeId", source = "workscopeId")
    TestrunMetadataEntity testrunMetadataRawToTestrunMetadataEntity(
            TestrunMetadataRaw testrunMetadataRaw,
            Long testrunItemId,
            Long workscopeId);
}
