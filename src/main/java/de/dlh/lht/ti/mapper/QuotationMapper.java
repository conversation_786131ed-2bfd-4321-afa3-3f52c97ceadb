package de.dlh.lht.ti.mapper;

import de.dlh.lht.ti.entity.CustomerEntity;
import de.dlh.lht.ti.entity.ProjectEntity;
import de.dlh.lht.ti.entity.QuotationEntity;
import de.dlh.lht.ti.importer.model.QuotationRaw;
import de.dlh.lht.ti.utils.Conversions;
import java.time.ZonedDateTime;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

@Mapper
public abstract class QuotationMapper {

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "contractStart", source = "quotationRaw.contractStart", qualifiedByName = "long_to_timestamp")
    @Mapping(target = "contractEnd", source = "quotationRaw.contractEnd", qualifiedByName = "long_to_timestamp")
    @Mapping(target = "project", source = "projectEntity")
    @Mapping(target = "customer", source = "customerEntity")
    public abstract QuotationEntity quotationRawToQuotationEntity(
            QuotationRaw quotationRaw,
            ProjectEntity projectEntity,
            CustomerEntity customerEntity);

    @Named("long_to_timestamp")
    protected ZonedDateTime longToZonedDateTime(long timestamp) {
        return Conversions.longToZonedDateTime(timestamp);
    }
}
