spring:
  application:
    name: ${APP_NAME:coca-backend-service}
  profiles:
    active: dev
  datasource:
    url: ${DB_JDBC_URL}
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}
    driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
  security:
    oauth2:
      resource-server:
        jwt:
          jwk-set-uri: ${SSO_JWK_SET_URI:https://sso.app.lhtcloud.com/auth/realms/LHT/protocol/openid-connect/certs}
  jpa:
    hibernate:
      naming:
        physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
  liquibase:
    change-log: classpath:db/changelog.xml

  kafka:
    bootstrap-servers: ${KAFKA_ENDPOINT}
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
    consumer:
      group-id: ${EVENT_HUB_GROUP_ID}
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
    properties:
      security.protocol: SASL_SSL
      sasl.mechanism: PLAIN
      sasl.jaas.config: org.apache.kafka.common.security.plain.PlainLoginModule required username="$ConnectionString" password="Endpoint=sb://${KAFKA_ENDPOINT}/;SharedAccessKeyName=${ACCESS_KEY_NAME};SharedAccessKey=${ACCESS_KEY};";
logging:
  level:
    root: info
  logstash:
    enabled: ${LHT_LOGGING_ENABLED}
    host: ${LHT_LOGGING_REMOTE_HOST}
    port: ${LHT_LOGGING_REMOTE_PORT}
    properties:
      lhtappid: ${APP_ID}
      namespace: ${LHT_LOGGING_NAMESPACE}
      stage: ${LHT_LOGGING_STAGE}
      serviceName: ${APP_NAME:coca-backend-service}

auth:
  token:
    jwkUrl: https://sso-kons.app.lhtcloud.com/auth/realms/LHT/protocol/openid-connect/certs
    emailPath: email
    usernamePath: username
    rolesPath: resource_access.core_calculation.roles
  custom:
    fullNamePath: name

sso:
  client-id: ${SSO_CLIENT_ID:core_calculation}

cors:
  allowed-origins: ${CORS_ALLOWED_ORIGINS:http://localhost:4200}

management:
  endpoints:
    enabled-by-default: false
    web:
      exposure:
        include: health, prometheus, metrics, info
  endpoint:
    info:
      enabled: true
    health:
      show-details: always
      enabled: true
      probes:
        enabled: true
    metrics:
      enabled: true
    prometheus:
      enabled: true
  info:
    java:
      enabled: true
    git:
      mode: full

server:
  servlet:
    context-path: /api
  shutdown: graceful

messaging:
  publisher:
    quotation-fetched-result: ${QUOTATION_FETCHED_RESULT}
  consumer:
    quotation-upload: ${QUOTATION_UPLOAD}

