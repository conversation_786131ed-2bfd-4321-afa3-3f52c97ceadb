openapi: 3.0.1
info:
  version: 1.0.6
  title: Core calculation tool api
  description: This is the Core Calculation(CoCa) tool YAML configuration
  contact:
    name: DevOps Factory
servers:
  - url: http://localhost:8080
    description: Generated server url
security:
  - BEARER_JWT: [ ]
tags:
  - name: User
    description: All User related methods.
  - name: Filters
    description: All Filters related methods.
  - name: Project
    description: All Project related methods.
  - name: Quotation
    description: All Quotation related methods.
  - name: Material Pricing
    description: All Material Pricing related methods.
  - name: Labour Pricing
    description: All Labour Pricing related methods.
  - name: Subcontract Pricing
    description: All Subcontract Pricing related methods.
  - name: Escalation Pricing
    description: All Escalation Pricing related methods.
  - name: Workscope Summary
    description: All Workscope Summary related methods.
paths:
  /users:
    get:
      description: This method returns all the possible users that quotation can be assigned
      tags:
        - User
      operationId: getAllUsers
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/QuotationOwnerResponse"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "500":
          $ref: "#/components/responses/InternalServerError"
  /users/details:
    put:
      tags:
        - User
      operationId: getOrCreateUserDetails
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UserDetailsResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
  /filters/quotations:
    get:
      description: This method returns all the possible filter options
      tags:
        - Filters
      operationId: getQuotationFilters
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/QuotationFiltersResponse"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "500":
          $ref: "#/components/responses/InternalServerError"
  /projects/{offerNumber}/owner:
    patch:
      tags:
        - Project
      description: This method changes a project's owner by a given offer number.
      operationId: changeOwner
      parameters:
        - name: offerNumber
          in: path
          required: true
          schema:
            type: string
            description: The offer number of a given project
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ChangeOwnerRequest"
      responses:
        "200":
          description: OK
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
  /quotations:
    get:
      description: This method returns a list of quotations with pagination and filtering applied
      tags:
        - Quotation
      operationId: getQuotations
      parameters:
        - name: quotationRequestParameters
          in: query
          schema:
            $ref: "#/components/schemas/QuotationsQueryParameters"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/QuotationsPageResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "500":
          $ref: "#/components/responses/InternalServerError"
  /quotations/{quotationId}:
    get:
      description: This method returns current quotation details
      tags:
        - Quotation
      operationId: getQuotationDetails
      parameters:
        - name: quotationId
          in: path
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/QuotationDetailsResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
    put:
      description: This method saves the quotation's contract types if not saved and initiates the quotation process
      tags:
        - Quotation
      operationId: beginQuotation
      parameters:
        - name: quotationId
          in: path
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/BeginQuotationRequest"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/QuotationDetailsResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
  /quotations/{quotationId}/handling-charges:
    get:
      description: This method returns current quotation handling charges
      tags:
        - Material Pricing
      operationId: getHandlingCharges
      parameters:
        - name: quotationId
          in: path
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/HandlingChargesResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
  /quotations/{quotationId}/handling-charges/save:
    put:
      description: This method returns current quotation handling charges
      tags:
        - Material Pricing
      operationId: saveHandlingCharges
      parameters:
        - name: quotationId
          in: path
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/HandlingChargesRequest"
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/HandlingChargesResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
  /quotations/{quotationId}/z2ratings:
    get:
      description: This method returns current quotation z2 ratings
      tags:
        - Material Pricing
      operationId: getZ2Ratings
      parameters:
        - name: quotationId
          in: path
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Z2RatingsResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
  /quotations/{quotationId}/z2ratings/save:
    put:
      description: This method save and returns current quotation z2 ratings
      tags:
        - Material Pricing
      operationId: saveZ2Ratings
      parameters:
        - name: quotationId
          in: path
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/Z2RatingsRequest"
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Z2RatingsResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
  /quotations/{quotationId}/labour-rates:
    get:
      description: This method returns current labour rates saved inputs and user progress for this quotation
      tags:
        - Labour Pricing
      operationId: getLabourRates
      parameters:
        - name: quotationId
          in: path
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/LabourRateResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
    put:
      description: This method saves and returns current quotation labour rates
      tags:
        - Labour Pricing
      operationId: saveLabourRates
      parameters:
        - name: quotationId
          in: path
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/LabourRateInput"
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/LabourRateResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
  /quotations/{quotationId}/rfp-engine:
    get:
      description: This method returns current labour EFP engine info related to current quotation and user progress for this quotation
      tags:
        - Labour Pricing
      operationId: getRfpEngineData
      parameters:
        - name: quotationId
          in: path
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/LabourRfpEngineResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
    put:
      description: This method saves and returns current quotation labour rfp engine data
      tags:
        - Labour Pricing
      operationId: saveRfpEngineData
      parameters:
        - name: quotationId
          in: path
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/RfpRequest"
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/LabourRfpEngineResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
  /quotations/{quotationId}/rfp-module:
    get:
      description: This method returns current RFP Module info related to current quotation and user progress for this quotation
      tags:
        - Labour Pricing
      operationId: getRfpModule
      parameters:
        - name: quotationId
          in: path
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/RfpModuleResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
    put:
      description: This method saves and returns current quotation RFP Module data
      tags:
        - Labour Pricing
      operationId: saveRfpModule
      parameters:
        - name: quotationId
          in: path
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/RfpRequest"
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/RfpModuleResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
  /quotations/{quotationId}/subcontracts:
    get:
      description: This method returns current Subcontract Pricing info related to current quotation and user progress for this quotation
      tags:
        - Subcontract Pricing
      operationId: getSubcontractPricing
      parameters:
        - name: quotationId
          in: path
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SubcontractPricingResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
    put:
      description: This method returns current Subcontract Pricing info related to current quotation and user progress for this quotation
      tags:
        - Subcontract Pricing
      operationId: saveSubcontractPricing
      parameters:
        - name: quotationId
          in: path
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SubcontractPricingRequest"
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SubcontractPricingResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
  /quotations/{quotationId}/escalations:
    get:
      description: This method returns current Escalations Pricing info related to current quotation and user progress for this quotation
      tags:
        - Escalation Pricing
      operationId: getEscalationPricing
      parameters:
        - name: quotationId
          in: path
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/EscalationsPricingResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
    put:
      description: This method updates the Escalations Pricing for current quotation
      tags:
        - Escalation Pricing
      operationId: saveEscalationPricing
      parameters:
        - name: quotationId
          in: path
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/EscalationPricingRequest"
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/EscalationsPricingResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
  /quotations/{quotationId}/summary:
    get:
      description: This method returns the Workscope Summary for the quotation if the user has finished all the previous screens
      tags:
        - Workscope Summary
      operationId: getWorkscopeSummary
      parameters:
        - name: quotationId
          in: path
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/WorkscopeSummaryResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
components:
  schemas:
    QuotationOwner:
      description: User object that represent quotation owner
      type: object
      properties:
        id:
          type: integer
          format: int64
          example: 1
        name:
          type: string
          example: Nikolas Barkow
        username:
          type: string
        email:
          type: string
      required:
        - id
    QuotationOwnerResponse:
      description: The response for Quotation Owner request.
      type: object
      properties:
        data:
          type: array
          items:
            $ref: "#/components/schemas/QuotationOwner"
        error:
          $ref: "#/components/schemas/Error"
    Resource:
      description: This is the resource object
      type: object
      properties:
        resource:
          type: string
    ScopeHolder:
      description: This is the scope holder object
      type: object
      properties:
        create:
          type: boolean
        read:
          type: boolean
        update:
          type: boolean
        delete:
          type: boolean
    Permission:
      description: This is the permission object
      type: object
      properties:
        resource:
          $ref: "#/components/schemas/Resource"
        scopeHolder:
          $ref: "#/components/schemas/ScopeHolder"
    UserDetails:
      type: object
      description: The user information
      properties:
        id:
          type: integer
          format: int64
        name:
          type: string
        email:
          type: string
        username:
          type: string
        permissions:
          type: array
          items:
            $ref: "#/components/schemas/Permission"
      required:
        - id
    UserDetailsResponse:
      description: The response for User Details request.
      type: object
      properties:
        data:
          $ref: "#/components/schemas/UserDetails"
        error:
          $ref: "#/components/schemas/Error"
    QuotationFilters:
      description: This is the object holding the possible filter options
      type: object
      properties:
        owners:
          type: array
          items:
            $ref: "#/components/schemas/QuotationOwner"
        quotationStatuses:
          type: array
          items:
            type: string
          example:
            - ANKA Validated
            - In Progress
            - Transferred
            - Completed
        engineTypes:
          type: array
          items:
            type: string
          example:
            - CFM56-5B
            - V2500
            - GE90
        customers:
          type: array
          items:
            type: string
          example:
            - DLH
            - IWG
            - WZZ
    QuotationFiltersResponse:
      description: The response for Filters request.
      type: object
      properties:
        data:
          $ref: "#/components/schemas/QuotationFilters"
        error:
          $ref: "#/components/schemas/Error"
    ChangeOwnerRequest:
      description: Change owner request body object.
      required:
        - id
      properties:
        id:
          type: integer
          format: int64
          minimum: 1
          description: The id of the project's new owner
    QuotationsQueryParameters:
      description: get quotation list query params wrapper
      type: object
      properties:
        pageSize:
          type: integer
          default: 20
          minimum: 0
          description: Quotations count in one quotation page
        pageIndex:
          type: integer
          default: 0
          minimum: 0
          description: Quotation page index
        sortBy:
          $ref: "#/components/schemas/QuotationSort"
        ownerId:
          type: integer
          format: int64
          minimum: 1
          description: The owner id of a given quotation
        offerNumber:
          type: string
          pattern: ^[0-9]{7}$
          description: The offer number of a given quotation
        version:
          type: integer
          minimum: 1
          description: The version of a given quotation
        position:
          type: integer
          minimum: 1
          description: The position of a given quotation
        scenario:
          type: integer
          minimum: 0
          description: The scenario of a given quotation
        quotationStatus:
          $ref: "#/components/schemas/QuotationStatus"
        engineType:
          type: string
          description: The engine type of a given quotation
        customer:
          type: string
          pattern: ^[A-Z]{3}$
          description: The customer of a given quotation
    QuotationLight:
      description: Representation of a quotation used in the list view
      type: object
      properties:
        id:
          type: integer
          format: int64
        offerNumber:
          type: string
        version:
          type: integer
        position:
          type: integer
        scenario:
          type: integer
        customer:
          type: string
        engineType:
          type: string
        lastUpdate:
          type: integer
          format: int64
        canCurrentUserEdit:
          type: boolean
        owner:
          $ref: "#/components/schemas/QuotationOwner"
        status:
          $ref: "#/components/schemas/QuotationStatus"
      required:
        - id
        - owner
    QuotationsPage:
      description: Page of quotations with pagination and filtering
      type: object
      properties:
        totalItems:
          type: integer
          format: int64
        totalPages:
          type: integer
        quotations:
          type: array
          items:
            $ref: "#/components/schemas/QuotationLight"
    QuotationsPageResponse:
      description: The response for Quotation Page request.
      type: object
      properties:
        data:
          $ref: "#/components/schemas/QuotationsPage"
        error:
          $ref: "#/components/schemas/Error"
    QuotationProject:
      description: Representation of a project parent to the current quotation
      type: object
      properties:
        offerNumber:
          type: string
          example: 5100207
        owner:
          $ref: "#/components/schemas/QuotationOwner"
    QuotationCustomer:
      description: Representation of a quotation project customer
      type: object
      properties:
        name:
          type: string
          example: LHT
        type:
          type: string
          example: LHT
    QuotationEngine:
      description: Representation of a engine related to the quotation
      type: object
      properties:
        name:
          type: string
          example: CFM56-5B
    QuotationWorkscope:
      description: Representation of a workscope related to the engine of the current quotation
      type: object
      properties:
        name:
          type: string
          example: CPR-LHC10
        class:
          type: string
          example: A
    ProgressStep:
      description: Representation of progress step
      type: object
      properties:
        name:
          $ref: "#/components/schemas/QuotationProgress"
        isValid:
          type: boolean
        id:
          type: integer
          format: int64
      required:
        - id
    NavigationStep:
      description: Representation of the navigation step holding the progress steps
      type: object
      properties:
        name:
          $ref: "#/components/schemas/NavigationSteps"
        progressSteps:
          type: array
          items:
            $ref: "#/components/schemas/ProgressStep"
        areAllProgressStepsValid:
          type: boolean
        id:
          type: integer
          format: int64
      required:
        - id
    Progress:
      description: Representation of the quotation's completion progress
      type: object
      properties:
        navigationSteps:
          type: array
          items:
            $ref: "#/components/schemas/NavigationStep"
    QuotationDetails:
      description: Representation of a quotation details used in the quotation details screen
      type: object
      properties:
        id:
          type: integer
          format: int64
          example: 1
        version:
          type: integer
          example: 1
        position:
          type: integer
          example: 1
        scenario:
          type: integer
          example: 0
        project:
          $ref: "#/components/schemas/QuotationProject"
        contractStart:
          type: integer
          format: int64
          example: 1616577123311
        contractEnd:
          type: integer
          format: int64
          example: 1616577123311
        customer:
          $ref: "#/components/schemas/QuotationCustomer"
        engine:
          $ref: "#/components/schemas/QuotationEngine"
        workscopes:
          type: array
          items:
            $ref: "#/components/schemas/QuotationWorkscope"
        lastUpdate:
          type: integer
          format: int64
          example: 1616577123311
        progress:
          $ref: "#/components/schemas/Progress"
        status:
          $ref: "#/components/schemas/QuotationStatus"
        usdExchangeRate:
          type: number
          format: double
          example: 1.07
        timeAndMaterial:
          type: boolean
          default: true
        routineFixedPrices:
          type: boolean
          default: false
        canCurrentUserEdit:
          type: boolean
      required:
        - id
    QuotationDetailsResponse:
      description: The response for Quotation Details request.
      type: object
      properties:
        data:
          $ref: "#/components/schemas/QuotationDetails"
        error:
          $ref: "#/components/schemas/Error"
    BeginQuotationRequest:
      description: Begin quotation request body object
      required:
        - routineFixedPrices
      type: object
      properties:
        routineFixedPrices:
          type: boolean
    HandlingChargesValues:
      description: Representation of a wrapper for the handling charges values
      type: object
      properties:
        z1:
          type: number
          format: float
          minimum: 0
          nullable: true
        z2:
          type: number
          format: float
          minimum: 0
          nullable: true
        pma:
          type: number
          format: float
          minimum: 0
          nullable: true
        csm:
          type: number
          format: float
          minimum: 0
          nullable: true
        oneItemCap:
          type: integer
          minimum: 1
          nullable: true
        lineItemCap:
          type: integer
          minimum: 1
          nullable: true
    HandlingChargesGlobalValues:
      description: Representation of a global values wrapper applied for all parts in handling charges
      type: object
      allOf:
        - $ref: "#/components/schemas/HandlingChargesValues"
      properties:
        baseCase:
          type: number
          format: float
          nullable: true
    HandlingChargesPart:
      description: Representation of a part for an specific engine related in usage for handling charges
      type: object
      allOf:
        - $ref: "#/components/schemas/HandlingChargesValues"
      properties:
        id:
          type: integer
          format: int64
          example: 1
        name:
          type: string
          example: M1 - Blade LPC Stg.  1 (Fan Blade)
        order:
          type: integer
        type:
          $ref: "#/components/schemas/PartType"
        quantity:
          type: integer
          example: 1
      required:
        - id
    HandlingChargesCluster:
      description: Representation of a parts' cluster for an specific engine related in usage for handling charges
      type: object
      allOf:
        - $ref: "#/components/schemas/HandlingChargesValues"
      properties:
        id:
          type: integer
          format: int64
          example: 1
        name:
          type: string
          example: Fan Blade
        order:
          type: integer
        partsType:
          $ref: "#/components/schemas/PartType"
        parts:
          type: array
          items:
            $ref: "#/components/schemas/HandlingChargesPart"
      required:
        - id
    HandlingCharges:
      description: Representation of a quotation handling charges schema
      type: object
      properties:
        globalValue:
          $ref: "#/components/schemas/HandlingChargesGlobalValues"
        canCurrentUserEdit:
          type: boolean
        clusters:
          type: array
          items:
            $ref: "#/components/schemas/HandlingChargesCluster"
        progress:
          $ref: "#/components/schemas/Progress"
    HandlingChargesResponse:
      description: The response for Handling Charges request.
      type: object
      properties:
        data:
          $ref: "#/components/schemas/HandlingCharges"
        error:
          $ref: "#/components/schemas/Error"
    HandlingChargesPartRequest:
      description: Representation of a part for an specific engine related in usage for handling charges
      type: object
      required:
        - id
      allOf:
        - $ref: "#/components/schemas/HandlingChargesValues"
      properties:
        id:
          type: integer
          format: int64
          example: 1
    HandlingChargesClusterRequest:
      description: Representation of a parts cluster for an specific engine related in usage for handling charges
      type: object
      required:
        - id
        - parts
      allOf:
        - $ref: "#/components/schemas/HandlingChargesValues"
      properties:
        id:
          type: integer
          format: int64
          example: 1
        partsType:
          $ref: "#/components/schemas/PartType"
        parts:
          type: array
          items:
            $ref: "#/components/schemas/HandlingChargesPartRequest"
    HandlingChargesRequest:
      description: Representation of a quotation's handling charges request body
      type: object
      required:
        - clusters
      properties:
        clusters:
          type: array
          items:
            $ref: "#/components/schemas/HandlingChargesClusterRequest"
    Z2Part:
      description: Representation of a quotation z2 ratings data for part
      properties:
        id:
          type: integer
          format: int64
        name:
          type: string
        order:
          type: integer
        type:
          $ref: "#/components/schemas/PartType"
        currency:
          $ref: "#/components/schemas/Currency"
        clp:
          type: number
          format: double
        clpPercentage:
          type: number
          format: float
        z2Cost:
          type: number
          format: double
        z2RatingInput:
          type: number
          format: float
        quantity:
          type: integer
      required:
        - id
    Z2Cluster:
      description: Representation of a quotation z2 ratings data per cluster
      properties:
        id:
          type: integer
          format: int64
        name:
          type: string
        order:
          type: integer
        z2RatingInput:
          type: number
          format: float
        partsType:
          $ref: "#/components/schemas/PartType"
        parts:
          type: array
          items:
            $ref: "#/components/schemas/Z2Part"
      required:
        - id
    Z2RatingYear:
      description: Representation of a quotation z2 ratings data per year
      properties:
        year:
          type: string
          minimum: 4
          maximum: 4
        z2RatingInput:
          type: number
          format: float
        clusters:
          type: array
          items:
            $ref: "#/components/schemas/Z2Cluster"
    Z2Ratings:
      description: Representation of a quotation z2 ratings data
      type: object
      properties:
        globalZ2RatingInput:
          type: number
          format: float
        canCurrentUserEdit:
          type: boolean
        years:
          type: array
          items:
            $ref: "#/components/schemas/Z2RatingYear"
        progress:
          $ref: "#/components/schemas/Progress"
    Z2RatingsResponse:
      description: The response for Z2 Ratings request.
      type: object
      properties:
        data:
          $ref: "#/components/schemas/Z2Ratings"
        error:
          $ref: "#/components/schemas/Error"
    Z2ratingsInput:
      description: z2 ratings user input data
      type: object
      required:
        - id
      properties:
        id:
          type: integer
          format: int64
        z2RatingInput:
          type: number
          format: float
    Z2RatingsRequest:
      description: Representation of a quotation's z2 ratings request body
      type: object
      required:
        - z2ratingsInput
      properties:
        z2ratingsInput:
          type: array
          items:
            $ref: "#/components/schemas/Z2ratingsInput"
    LabourRateInput:
      description: General Labour Rates input wrapper.
      type: object
      properties:
        routineLabourRate:
          type: integer
        nonRoutineLabourRate:
          type: integer
        eparDiscount:
          type: number
          format: double
    LabourRate:
      description: Representation labour rate wrapper for quotation
      type: object
      properties:
        labourRate:
          $ref: "#/components/schemas/LabourRateInput"
        canCurrentUserEdit:
          type: boolean
        progress:
          $ref: "#/components/schemas/Progress"
    LabourRateResponse:
      description: The response for General Labour Rate request.
      type: object
      properties:
        data:
          $ref: "#/components/schemas/LabourRate"
        error:
          $ref: "#/components/schemas/Error"
    RfpItemInput:
      type: object
      properties:
        id:
          type: integer
          format: int64
          example: 1
        price:
          type: number
          format: double
          example: 9999.99
        isTestrunMaterial:
          type: boolean
          example: false
      required:
        - id
    RfpItem:
      type: object
      allOf:
        - $ref: "#/components/schemas/RfpItemInput"
      properties:
        name:
          type: string
        cost:
          type: number
          format: double
        ciHoursCost:
          type: number
          format: double
        order:
          type: integer
    RfpEngineData:
      description: Representation of a quotation rfp engine data
      type: object
      properties:
        ciIncluded:
          type: boolean
        rfpEngineItems:
          type: array
          items:
            $ref: "#/components/schemas/RfpItem"
    RfpEngine:
      description: Representation of a quotation rfp engine data
      type: object
      properties:
        rfpEngineData:
          $ref: "#/components/schemas/RfpEngineData"
        canCurrentUserEdit:
          type: boolean
        progress:
          $ref: "#/components/schemas/Progress"
    LabourRfpEngineResponse:
      description: The response for Rfp Engine data request.
      type: object
      properties:
        data:
          $ref: "#/components/schemas/RfpEngine"
        error:
          $ref: "#/components/schemas/Error"
    RfpModuleWorkscope:
      description: Representation of a RFP Module workscope
      properties:
        id:
          type: integer
          format: int64
        name:
          type: string
        rfpItems:
          type: array
          items:
            $ref: "#/components/schemas/RfpItem"
      required:
        - id
    RfpModuleData:
      description: Representation of a quotation RFP Module data
      type: object
      properties:
        isCiIncluded:
          type: boolean
        workscopes:
          type: array
          items:
            $ref: "#/components/schemas/RfpModuleWorkscope"
    RfpModule:
      description: Representation of a quotation RFP Module
      type: object
      properties:
        rfpModuleData:
          $ref: "#/components/schemas/RfpModuleData"
        canCurrentUserEdit:
          type: boolean
        progress:
          $ref: "#/components/schemas/Progress"
    RfpModuleResponse:
      description: The response for RFP Module request.
      type: object
      properties:
        data:
          $ref: "#/components/schemas/RfpModule"
        error:
          $ref: "#/components/schemas/Error"
    RfpRequest:
      description: Representation of a quotation RFP Engine and RFP Module input data
      type: object
      properties:
        ciIncluded:
          type: boolean
          example: false
        rfpItemInputs:
          type: array
          items:
            $ref: "#/components/schemas/RfpItemInput"
    SubcontractValue:
      description: Representation of a quotation subcontract values
      type: object
      properties:
        margin:
          type: number
          format: float
          nullable: true
          example: 15.9
        cap:
          type: integer
          example: 1000
          nullable: true
    Subcontract:
      description: Representation of a quotation subcontract
      type: object
      allOf:
        - $ref: "#/components/schemas/SubcontractValue"
      properties:
        id:
          type: integer
          format: int64
          example: 1
        name:
          type: string
          example: Fan Blade
        order:
          type: integer
          example: 2
      required:
        - id
    SubcontractPricing:
      description: Representation of a quotation Subcontract Pricing
      type: object
      properties:
        globalValues:
          $ref: "#/components/schemas/SubcontractValue"
        subcontracts:
          type: array
          items:
            $ref: "#/components/schemas/Subcontract"
        canCurrentUserEdit:
          type: boolean
        progress:
          $ref: "#/components/schemas/Progress"
    SubcontractPricingResponse:
      description: The response for Subcontract Pricing request.
      type: object
      properties:
        data:
          $ref: "#/components/schemas/SubcontractPricing"
        error:
          $ref: "#/components/schemas/Error"
    SubcontractInput:
      description: Representation of a quotation subcontract input
      type: object
      allOf:
        - $ref: "#/components/schemas/SubcontractValue"
      properties:
        id:
          type: integer
          format: int64
      required:
        - id
    SubcontractPricingRequest:
      description: Representation of a quotation Subcontract Pricing input data
      type: object
      properties:
        subcontractInputs:
          type: array
          items:
            $ref: "#/components/schemas/SubcontractInput"
    EscalationPricingInputValues:
      type: object
      properties:
        labourPrices:
          type: number
          format: float
        eparPrices:
          type: number
          format: float
        rfpLabour:
          type: number
          format: float
        hcMaterialPrices:
          type: number
          format: float
        hcSubcontractPrices:
          type: number
          format: float
    EscalationPricingInput:
      description: Representation of a quotation pricing escalation input per year
      type: object
      allOf:
        - $ref: "#/components/schemas/EscalationPricingInputValues"
      properties:
        id:
          type: integer
          format: int64
        year:
          type: string
          minimum: 4
          maximum: 4
      required:
        - id
    EscalationPricingRequest:
      description: Representation of a quotation Escalation Pricing input data
      type: object
      properties:
        escalationInputs:
          type: array
          items:
            $ref: "#/components/schemas/EscalationPricingInput"
    EscalationFieldValue:
      description: Representation of a quotation pricing escalation field values
      type: object
      properties:
        value:
          type: number
          format: float
        defaultValue:
          type: number
          format: float
    EscalationValues:
      description: Representation of a quotation pricing escalation values
      type: object
      properties:
        labourPrices:
          $ref: "#/components/schemas/EscalationFieldValue"
        eparPrices:
          $ref: "#/components/schemas/EscalationFieldValue"
        rfpLabour:
          $ref: "#/components/schemas/EscalationFieldValue"
        hcMaterialPrices:
          $ref: "#/components/schemas/EscalationFieldValue"
        hcSubcontractPrices:
          $ref: "#/components/schemas/EscalationFieldValue"
    Escalation:
      description: Representation of a quotation pricing escalation per year
      type: object
      properties:
        id:
          type: integer
          format: int64
        year:
          type: string
          minimum: 4
          maximum: 4
        values:
          $ref: "#/components/schemas/EscalationValues"
      required:
        - id
    EscalationsPricing:
      description: Representation of a quotation Escalations Pricing
      type: object
      properties:
        escalationsPricing:
          type: array
          items:
            $ref: "#/components/schemas/Escalation"
        isRfpContractSelected:
          type: boolean
        canCurrentUserEdit:
          type: boolean
        progress:
          $ref: "#/components/schemas/Progress"
    EscalationsPricingResponse:
      description: The response for Escalations Pricing.
      type: object
      properties:
        data:
          $ref: "#/components/schemas/EscalationsPricing"
        error:
          $ref: "#/components/schemas/Error"
    WorkscopeSummaryItem:
      description: Representation of a quotation Workscope Summary Item
      type: object
      properties:
        year:
          type: string
          minimum: 4
          maximum: 4
        revenue:
          type: integer
        productionCost:
          type: integer
        discount:
          type: integer
        surchargesCost:
          type: integer
        productionCostAfterDiscountAndSurcharges:
          type: integer
        db2:
          type: integer
        db2Percentage:
          type: number
          format: float
        ebit:
          type: integer
        ebitPercentage:
          type: number
          format: float
        eatPercentage:
          type: number
          format: float
        netMargin:
          type: number
          format: float
    WorkscopeSummary:
      description: Representation of a quotation Workscope Summary
      type: object
      properties:
        id:
          type: integer
          format: int64
        name:
          type: string
        workscopeSummaryItems:
          type: array
          items:
            $ref: "#/components/schemas/WorkscopeSummaryItem"
      required:
        - id
    WorkscopeSummaryResponse:
      description: The response for Workscope Summary request.
      type: object
      properties:
        data:
          type: array
          items:
            $ref: "#/components/schemas/WorkscopeSummary"
        error:
          $ref: "#/components/schemas/Error"
    QuotationSort:
      type: string
      enum:
        - STATUS
        - LAST_UPDATE
        - OFFER_NUMBER
        - CUSTOMER
        - ENGINE_TYPE
        - OWNER
      default: STATUS
    QuotationStatus:
      type: string
      enum:
        - ANKA_VALIDATED
        - IN_PROGRESS
        - TRANSFERRED
        - COMPLETED
      description: The completion status of a given quotation
    PartType:
      type: string
      enum:
        - A_PART
        - KIT
        - COMPONENT
        - PARTS_PACKAGE
        - ROUTINE_MATERIAL
        - NON_ROUTINE_MATERIAL
      default: A_PART
    Currency:
      type: string
      enum:
        - USD
        - EUR
      default: USD
    NavigationSteps:
      type: string
      enum:
        - COVER
        - MATERIAL_PRICING
        - LABOUR_PRICING
        - SUBCONTRACT_PRICING
        - PRICING_ESCALATION
        - WORKSCOPE_SUMMARY
    QuotationProgress:
      type: string
      enum:
        - COVER
        - HANDLING_CHARGES
        - Z2_RATINGS
        - LABOUR_RATE_AND_EPAR
        - RFP_ENGINE
        - RFP_MODULE
        - SUBCONTRACT_PRICING
        - PRICING_ESCALATION
        - WORKSCOPE_SUMMARY
    ApiError:
      required:
        - code
        - description
        - status
      type: object
      properties:
        status:
          type: integer
          format: int32
        code:
          type: string
        description:
          type: string
      description: Common API error model.
    ErrorResponse:
      description: This is the response object in case of errors, compliant with RFC7807
      type: object
      properties:
        error:
          $ref: "#/components/schemas/Error"
    Error:
      description: This is the error object
      type: object
      properties:
        type:
          type: string
        title:
          type: string
        status:
          type: integer
        detail:
          type: string
  responses:
    BadRequest:
      description: BAD REQUEST
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorResponse"
    Unauthorized:
      description: UNAUTHORIZED
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorResponse"
    Forbidden:
      description: FORBIDDEN
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorResponse"
    NotFound:
      description: NOT FOUND
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorResponse"
    InternalServerError:
      description: INTERNAL SERVER ERROR
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorResponse"
  securitySchemes:
    BEARER_JWT:
      type: http
      scheme: bearer
      bearerFormat: JWT
