openapi: 3.0.1
info:
  version: 1.0.0
  title: Core calculation tool api
  description: This is the Core Calculation(CoCa) tool YAML configuration
  contact:
    name: DevOps Factory
servers:
  - url: http://localhost:8080
    description: Generated server url
security:
  - BEARER_JWT: [ ]
tags:
  - name: Quotation
  - name: Filters
paths:
  /trigger-quotation-event:
    post:
      description: This method trigger quotation event manually for testing purposes
      tags:
        - Quotation
      operationId: triggerQuotationEvent
      responses:
        "200":
          description: "OK"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
components:
  schemas:
    WorkscopeClass:
      type: string
      enum:
        - A
        - B
        - C
      default: A
    PartType:
      type: string
      enum:
        - A_PART
        - CASE_AND_FRAME
        - LLP
        - KIT
        - COMPONENT
        - PARTS_PACKAGE
        - ROUTINE_MATERIAL
        - REPAIR_A_PART
        - REPAIR_CASE_AND_FRAME
        - REPAIR_LLP
        - REPAIR_COMPONENT
      default: A_PART
    TaskType:
      type: string
      enum:
        - RFP_ENGINE
        - RFP_MODULE
        - NON_ROUTINE
    QuotationsPageRaw:
      description: Page of quotations with pagination and filtering
      type: object
      properties:
        totalItems:
          type: integer
          format: int64
        totalPages:
          type: integer
        quotations:
          type: array
          items:
            $ref: '#/components/schemas/QuotationRaw'
    QuotationRaw:
      description: Representation of a quotation object sent to core calculation app
      type: object
      properties:
        version:
          type: integer
        position:
          type: integer
        scenario:
          type: integer
        usdExchangeRate:
          type: string
        contractStart:
          type: integer
          format: int64
        contractEnd:
          type: integer
          format: int64
        project:
          $ref: "#/components/schemas/ProjectRaw"
        customer:
          $ref: "#/components/schemas/CustomerRaw"
        engine:
          type: string
        engineVersion:
          type: string
        workscopes:
          type: array
          items:
            $ref: "#/components/schemas/WorkscopeRaw"
        materialPricingItems:
          type: array
          items:
            $ref: "#/components/schemas/MaterialPricingItemRaw"
        labourPricingItems:
          type: array
          items:
            $ref: "#/components/schemas/LabourPricingItemRaw"
        subcontractPricingItems:
          type: array
          items:
            $ref: "#/components/schemas/SubcontractPricingItemRaw"
        testrunItems:
          type: array
          items:
            $ref: "#/components/schemas/TestrunItemRaw"
    CustomerRaw:
      description: Representation of a quotation project customer
      type: object
      properties:
        threeLetterCode:
          type: string
        type:
          type: string
    ProjectRaw:
      description: Representation of a project parent to the current quotation
      type: object
      properties:
        offerNumber:
          type: string
        owner:
          description: Quotation owner U-number
          type: string
    WorkscopeRaw:
      description: Representation of a workscope related to the engine of the current quotation
      type: object
      properties:
        name:
          type: string
        workscopeClass:
          $ref: "#/components/schemas/WorkscopeClass"
    PartRaw:
      description: Representation of a engine general part data
      type: object
      properties:
        name:
          type: string
        type:
          $ref: "#/components/schemas/PartType"
    PartMetadataRaw:
      description: Representation of a part data coming from anka related to current part
      type: object
      properties:
        workscope:
          type: string
        quantity:
          type: integer
        oemZ1Price:
          type: string
        oemZ1PriceCurrency:
          type: string
        z1Value:
          type: string
        z1Currency:
          type: string
        z1WeightedQuantity:
          type: string
        z2Value:
          type: string
        z2Currency:
          type: string
        z2WeightedQuantity:
          type: string
        pmaValue:
          type: string
        pmaCurrency:
          type: string
        pmaWeightedQuantity:
          type: string
        csmValue:
          type: string
        csmCurrency:
          type: string
        csmWeightedQuantity:
          type: string
        recyclingQuote:
          type: string
        year:
          type: string
        name:
          type: string
    MaterialPricingItemRaw:
      description: Representation of material pricing item holder of current part sent to core calculation app
      type: object
      properties:
        part:
          $ref: "#/components/schemas/PartRaw"
        partMetadata:
          type: array
          items:
            $ref: "#/components/schemas/PartMetadataRaw"
    TaskRaw:
      description: Representation of a task
      type: object
      properties:
        name:
          type: string
        type:
          $ref: "#/components/schemas/TaskType"
    TaskMetadataRaw:
      description: Representation of labour pricing data holder of current quotation to core calculation app
      type: object
      properties:
        workscope:
          type: string
        quantity:
          type: integer
        duration:
          type: string
        weightedQuantity:
          type: string
        isRoutine:
          type: boolean
        isEpar:
          type: boolean
        isRepair:
          type: boolean
        isCleaningAndInspection:
          type: boolean
        value:
          type: string
        currency:
          type: string
        year:
          type: string
        name:
          type: string
    LabourPricingItemRaw:
      description: Representation of labour pricing item holder of current task sent to core calculation app
      type: object
      properties:
        task:
          $ref: "#/components/schemas/TaskRaw"
        taskMetadata:
          type: array
          items:
            $ref: "#/components/schemas/TaskMetadataRaw"
    SubcontractMetadataRaw:
      description: Representation of subcontract pricing data holder of current quotation to core calculation app
      type: object
      properties:
        workscope:
          type: string
        quantity:
          type: integer
        weightedQuantity:
          type: string
        value:
          type: string
        currency:
          type: string
        year:
          type: string
        name:
          type: string
    SubcontractPricingItemRaw:
      description: Representation of subcontract pricing item holder of current part sent to core calculation app
      type: object
      properties:
        part:
          $ref: "#/components/schemas/PartRaw"
        subcontractMetadata:
          type: array
          items:
            $ref: "#/components/schemas/SubcontractMetadataRaw"
    TestrunMetadataRaw:
      description: Representation of testrun data holder of current quotation to core calculation app
      type: object
      properties:
        workscope:
          type: string
        quantity:
          type: integer
        weightedQuantity:
          type: string
        value:
          type: string
        currency:
          type: string
        year:
          type: string
        name:
          type: string
    TestrunItemRaw:
      description: Representation of testrun item holder sent to core calculation app
      type: object
      properties:
        testrunMetadata:
          type: array
          items:
            $ref: "#/components/schemas/TestrunMetadataRaw"
    QuotationUploadEvent:
      description: Representation of a quotation upload event
      type: object
      properties:
        quotationId:
          type: integer
          format: int64
        offerNumber:
          type: string
        quotationVersion:
          type: string
        quotationPosition:
          type: string
        quotationScenario:
          type: string
        filePath:
          type: string
      required:
        - quotationId
        - offerNumber
        - quotationVersion
        - quotationPosition
        - quotationScenario
        - filePath
    QuotationUploadResultEvent:
      description: Representation of a quotation upload result event
      type: object
      properties:
        quotationId:
          type: integer
          format: int64
        status:
          type: string
          enum:
            - SUCCESS
            - FAILURE
      required:
        - quotationId
        - status
    ApiError:
      required:
        - code
        - description
        - status
      type: object
      properties:
        status:
          type: integer
          format: int32
        code:
          type: string
        description:
          type: string
      description: Common API error model.
    ErrorResponse:
      description: This is the response object in case of errors, compliant with RFC7807
      type: object
      properties:
        error:
          $ref: "#/components/schemas/Error"
    Error:
      description: This is the error object
      type: object
      properties:
        type:
          type: string
        title:
          type: string
        status:
          type: integer
        detail:
          type: string
  responses:
    BadRequest:
      description: "BAD REQUEST"
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorResponse"
    Unauthorized:
      description: "UNAUTHORIZED"
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorResponse"
    Forbidden:
      description: "FORBIDDEN"
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorResponse"
    NotFound:
      description: "NOT FOUND"
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorResponse"
    InternalServerError:
      description: "INTERNAL SERVER ERROR"
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorResponse"
  securitySchemes:
    BEARER_JWT:
      type: http
      scheme: bearer
      bearerFormat: JWT
