<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet author="U143496" id="COCA-429">

        <createTable tableName="users">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="name" type="varchar(255)"/>
            <column name="email" type="varchar(255)"/>
            <column name="u_number" type="varchar(7)">
                <constraints unique="true"/>
            </column>
            <column name="created_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <addUniqueConstraint columnNames="name, u_number" constraintName="uc_users_name_u_number" tableName="users"/>

        <createTable tableName="projects">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="offer_number" type="varchar(7)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="original_owner_id" type="bigint">
                <constraints nullable="false" foreignKeyName="fk_projects_original_users" references="users(id)"/>
            </column>
            <column name="current_owner_id" type="bigint">
                <constraints nullable="false" foreignKeyName="fk_projects_current_users" references="users(id)"/>
            </column>
            <column name="created_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <createTable tableName="customers">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="three_letter_code" type="varchar(255)">
                <constraints nullable="false"/>
            </column>
            <column name="type" type="varchar(3)">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <addUniqueConstraint columnNames="three_letter_code, type"
                             constraintName="uc_customers_three_letter_code_type"
                             tableName="customers"/>

        <createTable tableName="quotations">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="contract_start" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="contract_end" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="position" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="version" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="scenario" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="engine_version" type="varchar(20)"/>
            <column name="usd_exchange_rate" type="decimal(15,8)">
                <constraints nullable="false"/>
            </column>
            <column name="status" type="int" defaultValue="0">
                <constraints nullable="false"/>
            </column>
            <column name="status_last_updated" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="time_and_material" type="boolean" defaultValueBoolean="true">
                <constraints nullable="false"/>
            </column>
            <column name="routine_fixed_prices" type="boolean" defaultValueBoolean="true">
                <constraints nullable="false"/>
            </column>
            <column name="customer_id" type="bigint">
                <constraints nullable="false" foreignKeyName="fk_quotations_customers" references="customers(id)"/>
            </column>
            <column name="project_id" type="bigint">
                <constraints nullable="false" foreignKeyName="fk_quotations_projects" references="projects(id)"/>
            </column>
            <column name="created_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <addUniqueConstraint columnNames="position, version, scenario, project_id"
                             constraintName="uc_quotations_position_version_scenario_project_id"
                             tableName="quotations"/>

        <createTable tableName="engines">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="name" type="varchar(255)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="created_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <createTable tableName="workscopes">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="name" type="varchar(255)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="class" type="varchar(1)">
                <constraints nullable="false"/>
            </column>
            <column name="is_system" type="boolean">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <createTable tableName="quotation_engines">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="quotation_id" type="bigint">
                <constraints nullable="false"
                             foreignKeyName="fk_quotation_engines_quotations"
                             references="quotations(id)"/>
            </column>
            <column name="engine_id" type="bigint">
                <constraints nullable="false" foreignKeyName="fk_quotation_engines_engines" references="engines(id)"/>
            </column>
            <column name="created_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <addUniqueConstraint columnNames="quotation_id, engine_id"
                             constraintName="uc_quotation_engines_quotation_id_engine_id"
                             tableName="quotation_engines"/>

        <createTable tableName="quotation_engine_workscopes">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="quotation_engine_id" type="bigint">
                <constraints nullable="false"
                             foreignKeyName="fk_quotation_engine_workscopes_quotation_engines"
                             references="quotation_engines(id)"/>
            </column>
            <column name="workscope_id" type="bigint">
                <constraints nullable="false"
                             foreignKeyName="fk_quotation_engine_workscopes_workscopes"
                             references="workscopes(id)"/>
            </column>
            <column name="created_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <addUniqueConstraint columnNames="quotation_engine_id, workscope_id"
                             constraintName="uc_quotation_engine_workscopes_quotation_engine_id_workscope_id"
                             tableName="quotation_engine_workscopes"/>

        <createTable tableName="clusters">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="name" type="varchar(255)">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <createTable tableName="engine_clusters">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="sort_order" type="int"/>
            <column name="engine_id" type="bigint">
                <constraints nullable="false" foreignKeyName="fk_engine_clusters_engines" references="engines(id)"/>
            </column>
            <column name="cluster_id" type="bigint">
                <constraints nullable="false" foreignKeyName="fk_engine_clusters_clusters" references="clusters(id)"/>
            </column>
            <column name="created_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <addUniqueConstraint columnNames="engine_id, cluster_id"
                             constraintName="uc_engine_clusters_engine_id_cluster_id"
                             tableName="engine_clusters"/>

        <createTable tableName="parts">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="name" type="varchar(255)">
                <constraints nullable="false"/>
            </column>
            <column name="type" type="varchar(255)" defaultValue="">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <addUniqueConstraint columnNames="name, type" constraintName="uc_parts_name_type" tableName="parts"/>

        <createTable tableName="engine_cluster_parts">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="sort_order" type="int"/>
            <column name="engine_cluster_id" type="bigint">
                <constraints nullable="false"
                             foreignKeyName="fk_engine_cluster_parts_engine_clusters"
                             references="engine_clusters(id)"/>
            </column>
            <column name="part_id" type="bigint">
                <constraints nullable="false"
                             foreignKeyName="fk_engine_cluster_parts_parts"
                             references="parts(id)"/>
            </column>
            <column name="created_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <addUniqueConstraint columnNames="engine_cluster_id, part_id"
                             constraintName="uc_engine_cluster_parts_engine_cluster_id_part_id"
                             tableName="engine_cluster_parts"/>

        <createTable tableName="material_pricing_items">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="quotation_engine_id" type="bigint">
                <constraints nullable="false"
                             foreignKeyName="fk_material_pricing_items_quotation_engines"
                             references="quotation_engines(id)"/>
            </column>
            <column name="cluster_id" type="bigint">
                <constraints nullable="false" foreignKeyName="fk_material_pricing_items_clusters"
                             references="clusters(id)"/>
            </column>
            <column name="part_id" type="bigint">
                <constraints nullable="false" foreignKeyName="fk_material_pricing_items_parts" references="parts(id)"/>
            </column>
            <column name="created_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <addUniqueConstraint columnNames="quotation_engine_id, cluster_id, part_id"
                             constraintName="uc_material_pricing_items_quotation_engine_id_cluster_id_part_id"
                             tableName="material_pricing_items"/>

        <createTable tableName="handling_charges">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="z1_margin" type="float"/>
            <column name="z2_margin" type="float"/>
            <column name="pma_margin" type="float"/>
            <column name="csm_margin" type="float"/>
            <column name="one_item_cap" type="int"/>
            <column name="line_item_cap" type="int"/>
            <column name="material_pricing_item_id" type="bigint">
                <constraints nullable="false" unique="true"
                             foreignKeyName="fk_handling_charges_material_pricing_items"
                             references="material_pricing_items(id)"/>
            </column>
            <column name="created_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <createTable tableName="part_metadata">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="z1_value" type="decimal(15,8)"/>
            <column name="z1_currency" type="varchar(3)"/>
            <column name="z1_weighted_quantity" type="decimal(15,8)"/>
            <column name="z2_value" type="decimal(15,8)"/>
            <column name="z2_currency" type="varchar(3)"/>
            <column name="z2_weighted_quantity" type="decimal(15,8)"/>
            <column name="pma_value" type="decimal(15,8)"/>
            <column name="pma_currency" type="varchar(3)"/>
            <column name="pma_weighted_quantity" type="decimal(15,8)"/>
            <column name="csm_value" type="decimal(15,8)"/>
            <column name="csm_currency" type="varchar(3)"/>
            <column name="csm_weighted_quantity" type="decimal(15,8)"/>
            <column name="oem_z1_price" type="decimal(15,8)"/>
            <column name="oem_z1_price_currency" type="varchar(3)"/>
            <column name="quantity" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="recycling_quote" type="decimal(15,8)"/>
            <column name="year" type="varchar(4)">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="varchar(255)"/>
            <column name="material_pricing_item_id" type="bigint">
                <constraints nullable="false"
                             foreignKeyName="fk_part_metadata_material_pricing_items"
                             references="material_pricing_items(id)"/>
            </column>
            <column name="workscope_id" type="bigint">
                <constraints nullable="false" foreignKeyName="fk_part_metadata_workscopes" references="workscopes(id)"/>
            </column>
            <column name="created_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <createTable tableName="z2_ratings">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="year" type="varchar(4)">
                <constraints nullable="false"/>
            </column>
            <column name="z2_rating" type="float"/>
            <column name="material_pricing_item_id" type="bigint">
                <constraints nullable="false"
                             foreignKeyName="fk_z2_ratings_material_pricing_item_id"
                             references="material_pricing_items(id)"/>
            </column>
            <column name="created_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <createTable tableName="navigation_steps">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="name" type="varchar(50)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="created_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <createTable tableName="progress_steps">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="name" type="varchar(50)">
                <constraints nullable="false" unique="false"/>
            </column>
            <column name="created_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <createTable tableName="navigation_items">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="is_valid" type="boolean"/>
            <column name="quotation_id" type="bigint">
                <constraints nullable="false" foreignKeyName="fk_navigation_items_quotations"
                             references="quotations(id)"/>
            </column>
            <column name="navigation_step_id" type="bigint">
                <constraints nullable="false" foreignKeyName="fk_navigation_items_navigation_steps"
                             references="navigation_steps(id)"/>
            </column>
            <column name="progress_step_id" type="bigint">
                <constraints nullable="false" foreignKeyName="fk_navigation_items_progress_steps"
                             references="progress_steps(id)"/>
            </column>
            <column name="created_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <addUniqueConstraint tableName="navigation_items"
                             constraintName="uc_navigation_items_quotation_id_navigation_step_id_progress_step_id"
                             columnNames="quotation_id, navigation_step_id, progress_step_id"/>

        <createIndex indexName="index_material_pricing_item_id" tableName="part_metadata">
            <column name="material_pricing_item_id"/>
        </createIndex>

        <createIndex indexName="index_quotation_engine_id" tableName="material_pricing_items">
            <column name="quotation_engine_id"/>
        </createIndex>

        <createIndex indexName="index_quotation_id" tableName="quotation_engines">
            <column name="quotation_id"/>
        </createIndex>
    </changeSet>

    <changeSet id="COCA-286" author="U142694">

        <createTable tableName="tasks">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="name" type="varchar(255)">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <createTable tableName="engine_cluster_tasks">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="engine_cluster_id" type="bigint">
                <constraints nullable="false"
                             foreignKeyName="fk_engine_cluster_tasks_engine_clusters"
                             references="engine_clusters(id)"/>
            </column>
            <column name="task_id" type="bigint">
                <constraints nullable="false" foreignKeyName="fk_engine_cluster_tasks_tasks"
                             references="tasks(id)"/>
            </column>
            <column name="created_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <addUniqueConstraint columnNames="engine_cluster_id, task_id"
                             constraintName="uc_engine_cluster_tasks_engine_cluster_id_task_id"
                             tableName="engine_cluster_tasks"/>

        <createTable tableName="labour_pricing_items">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="quotation_engine_id" type="bigint">
                <constraints nullable="false"
                             foreignKeyName="fk_labour_pricing_items_quotation_engines"
                             references="quotation_engines(id)"/>
            </column>
            <column name="cluster_id" type="bigint">
                <constraints nullable="false"
                             foreignKeyName="fk_labour_pricing_items_clusters"
                             references="clusters(id)"/>
            </column>
            <column name="type" type="varchar(11)" defaultValue="">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <addUniqueConstraint columnNames="quotation_engine_id, cluster_id"
                             constraintName="uc_labour_pricing_items_engine_id_cluster_id"
                             tableName="labour_pricing_items"/>

        <createTable tableName="task_metadata">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="workscope_id" type="bigint">
                <constraints nullable="false"
                             foreignKeyName="fk_task_metadata_workscopes"
                             references="workscopes(id)"/>
            </column>
            <column name="labour_pricing_item_id" type="bigint">
                <constraints nullable="false"
                             foreignKeyName="fk_task_metadata_labour_pricing_items"
                             references="labour_pricing_items(id)"/>
            </column>
            <column name="year" type="varchar(4)">
                <constraints nullable="false"/>
            </column>
            <column name="is_routine" type="boolean">
                <constraints nullable="false"/>
            </column>
            <column name="is_cleaning_and_inspection" type="boolean" defaultValueBoolean="false">
                <constraints nullable="false"/>
            </column>
            <column name="man_hours" type="decimal(15,8)"/>
            <column name="quantity" type="int"/>
            <column name="weighted_quantity" type="decimal(15,8)"/>
            <column name="is_epar_catalogue_part" type="boolean">
                <constraints nullable="false"/>
            </column>
            <column name="is_repair_task" type="boolean">
                <constraints nullable="false"/>
            </column>
            <column name="label" type="varchar(255)">
                <constraints nullable="false"/>
            </column>
            <column name="value" type="decimal(15,8)"/>
            <column name="currency" type="varchar(3)"/>
            <column name="created_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <createTable tableName="labour_rates">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="quotation_id" type="bigint">
                <constraints nullable="false" foreignKeyName="fk_labour_rates_quotations" references="quotations(id)"/>
            </column>
            <column name="routine_labour_rate" type="int"/>
            <column name="non_routine_labour_rate" type="int"/>
            <column name="epar_discount" type="decimal(3,1)"/>
            <column name="created_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="COCA-547-integrate-epar-excel" author="U143496">

        <createTable tableName="epar_prices">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="cleaning_and_inspection" type="decimal(15,8)">
                <constraints nullable="false"/>
            </column>
            <column name="repair" type="decimal(15,8)">
                <constraints nullable="false"/>
            </column>
            <column name="currency" type="varchar(3)">
                <constraints nullable="false"/>
            </column>
            <column name="year" type="varchar(4)">
                <constraints nullable="false"/>
            </column>
            <column name="engine_version" type="varchar(10)"/>
            <column name="engine_id" type="bigint">
                <constraints nullable="false" foreignKeyName="fk_epar_prices_engines" references="engines(id)"/>
            </column>
            <column name="created_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="COCA-292-put-save-functionality-for-rfp-engine" author="U142694">

        <createTable tableName="rfp_engine_items">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="labour_pricing_item_id" type="bigint">
                <constraints nullable="false"
                             foreignKeyName="fk_labour_rfp_engine_price_labour_pricing_items"
                             references="labour_pricing_items(id)"/>
            </column>
            <column name="rfp_engine_base_cost" type="decimal(15,8)"/>
            <column name="rfp_engine_ci_hours_cost" type="decimal(15,8)"/>
            <column name="rfp_engine_price" type="decimal(15,2)"/>
            <column name="created_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <createTable tableName="cleaning_and_inspections">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="quotation_id" type="bigint">
                <constraints nullable="false"
                             foreignKeyName="fk_quotation_labour_pricing_input_quotations"
                             references="quotations(id)"/>
            </column>
            <column name="is_rfp_engine_included" type="boolean" defaultValueBoolean="false">
                <constraints nullable="false"/>
            </column>
            <column name="is_rfp_module_included" type="boolean" defaultValueBoolean="false">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="COCA-295-get-rfp-module" author="U143496">

        <createTable tableName="rfp_modules">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="price" type="decimal(15,2)"/>
            <column name="labour_pricing_item_id" type="bigint">
                <constraints nullable="false" foreignKeyName="fk_rfp_modules_labour_pricing_items"
                             references="labour_pricing_items(id)"/>
            </column>
            <column name="workscope_id" type="bigint">
                <constraints nullable="false" foreignKeyName="fk_rfp_modules_workscopes" references="workscopes(id)"/>
            </column>
            <column name="created_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="COCA-517-add-subcontract-metadata" author="U143496">

        <createTable tableName="subcontract_pricing_items">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="margin" type="decimal(3,1)"/>
            <column name="cap" type="int"/>
            <column name="quotation_engine_id" type="bigint">
                <constraints nullable="false"
                             foreignKeyName="fk_subcontract_pricing_items_quotation_engines"
                             references="quotation_engines(id)"/>
            </column>
            <column name="cluster_id" type="bigint">
                <constraints nullable="false"
                             foreignKeyName="fk_subcontract_pricing_items_clusters"
                             references="clusters(id)"/>
            </column>
            <column name="created_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <createTable tableName="subcontract_metadata">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="subcontract_pricing_item_id" type="bigint">
                <constraints nullable="false"
                             foreignKeyName="fk_subcontracts_subcontract_pricing_items"
                             references="subcontract_pricing_items(id)"/>
            </column>
            <column name="quantity" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="weighted_quantity" type="decimal(15,8)"/>
            <column name="value" type="decimal(15,8)"/>
            <column name="currency" type="varchar(3)"/>
            <column name="year" type="varchar(4)">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="varchar(255)"/>
            <column name="workscope_id" type="bigint">
                <constraints nullable="false" foreignKeyName="fk_subcontracts_workscopes" references="workscopes(id)"/>
            </column>
            <column name="created_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="COCA-569-escalations-table" author="U162952">

        <createTable tableName="escalations">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="quotation_engine_id" type="bigint">
                <constraints nullable="false"
                             foreignKeyName="fk_escalations_quotation_engines"
                             references="quotation_engines(id)"/>
            </column>
            <column name="year" type="varchar(4)">
                <constraints nullable="false"/>
            </column>
            <column name="labour_prices" type="decimal(3,1)" defaultValue="0.0">
                <constraints nullable="false"/>
            </column>
            <column name="epar_prices" type="decimal(3,1)" defaultValue="5.0">
                <constraints nullable="false"/>
            </column>
            <column name="rfp_labour" type="decimal(3,1)" defaultValue="0.0">
                <constraints nullable="false"/>
            </column>
            <column name="hc_material_prices" type="decimal(3,1)">
                <constraints nullable="false"/>
            </column>
            <column name="hc_subcontract_prices" type="decimal(3,1)">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <createTable tableName="escalations_material_prices_defaults">
            <column name="engine_id" type="bigint">
                <constraints nullable="false"
                             foreignKeyName="fk_escalations_material_prices_defaults_engines"
                             references="engines(id)"/>
            </column>
            <column name="year" type="varchar(4)">
                <constraints nullable="false"/>
            </column>
            <column name="value" type="decimal(4,1)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="COCA-597-extract-static-data-from-liquibase" author="U143496">

        <createTable tableName="data_migrations">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="file_name" type="varchar(255)">
                <constraints nullable="false"/>
            </column>
            <column name="file_md5sum" type="varchar(255)">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="COCA-567-get-escalations" author="U162952">

        <dropForeignKeyConstraint
                baseTableName="escalations"
                constraintName="fk_escalations_quotation_engines"/>
        <dropColumn tableName="escalations" columnName="quotation_engine_id"/>

        <addColumn tableName="escalations">
            <column name="quotation_id" type="bigint">
                <constraints nullable="false"
                             foreignKeyName="fk_escalations_quotations"
                             references="quotations(id)"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="COCA-639-new-blue-row-logic" author="U143496">

        <createTable tableName="testrun_items">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="price" type="decimal(15,2)"/>
            <column name="quotation_engine_id" type="bigint">
                <constraints nullable="false"
                             foreignKeyName="fk_testrun_items_quotation_engines"
                             references="quotation_engines(id)"/>
            </column>
            <column name="cluster_id" type="bigint">
                <constraints nullable="false" foreignKeyName="fk_testrun_items_clusters" references="clusters(id)"/>
            </column>
            <column name="handling_charge_id" type="bigint">
                <constraints foreignKeyName="fk_testrun_items_handling_charges"
                             references="handling_charges(id)"/>
            </column>
            <column name="created_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <createTable tableName="testrun_metadata">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="testrun_item_id" type="bigint">
                <constraints nullable="false"
                             foreignKeyName="fk_testrun_metadata_testrun_items"
                             references="testrun_items(id)"/>
            </column>
            <column name="quantity" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="weighted_quantity" type="decimal(15,8)"/>
            <column name="value" type="decimal(15,8)"/>
            <column name="currency" type="varchar(3)"/>
            <column name="year" type="varchar(4)">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="varchar(255)"/>
            <column name="workscope_id" type="bigint">
                <constraints nullable="false" foreignKeyName="fk_testrun_metadata_workscopes"
                             references="workscopes(id)"/>
            </column>
            <column name="created_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="bugfix/rfp-check-for-escalations" author="U162952">
        <dropNotNullConstraint tableName="escalations" columnName="rfp_labour" columnDataType="decimal(3,1)"/>
        <dropDefaultValue tableName="escalations" columnName="rfp_labour" columnDataType="decimal(3,1)"/>
    </changeSet>

    <changeSet id="COCA-667-External tables for Discounts" author="U142694">
        <createTable tableName="material_discounts">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="engine_id" type="bigint">
                <constraints nullable="false" foreignKeyName="fk_material_discounts_engines" references="engines(id)"/>
            </column>
            <column name="labour_type" type="varchar(50)">
                <constraints nullable="false"/>
            </column>
            <column name="part_type" type="varchar(50)">
                <constraints nullable="false"/>
            </column>
            <column name="expendable_portion" type="decimal(5,2)">
                <constraints nullable="false"/>
            </column>
            <column name="non_expendable_non_llp_portion" type="decimal(5,2)">
                <constraints nullable="false"/>
            </column>
            <column name="llp_portion" type="decimal(5,2)">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addUniqueConstraint columnNames="engine_id, labour_type, part_type"
                             constraintName="material_discounts_engine_id_labour_type_part_type" tableName="material_discounts"/>
        <createTable tableName="material_discounts_conditions">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="engine_id" type="bigint">
                <constraints nullable="false" foreignKeyName="fk_material_discounts_conditions_engines" references="engines(id)"/>
            </column>
            <column name="year" type="varchar(4)">
                <constraints nullable="false"/>
            </column>
            <column name="non_llp_tiered" type="decimal(5,2)">
                <constraints nullable="false"/>
            </column>
            <column name="non_llp_escalated" type="decimal(5,2)">
                <constraints nullable="false"/>
            </column>
            <column name="llp_tiered" type="decimal(5,2)">
                <constraints nullable="false"/>
            </column>
            <column name="llp_escalated" type="decimal(5,2)">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addUniqueConstraint columnNames="engine_id, year" constraintName="material_discounts_conditions_engine_id_year"
                             tableName="material_discounts_conditions"/>
        <createTable tableName="material_discounts_expendables">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="engine_id" type="bigint">
                <constraints nullable="false" foreignKeyName="fk_material_discounts_expendables_engines" references="engines(id)"/>
            </column>
            <column name="year" type="varchar(4)">
                <constraints nullable="false"/>
            </column>
            <column name="level_zero" type="decimal(5,2)">
                <constraints nullable="false"/>
            </column>
            <column name="level_one" type="decimal(5,2)">
                <constraints nullable="false"/>
            </column>
            <column name="level_two" type="decimal(5,2)">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addUniqueConstraint columnNames="engine_id, year" constraintName="material_discounts_expendables_engine_id_year"
                             tableName="material_discounts_expendables"/>
        <createTable tableName="subcontract_discounts">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="engine_id" type="bigint">
                <constraints nullable="false" foreignKeyName="fk_subcontract_discounts_engines" references="engines(id)"/>
            </column>
            <column name="part" type="varchar(50)">
                <constraints nullable="false"/>
            </column>
            <column name="subcontractor_ge" type="decimal(5,2)">
                <constraints nullable="false"/>
            </column>
            <column name="subcontractor_hpt_blade_ge" type="decimal(5,2)">
                <constraints nullable="false"/>
            </column>
            <column name="subcontractor_pw" type="decimal(5,2)">
                <constraints nullable="false"/>
            </column>
            <column name="subcontractor_chromalloy" type="decimal(5,2)">
                <constraints nullable="false"/>
            </column>
            <column name="subcontractor_ltts" type="decimal(5,2)">
                <constraints nullable="false"/>
            </column>
            <column name="subcontractor_leap_1a" type="decimal(5,2)">
                <constraints nullable="false"/>
            </column>
            <column name="subcontractor_mtu" type="decimal(5,2)">
                <constraints nullable="false"/>
            </column>
            <column name="subcontractor_schäffler" type="decimal(5,2)">
                <constraints nullable="false"/>
            </column>
            <column name="subcontractor_standard_aero" type="decimal(5,2)">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addUniqueConstraint columnNames="engine_id, part" constraintName="subcontract_discounts_engine_id_part"
                             tableName="subcontract_discounts"/>
        <createTable tableName="subcontract_discounts_conditions">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="subcontractor" type="varchar(50)">
                <constraints nullable="false"/>
            </column>
            <column name="year" type="varchar(4)">
                <constraints nullable="false"/>
            </column>
            <column name="discount" type="decimal(5,2)">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addUniqueConstraint columnNames="subcontractor, year" constraintName="subcontract_discounts_conditions_subcontractor_year"
                             tableName="subcontract_discounts_conditions"/>
    </changeSet>

    <changeSet id="COCA-605-calculate-production-cost" author="U143496">
        <createTable tableName="workscope_summaries">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="year" type="varchar(4)">
                <constraints nullable="false"/>
            </column>
            <column name="revenue_usd" type="decimal(20,8)"/>
            <column name="revenue_eur" type="decimal(20,8)"/>
            <column name="production_cost_usd" type="decimal(20,8)"/>
            <column name="production_cost_eur" type="decimal(20,8)"/>
            <column name="discount_usd" type="decimal(20,8)"/>
            <column name="discount_eur" type="decimal(20,8)"/>
            <column name="surcharges_cost_usd" type="decimal(20,8)"/>
            <column name="surcharges_cost_eur" type="decimal(20,8)"/>
            <column name="quotation_id" type="bigint">
                <constraints nullable="false"
                             foreignKeyName="fk_summaries_quotations"
                             references="quotations(id)"/>
            </column>
            <column name="workscope_id" type="bigint">
                <constraints nullable="false"
                             foreignKeyName="fk_summaries_workscopes"
                             references="workscopes(id)"/>
            </column>
            <column name="created_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet id="COCA-666-summary discounts adaptation" author="U142694">
        <dropUniqueConstraint uniqueColumns="quotation_engine_id, cluster_id, part_id"
                              constraintName="uc_material_pricing_items_quotation_engine_id_cluster_id_part_id"
                              tableName="material_pricing_items"/>
        <addColumn tableName="material_pricing_items">
            <column name="non_routine_material_sub_type" type="varchar(20)">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <addUniqueConstraint columnNames="quotation_engine_id, cluster_id, part_id, non_routine_material_sub_type"
                             constraintName="uc_material_pricing_items_quotation_engine_id_cluster_id_part_id_non_routine_material_sub_type"
                             tableName="material_pricing_items"/>
        <renameColumn
                tableName="task_metadata"
                oldColumnName="label"
                newColumnName="name"
                columnDataType="varchar(255)"
        />
    </changeSet>

    <changeSet id="COCA-672-create-surcharges-table" author="U143496">
        <createTable tableName="surcharges">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="year" type="varchar(4)">
                <constraints nullable="false"/>
            </column>
            <column name="labour_cost" type="decimal(6,2)">
                <constraints nullable="false"/>
            </column>
            <column name="ah_rate_v25_cfm56_cf6_leap" type="decimal(6,2)">
                <constraints nullable="false"/>
            </column>
            <column name="ah_rate_v25_cfm56_cf6_leap_yoy" type="decimal(6,2)">
                <constraints nullable="false"/>
            </column>
            <column name="tec_surcharge_per_ws_a_inh_standard" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="tec_surcharge_per_ws_a_inh_leap_cfm_oem_offload" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="tec_surcharge_per_ws_a_sc_dlh_swr_pw1_ovh" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="tec_surcharge_enl_per_month_per_lease_event" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="tec_surcharge_per_ws_b_fixed_part_inh_sc_oem_offloads" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="tec_surcharge_per_ws_b_c_variable_part_inh_sc_oem_offloads" type="decimal(6,2)">
                <constraints nullable="false"/>
            </column>
            <column name="teo21_22_surcharge_per_ws_a_b" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="teo21_22_surcharge_per_mes_hub_module_event_ham" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="v25_classic_engine_epar_royalties_per_sle_ws_a" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="v25_select_retrofit_engine_epar_royalties_per_sle_ws_a" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="internal_logistic_ws_a" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="internal_logistic_ws_b" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="internal_logistic_ws_c" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="loss_of_value" type="decimal(6,2)">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <createTable tableName="overheads">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="year" type="varchar(4)">
                <constraints nullable="false"/>
            </column>
            <column name="db3_overhead_dlh" type="decimal(6,2)">
                <constraints nullable="false"/>
            </column>
            <column name="db3_overhead_int" type="decimal(6,2)">
                <constraints nullable="false"/>
            </column>
            <column name="db3_overhead_ext" type="decimal(6,2)">
                <constraints nullable="false"/>
            </column>
            <column name="db3_overhead_llp" type="decimal(6,2)">
                <constraints nullable="false"/>
            </column>
            <column name="ebit_overhead_dlh" type="decimal(6,2)">
                <constraints nullable="false"/>
            </column>
            <column name="ebit_overhead_int" type="decimal(6,2)">
                <constraints nullable="false"/>
            </column>
            <column name="ebit_overhead_ext" type="decimal(6,2)">
                <constraints nullable="false"/>
            </column>
            <column name="ebit_overhead_llp" type="decimal(6,2)">
                <constraints nullable="false"/>
            </column>
            <column name="ebit_overhead_ebit_sc" type="decimal(6,2)">
                <constraints nullable="false"/>
            </column>
            <column name="ebit_overhead_d3_sc" type="decimal(6,2)">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <createTable tableName="hurdles">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="year" type="varchar(4)">
                <constraints nullable="false"/>
            </column>
            <column name="hurdle_ens" type="decimal(6,2)">
                <constraints nullable="false"/>
            </column>
            <column name="hurdle_mes" type="decimal(6,2)">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <createTable tableName="column_escalation_values">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="labour_escalation" type="decimal(6,2)">
                <constraints nullable="false"/>
            </column>
            <column name="v25_royalties_escalation" type="decimal(6,2)">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet id="COCA-738-discounts table - volume Based" author="U142694">
        <createTable tableName="volume_based_material_discounts">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="engine_id" type="bigint">
                <constraints nullable="false" foreignKeyName="fk_volume_based_material_discounts_engines" references="engines(id)"/>
            </column>
            <column name="year" type="varchar(4)">
                <constraints nullable="false"/>
            </column>
            <column name="target_material_cost" type="decimal(20,8)"/>
            <column name="minimum_material_cost" type="decimal(20,8)"/>
            <column name="maximum_volume_based_discount" type="decimal(20,8)"/>
            <column name="created_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addUniqueConstraint columnNames="engine_id, year" constraintName="volume_based_material_discounts_engine_id_year"
                             tableName="volume_based_material_discounts"/>
    </changeSet>
</databaseChangeLog>
