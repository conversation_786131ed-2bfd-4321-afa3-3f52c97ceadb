package de.dlh.lht.ti.configuration;

import java.time.Instant;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import org.springframework.security.oauth2.jwt.Jwt;

public class JwtTestData {

  public static final Set<String> ROLES = Set.of("reader", "writer", "authenticated");
  public static final String SAMPLE_APP = "sample_app";
  private static final Map<String, Object> claims =
      Map.of(
          "preferred_username",
          "U123456",
          "sub",
          UUID.randomUUID().toString(),
          "resource_access",
          Map.of(SAMPLE_APP, Map.of("roles", ROLES)));

  public static final Jwt JWT =
      new Jwt("Foo", Instant.MIN, Instant.MAX, Map.of("foo", "bar"), claims);
}
