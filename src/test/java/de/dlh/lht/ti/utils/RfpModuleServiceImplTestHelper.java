package de.dlh.lht.ti.utils;

import de.dlh.lht.ti.dto.RfpModuleDataDto;
import de.dlh.lht.ti.dto.RfpModuleDto;
import de.dlh.lht.ti.dto.RfpWorkscopeDto;
import de.dlh.lht.ti.dto.TaskMetadataDto;
import de.dlh.lht.ti.dto.api.ClusterDto;
import de.dlh.lht.ti.entity.RfpModuleEntity;
import de.dlh.lht.ti.enums.Currency;
import java.util.List;


import static de.dlh.lht.ti.utils.TestConstants.LONG_ID_1;
import static de.dlh.lht.ti.utils.TestConstants.LONG_ID_2;
import static de.dlh.lht.ti.utils.TestConstants.TEST_BIG_DECIMAL;
import static de.dlh.lht.ti.utils.TestConstants.TEST_INT_1;
import static de.dlh.lht.ti.utils.TestConstants.TEST_STRING;

public class RfpModuleServiceImplTestHelper {

    private RfpModuleServiceImplTestHelper() {
        throw new AssertionError("Cannot create instances of this class");
    }

    public static List<RfpModuleDataDto> createRfpModuleDataDtoList() {
        var clusterDto = new ClusterDto(LONG_ID_1, TEST_STRING, TEST_INT_1);
        var rfpModuleDto1 = new RfpModuleDto(LONG_ID_1, TEST_BIG_DECIMAL, clusterDto);
        var rfpModuleDto2 = new RfpModuleDto(LONG_ID_1, TEST_BIG_DECIMAL, clusterDto);
        var task = new TaskMetadataDto(TEST_BIG_DECIMAL, TEST_INT_1, TEST_BIG_DECIMAL, TEST_BIG_DECIMAL, Currency.USD, false);
        var workscope = new RfpWorkscopeDto(LONG_ID_1, TEST_STRING);
        return List.of(
                new RfpModuleDataDto(rfpModuleDto1, task, workscope),
                new RfpModuleDataDto(rfpModuleDto2, task, workscope));
    }

    public static List<RfpModuleEntity> createRfpModuleEntityList() {
        var rfpModuleEntity1 = new RfpModuleEntity(null, LONG_ID_1, LONG_ID_1);
        rfpModuleEntity1.setId(LONG_ID_1);
        var rfpModuleEntity2 = new RfpModuleEntity(null, LONG_ID_2, LONG_ID_2);
        rfpModuleEntity2.setId(LONG_ID_2);
        return List.of(rfpModuleEntity1, rfpModuleEntity2);
    }
}
