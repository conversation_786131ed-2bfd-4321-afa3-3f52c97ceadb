package de.dlh.lht.ti.utils;

import java.math.BigDecimal;
import java.time.ZonedDateTime;

public class TestConstants {

    public static final Long LONG_ID_1 = 1L;
    public static final Long LONG_ID_2 = 2L;
    public static final int TEST_INT_1 = 1;
    public static final float TEST_FLOAT_1 = 1.0f;
    public static final float TEST_NEGATIVE_FLOAT_1 = -1.0f;
    public static final float TEST_HUNDRED_FLOAT_1 = 100.0f;

    public static final Double TEST_DOUBLE = 1.2;
    public static final String TEST_STRING = "TEST";
    public static final String TEST_YEAR = "2022";
    public static final String TEST_YEAR_2021 = "2021";

    public static final String TEST_YEAR_2023 = "2023";

    public static final String TEST_OFFER_NUMBER = "1000000";
    public static final String TEST_STRING_2 = "TEST_2";
    public static final String TEST_3LC = "LHT";
    public static final String LOCALHOST = "http://localhost:%s";
    public static final String TEST_UNUMBER = "M888157";
    public static final ZonedDateTime ZONED_DATE_TIME = ZonedDateTime.now();
    public static final ZonedDateTime ZONED_DATE_TIME_2_YEARS_AHEAD = ZonedDateTime.now().plusYears(2);
    public static final BigDecimal TEST_BIG_DECIMAL = BigDecimal.valueOf(1.2);
    public static final int PROGRESS_STEPS_COUNT = 9;
    public static final int PROGRESS_STEPS_COUNT_WITHOUT_RFP = 7;

    private TestConstants() {
        throw new AssertionError("Cannot create instances of this class");
    }
}
