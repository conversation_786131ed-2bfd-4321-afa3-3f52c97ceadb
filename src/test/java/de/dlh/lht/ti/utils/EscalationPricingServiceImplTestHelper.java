package de.dlh.lht.ti.utils;

import de.dlh.lht.ti.dto.EscalationPricingDto;
import de.dlh.lht.ti.entity.EscalationPricingEntity;
import de.dlh.lht.ti.model.*;

import java.math.BigDecimal;
import java.util.List;

import static de.dlh.lht.ti.utils.TestConstants.*;

public class EscalationPricingServiceImplTestHelper {

    private EscalationPricingServiceImplTestHelper() {
        throw new AssertionError("Cannot create instances of this class");
    }

    public static EscalationPricingDto createEscalationPricingDto() {
        return new EscalationPricingDto(LONG_ID_1, "2022", new BigDecimal("1"), new BigDecimal("2"), new BigDecimal("3"), new BigDecimal("4"), new BigDecimal("5"), new BigDecimal("6"), true);
    }

    public static List<EscalationPricingDto> createEscalationPricingDtoList() {
        return List.of(createEscalationPricingDto());
    }

    public static Escalation createEscalation() {
        var escalation = new Escalation();
        escalation.setId(LONG_ID_1);
        escalation.setYear(TEST_YEAR);

        var escalationFieldValue = new EscalationFieldValue();
        escalationFieldValue.setValue(TEST_FLOAT_1);
        escalationFieldValue.setDefaultValue(TEST_FLOAT_1);

        var escalationValue = new EscalationValues();
        escalationValue.setEparPrices(escalationFieldValue);
        escalationValue.setLabourPrices(escalationFieldValue);
        escalationValue.setRfpLabour(escalationFieldValue);
        escalationValue.setHcMaterialPrices(escalationFieldValue);
        escalationValue.setHcSubcontractPrices(escalationFieldValue);
        
        escalation.setValues(escalationValue);

        return escalation;
    }

    public static List<Escalation> createEscalationList() {
        return List.of(createEscalation());
    }

    public static EscalationPricingEntity createEscalationPricingEntity() {
        var entity = new EscalationPricingEntity(LONG_ID_1, TEST_YEAR, new BigDecimal("1"), new BigDecimal("2"), new BigDecimal("3"), new BigDecimal("4"), new BigDecimal("5"));
        entity.setId(LONG_ID_1);
        return entity;
    }

    public static List<EscalationPricingEntity> createEscalationPricingEntityList() {
        return List.of(createEscalationPricingEntity());
    }

    public static EscalationPricingRequest createEscalationPricingRequest() {
        var input = new EscalationPricingInput();
        input.setId(LONG_ID_1);
        input.setEparPrices(TEST_FLOAT_1);
        input.setLabourPrices(TEST_FLOAT_1);
        input.setRfpLabour(TEST_FLOAT_1);
        input.setHcMaterialPrices(TEST_FLOAT_1);
        input.setHcSubcontractPrices(TEST_FLOAT_1);

        var request = new EscalationPricingRequest();
        request.setEscalationInputs(List.of(input));

        return request;
    }

    public static EscalationPricingRequest createInvalidNegativeEscalationPricingRequest() {
        var input = new EscalationPricingInput();
        input.setId(LONG_ID_1);
        input.setEparPrices(TEST_NEGATIVE_FLOAT_1);
        input.setLabourPrices(TEST_NEGATIVE_FLOAT_1);
        input.setRfpLabour(TEST_NEGATIVE_FLOAT_1);
        input.setHcMaterialPrices(TEST_NEGATIVE_FLOAT_1);
        input.setHcSubcontractPrices(TEST_NEGATIVE_FLOAT_1);

        var request = new EscalationPricingRequest();
        request.setEscalationInputs(List.of(input));

        return request;
    }

    public static EscalationPricingRequest createInvalidEscalationPricingRequest() {
        var input = new EscalationPricingInput();
        input.setId(LONG_ID_1);
        input.setEparPrices(TEST_HUNDRED_FLOAT_1);
        input.setLabourPrices(TEST_HUNDRED_FLOAT_1);
        input.setRfpLabour(TEST_HUNDRED_FLOAT_1);
        input.setHcMaterialPrices(TEST_HUNDRED_FLOAT_1);
        input.setHcSubcontractPrices(TEST_HUNDRED_FLOAT_1);

        var request = new EscalationPricingRequest();
        request.setEscalationInputs(List.of(input));

        return request;
    }



}
