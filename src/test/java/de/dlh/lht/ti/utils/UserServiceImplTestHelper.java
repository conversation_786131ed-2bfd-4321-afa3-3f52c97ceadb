package de.dlh.lht.ti.utils;

import com.cleverpine.viravaspringhelper.dto.Permission;
import com.cleverpine.viravaspringhelper.dto.Scope;
import de.dlh.lht.ti.auth.roles.Resources;
import de.dlh.lht.ti.entity.UserEntity;
import de.dlh.lht.ti.model.Resource;
import de.dlh.lht.ti.model.ScopeHolder;
import de.dlh.lht.ti.model.UserDetails;
import java.util.List;


import static de.dlh.lht.ti.utils.TestConstants.LONG_ID_1;
import static de.dlh.lht.ti.utils.TestConstants.TEST_STRING;

public class UserServiceImplTestHelper {

    private UserServiceImplTestHelper() {
        throw new AssertionError("Cannot create instances of this class");
    }

    public static UserEntity createUserEntity() {
        var userEntity = new UserEntity();
        userEntity.setId(LONG_ID_1);
        userEntity.setName(TEST_STRING);
        userEntity.setUNumber(TEST_STRING);
        return userEntity;
    }

    public static List<Permission> createViravaPermissions() {
        return List.of(Permission.of(Resources.USER, Scope.READ));
    }

    public static de.dlh.lht.ti.model.Permission createDomainPermission() {
        var permission = new de.dlh.lht.ti.model.Permission();
        permission.setResource(new Resource().resource(TEST_STRING));
        permission.setScopeHolder(new ScopeHolder().create(true));

        return permission;
    }

    public static UserDetails createUserDetails() {
        return new UserDetails()
                .id(LONG_ID_1)
                .name(TEST_STRING)
                .username(TEST_STRING)
                .permissions(List.of(createDomainPermission()));
    }
}
