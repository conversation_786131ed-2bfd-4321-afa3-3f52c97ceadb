package de.dlh.lht.ti.utils;

import de.dlh.lht.ti.dto.QuotationDetailsDto;
import de.dlh.lht.ti.entity.QuotationEngineEntity;
import de.dlh.lht.ti.model.QuotationLight;
import java.util.Arrays;
import java.util.List;


import static de.dlh.lht.ti.utils.EngineServiceImplTestHelper.createEngineEntity;
import static de.dlh.lht.ti.utils.QuotationServiceImplTestHelper.createQuotationEntity;
import static de.dlh.lht.ti.utils.WorkscopeServiceImplTestHelper.createWorkscopeEntityList;

public class QuotationEngineServiceImplTestHelper {

    private QuotationEngineServiceImplTestHelper() {
        throw new AssertionError("Cannot create instances of this class");
    }

    public static QuotationEngineEntity createQuotationEngineEntity(Long id) {
        var quotationEngineEntity = new QuotationEngineEntity();

        quotationEngineEntity.setId(id);
        quotationEngineEntity.setQuotation(createQuotationEntity());
        quotationEngineEntity.setEngine(createEngineEntity());
        quotationEngineEntity.setWorkscopes(createWorkscopeEntityList());

        return quotationEngineEntity;
    }

    public static QuotationDetailsDto createQuotationDetailsDto() {
        return new QuotationDetailsDto(createQuotationEntity(), createEngineEntity());
    }

    public static List<QuotationEngineEntity> createQuotationEngineEntities(Long... ids) {
        return Arrays.stream(ids)
                .map(QuotationEngineServiceImplTestHelper::createQuotationEngineEntity)
                .toList();
    }

    public static QuotationLight createQuotationLight(Long id) {
        return new QuotationLight()
                .id(id);
    }

    public static List<QuotationLight> createQuotationLightList(Long... ids) {
        return Arrays.stream(ids)
                .map(QuotationEngineServiceImplTestHelper::createQuotationLight)
                .toList();
    }
}
