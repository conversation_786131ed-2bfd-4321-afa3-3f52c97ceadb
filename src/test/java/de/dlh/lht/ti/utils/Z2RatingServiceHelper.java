package de.dlh.lht.ti.utils;

import de.dlh.lht.ti.dto.Z2RatingDto;
import de.dlh.lht.ti.entity.Z2RatingEntity;
import de.dlh.lht.ti.enums.Currency;
import de.dlh.lht.ti.enums.PartType;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;


import static de.dlh.lht.ti.utils.TestConstants.LONG_ID_1;
import static de.dlh.lht.ti.utils.TestConstants.LONG_ID_2;
import static de.dlh.lht.ti.utils.TestConstants.TEST_BIG_DECIMAL;
import static de.dlh.lht.ti.utils.TestConstants.TEST_FLOAT_1;
import static de.dlh.lht.ti.utils.TestConstants.TEST_INT_1;
import static de.dlh.lht.ti.utils.TestConstants.TEST_STRING;
import static de.dlh.lht.ti.utils.TestConstants.TEST_YEAR;

public class Z2RatingServiceHelper {

    private Z2RatingServiceHelper() {
        throw new AssertionError("Cannot create instances of this class");
    }

    public static List<Z2RatingDto> createZ2RatingDtoList() {
        var z2RatingDto1 =
                new Z2RatingDto(LONG_ID_1, LONG_ID_1, TEST_STRING, TEST_INT_1, LONG_ID_1, TEST_STRING, TEST_INT_1, PartType.A_PART,
                        LONG_ID_1, TEST_YEAR, TEST_BIG_DECIMAL, Currency.USD, TEST_BIG_DECIMAL, TEST_INT_1, TEST_FLOAT_1);
        var z2RatingDto2 =
                new Z2RatingDto(LONG_ID_2, LONG_ID_2, TEST_STRING, TEST_INT_1, LONG_ID_1, TEST_STRING, TEST_INT_1, PartType.A_PART,
                        LONG_ID_1, TEST_YEAR, TEST_BIG_DECIMAL, Currency.USD, TEST_BIG_DECIMAL, TEST_INT_1, TEST_FLOAT_1);

        var z2RatingDtoList = new ArrayList<Z2RatingDto>();
        z2RatingDtoList.add(z2RatingDto1);
        z2RatingDtoList.add(z2RatingDto2);

        return z2RatingDtoList;
    }

    public static List<Z2RatingEntity> createZ2RatingEntities(Long... ids) {
        return Arrays.stream(ids).map(Z2RatingServiceHelper::createZ2RatingEntity).toList();
    }

    public static Z2RatingEntity createZ2RatingEntity(Long id) {
        var entity = new Z2RatingEntity();
        entity.setId(id);

        return entity;
    }
}