package de.dlh.lht.ti.utils;

import java.util.Map;

public class MaterialPricingTestHelper {

    private MaterialPricingTestHelper() {
        throw new AssertionError("Cannot create instances of this class");
    }

    public static Map<Long, Map<Long, Long>> createClusterIdByEngineIdByPartIdMap() {
        return Map.of(
                1L, Map.of(1L, 1L),
                2L, Map.of(2L, 2L));
    }
}
