package de.dlh.lht.ti.utils;

import de.dlh.lht.ti.entity.NavigationItemEntity;
import de.dlh.lht.ti.entity.NavigationStepEntity;
import de.dlh.lht.ti.entity.ProgressStepEntity;
import de.dlh.lht.ti.enums.NavigationSteps;
import de.dlh.lht.ti.enums.QuotationProgress;
import de.dlh.lht.ti.model.NavigationStep;
import de.dlh.lht.ti.model.Progress;
import de.dlh.lht.ti.model.ProgressStep;
import java.util.Arrays;
import java.util.List;


import static de.dlh.lht.ti.utils.TestConstants.LONG_ID_1;

public class ProgressMapperTestHelper {

    private ProgressMapperTestHelper() {
        throw new AssertionError("Cannot create instances of this class");
    }

    public static List<NavigationItemEntity> createNavigationItemEntityList(Boolean isValidValue1, Boolean isValidValue2) {
        var navigationStep = createNavigationStepEntity();

        var navigationItemEntity1 = createNavigationItemEntity(isValidValue1, navigationStep);
        var navigationItemEntity2 = createNavigationItemEntity(isValidValue2, navigationStep);
        
        return Arrays.asList(navigationItemEntity1, navigationItemEntity2);
    }

    public static NavigationStepEntity createNavigationStepEntity() {
        return new NavigationStepEntity(NavigationSteps.COVER);
    }

    public static Progress createTrueProgress() {
        var navigationStep = new NavigationStep()
                .name(de.dlh.lht.ti.model.NavigationSteps.COVER)
                .id(LONG_ID_1)
                .progressSteps(List.of(createProgressStep(true), createProgressStep(true)))
                .areAllProgressStepsValid(true);

        return new Progress()
                .navigationSteps(List.of(navigationStep));
    }

    public static Progress createFalseProgress() {
        var navigationStep = new NavigationStep()
                .name(de.dlh.lht.ti.model.NavigationSteps.COVER)
                .progressSteps(List.of(createProgressStep(false), createProgressStep(true)))
                .areAllProgressStepsValid(false);

        return new Progress()
                .navigationSteps(List.of(navigationStep));
    }

    public static NavigationItemEntity createNavigationItemEntity(Boolean isValid, NavigationStepEntity navigationStep) {

        var progressStep = new ProgressStepEntity(QuotationProgress.COVER);
        progressStep.setId(LONG_ID_1);

        var navigationItem = new NavigationItemEntity();
        navigationItem.setId(LONG_ID_1);
        navigationItem.setIsValid(isValid);
        navigationItem.setQuotationId(LONG_ID_1);
        navigationItem.setNavigationStep(navigationStep);
        navigationItem.setProgressStep(progressStep);
        return navigationItem;
    }

    public static Progress createNullNavigationItemsProgress() {
        return new Progress();
    }

    private static ProgressStep createProgressStep(Boolean isValid) {
        return new ProgressStep()
                .name(de.dlh.lht.ti.model.QuotationProgress.COVER)
                .id(LONG_ID_1)
                .isValid(isValid);
    }
}
