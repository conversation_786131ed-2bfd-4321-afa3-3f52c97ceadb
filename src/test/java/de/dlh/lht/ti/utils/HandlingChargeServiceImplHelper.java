package de.dlh.lht.ti.utils;

import de.dlh.lht.ti.dto.HandlingChargeDto;
import de.dlh.lht.ti.entity.HandlingChargeEntity;
import de.dlh.lht.ti.enums.PartType;
import java.util.ArrayList;
import java.util.List;


import static de.dlh.lht.ti.utils.TestConstants.LONG_ID_1;
import static de.dlh.lht.ti.utils.TestConstants.TEST_INT_1;
import static de.dlh.lht.ti.utils.TestConstants.TEST_STRING;

public class HandlingChargeServiceImplHelper {

    private HandlingChargeServiceImplHelper() {
        throw new AssertionError("Cannot create instances of this class");
    }

    public static HandlingChargeEntity createHandlingChargeEntity() {
        var handlingChargeEntity = HandlingChargeEntity.builder()
                .materialPricingItemId(LONG_ID_1)
                .build();
        handlingChargeEntity.setId(LONG_ID_1);
        return handlingChargeEntity;
    }

    public static List<HandlingChargeDto> createHandlingChargeDtoList() {
        var handlingChargeDto1 =
                new HandlingChargeDto(createHandlingChargeEntity(), LONG_ID_1, TEST_STRING, TEST_INT_1, LONG_ID_1, TEST_STRING,
                        TEST_INT_1, PartType.A_PART, TEST_INT_1);
        var handlingChargeDto2 =
                new HandlingChargeDto(createHandlingChargeEntity(), LONG_ID_1, TEST_STRING, TEST_INT_1, LONG_ID_1, TEST_STRING,
                        TEST_INT_1, PartType.A_PART, TEST_INT_1);

        var handlingChargeDtoList = new ArrayList<HandlingChargeDto>();
        handlingChargeDtoList.add(handlingChargeDto1);
        handlingChargeDtoList.add(handlingChargeDto2);

        return handlingChargeDtoList;
    }
}
