package de.dlh.lht.ti.utils;

import de.dlh.lht.ti.dto.importer.WorkscopeDto;
import de.dlh.lht.ti.entity.WorkscopeEntity;
import de.dlh.lht.ti.enums.WorkscopeClass;
import de.dlh.lht.ti.importer.model.WorkscopeRaw;
import java.util.List;
import java.util.Map;


import static de.dlh.lht.ti.utils.TestConstants.LONG_ID_1;
import static de.dlh.lht.ti.utils.TestConstants.LONG_ID_2;
import static de.dlh.lht.ti.utils.TestConstants.TEST_STRING;

public class WorkscopeServiceImplTestHelper {

    private WorkscopeServiceImplTestHelper() {
        throw new AssertionError("Cannot create instances of this class");
    }

    public static WorkscopeEntity createWorkscopeEntity(Long id) {
        var workscopeEntity = new WorkscopeEntity();
        workscopeEntity.setId(id);
        workscopeEntity.setName(TEST_STRING);
        workscopeEntity.setWorkscopeClass(WorkscopeClass.A);
        return workscopeEntity;
    }

    public static WorkscopeDto createWorkscopeDto() {
        return new WorkscopeDto(TEST_STRING, WorkscopeClass.A);
    }

    public static WorkscopeRaw createWorkscopeRaw() {
        return new WorkscopeRaw()
                .name(TEST_STRING)
                .workscopeClass(de.dlh.lht.ti.importer.model.WorkscopeClass.A);
    }

    public static List<WorkscopeEntity> createWorkscopeEntityList() {
        return List.of(
                createWorkscopeEntity(LONG_ID_1),
                createWorkscopeEntity(LONG_ID_2)
        );
    }

    public static List<WorkscopeDto> createWorkscopeDtoList() {
        return List.of(createWorkscopeDto(), createWorkscopeDto()
        );
    }

    public static Map<String, Long> createWorkscopeNameByIdMap() {
        return Map.of(TEST_STRING, LONG_ID_1);
    }
}
