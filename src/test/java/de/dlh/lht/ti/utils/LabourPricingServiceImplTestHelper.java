package de.dlh.lht.ti.utils;

import de.dlh.lht.ti.model.RfpItem;
import de.dlh.lht.ti.model.RfpItemInput;
import de.dlh.lht.ti.model.RfpModuleData;
import de.dlh.lht.ti.model.RfpModuleWorkscope;
import de.dlh.lht.ti.model.RfpRequest;
import java.util.List;


import static de.dlh.lht.ti.utils.TestConstants.LONG_ID_1;
import static de.dlh.lht.ti.utils.TestConstants.LONG_ID_2;
import static de.dlh.lht.ti.utils.TestConstants.TEST_DOUBLE;
import static de.dlh.lht.ti.utils.TestConstants.TEST_STRING;
import static de.dlh.lht.ti.utils.TestConstants.TEST_STRING_2;

public class LabourPricingServiceImplTestHelper {

    private LabourPricingServiceImplTestHelper() {
        throw new AssertionError("Cannot create instances of this class");
    }

    public static RfpModuleData createRfpModuleData() {
        var rfpItems = List.of(
                new RfpItem().id(LONG_ID_1).price(TEST_DOUBLE).name(TEST_STRING).cost(TEST_DOUBLE).ciHoursCost(TEST_DOUBLE),
                new RfpItem().id(LONG_ID_2).price(TEST_DOUBLE).name(TEST_STRING_2).cost(TEST_DOUBLE).ciHoursCost(TEST_DOUBLE));
        var workscopes = List.of(new RfpModuleWorkscope().id(LONG_ID_1).name(TEST_STRING).rfpItems(rfpItems));
        return new RfpModuleData()
                .workscopes(workscopes)
                .isCiIncluded(true);
    }

    public static RfpRequest createRfpRequest() {
        var rfpItemInputs = List.of(
                new RfpItemInput().id(LONG_ID_1).price(TEST_DOUBLE),
                new RfpItemInput().id(LONG_ID_2).price(TEST_DOUBLE));
        return new RfpRequest()
                .ciIncluded(true)
                .rfpItemInputs(rfpItemInputs);
    }

    public static RfpRequest createInvalidRfpRequest() {
        var rfpItemInputs = List.of(
                new RfpItemInput().id(LONG_ID_1).price(-1d),
                new RfpItemInput().id(LONG_ID_2).price(TEST_DOUBLE));
        return new RfpRequest()
                .ciIncluded(true)
                .rfpItemInputs(rfpItemInputs);
    }
}
