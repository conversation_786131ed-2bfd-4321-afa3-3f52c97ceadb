package de.dlh.lht.ti.utils;

import de.dlh.lht.ti.dto.importer.CustomerDto;
import de.dlh.lht.ti.entity.CustomerEntity;
import de.dlh.lht.ti.enums.CustomerType;
import de.dlh.lht.ti.importer.model.CustomerRaw;
import java.util.List;


import static de.dlh.lht.ti.utils.TestConstants.TEST_3LC;

public class CustomerServiceImplTestHelper {

    private CustomerServiceImplTestHelper() {
        throw new AssertionError("Cannot create instances of this class");
    }

    public static CustomerEntity createCustomerEntity() {
        return new CustomerEntity(TEST_3LC, CustomerType.DLH);
    }

    public static CustomerDto createCustomerDto() {
        return new CustomerDto(TEST_3LC, CustomerType.DLH);
    }

    public static CustomerRaw createCustomerRaw() {
        return new CustomerRaw().threeLetterCode(TEST_3LC).type("DLH");
    }

    public static List<CustomerEntity> createCustomerEntityList() {
        return List.of(createCustomerEntity());
    }
}
