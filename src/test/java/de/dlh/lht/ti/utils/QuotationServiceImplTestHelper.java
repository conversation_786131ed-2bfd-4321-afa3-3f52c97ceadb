package de.dlh.lht.ti.utils;

import de.dlh.lht.ti.dto.ContractTypesDto;
import de.dlh.lht.ti.dto.RfpQuotationDto;
import de.dlh.lht.ti.entity.QuotationEntity;
import de.dlh.lht.ti.enums.QuotationStatus;
import java.math.BigDecimal;


import static de.dlh.lht.ti.utils.CustomerServiceImplTestHelper.createCustomerEntity;
import static de.dlh.lht.ti.utils.ProjectServiceImplTestHelper.createProjectEntity;
import static de.dlh.lht.ti.utils.TestConstants.*;

public class QuotationServiceImplTestHelper {

    private QuotationServiceImplTestHelper() {
        throw new AssertionError("Cannot create instances of this class");
    }

    public static QuotationEntity createQuotationEntity() {
        var quotationEntity = new QuotationEntity();
        quotationEntity.setId(LONG_ID_1);
        quotationEntity.setContractStart(ZONED_DATE_TIME);
        quotationEntity.setContractEnd(ZONED_DATE_TIME_2_YEARS_AHEAD);
        quotationEntity.setPosition(1);
        quotationEntity.setVersion(1);
        quotationEntity.setScenario(0);
        quotationEntity.setUsdExchangeRate(new BigDecimal("1.07"));
        quotationEntity.setStatus(QuotationStatus.ANKA_VALIDATED.ordinal());
        quotationEntity.setStatusLastUpdated(ZONED_DATE_TIME);
        quotationEntity.setTimeAndMaterial(true);
        quotationEntity.setRoutineFixedPrices(true);
        quotationEntity.setCustomer(createCustomerEntity());
        quotationEntity.setProject(createProjectEntity());

        return quotationEntity;
    }

    public static QuotationEntity createBegunQuotationEntity() {
        var quotationEntity = createQuotationEntity();
        quotationEntity.setStatus(QuotationStatus.IN_PROGRESS.ordinal());

        return quotationEntity;
    }

    public static ContractTypesDto createContractTypesDto() {
        return new ContractTypesDto(false);
    }

    public static RfpQuotationDto createRfpQuotationDto() {
        return new RfpQuotationDto(LONG_ID_1, "CLASSIC", true, false, ZONED_DATE_TIME, TEST_BIG_DECIMAL);
    }
}
