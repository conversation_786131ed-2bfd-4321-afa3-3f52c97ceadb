package de.dlh.lht.ti.utils;

import de.dlh.lht.ti.dto.importer.ProjectDto;
import de.dlh.lht.ti.entity.ProjectEntity;
import de.dlh.lht.ti.importer.model.ProjectRaw;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


import static de.dlh.lht.ti.utils.TestConstants.LONG_ID_1;
import static de.dlh.lht.ti.utils.TestConstants.TEST_STRING;
import static de.dlh.lht.ti.utils.UserServiceImplTestHelper.createUserEntity;

public class ProjectServiceImplTestHelper {

    private ProjectServiceImplTestHelper() {
        throw new AssertionError("Cannot create instances of this class");
    }

    public static ProjectEntity createProjectEntity() {
        var user = createUserEntity();
        var projectEntity = new ProjectEntity();
        projectEntity.setId(LONG_ID_1);
        projectEntity.setOfferNumber(TEST_STRING);
        projectEntity.setCurrentOwner(user);
        projectEntity.setOriginalOwner(user);
        return projectEntity;
    }

    public static ProjectDto createProjectDto() {
        return new ProjectDto(TEST_STRING);
    }

    public static ProjectRaw createProjectRaw() {
        return new ProjectRaw()
                .offerNumber(TEST_STRING);
    }

    public static List<ProjectEntity> createProjectEntityList() {
        return List.of(createProjectEntity());
    }

    public static List<ProjectDto> createProjectDtoList() {
        return List.of(createProjectDto(), createProjectDto());
    }

    public static Map<ProjectDto, ProjectEntity> createProjectDtoProjectEntityMap() {
        var map = new HashMap<ProjectDto, ProjectEntity>();
        map.put(createProjectDto(), createProjectEntity());
        map.put(createProjectDto(), createProjectEntity());
        return map;
    }
}
