package de.dlh.lht.ti.utils;

import de.dlh.lht.ti.dto.SubcontractDto;
import de.dlh.lht.ti.entity.SubcontractMetadataEntity;
import de.dlh.lht.ti.entity.SubcontractPricingItemEntity;
import de.dlh.lht.ti.enums.Currency;
import de.dlh.lht.ti.importer.model.PartRaw;
import de.dlh.lht.ti.importer.model.PartType;
import de.dlh.lht.ti.importer.model.SubcontractMetadataRaw;
import de.dlh.lht.ti.importer.model.SubcontractPricingItemRaw;
import de.dlh.lht.ti.model.Subcontract;
import de.dlh.lht.ti.model.SubcontractInput;
import de.dlh.lht.ti.model.SubcontractPricing;
import de.dlh.lht.ti.model.SubcontractPricingRequest;
import de.dlh.lht.ti.model.SubcontractValue;
import java.util.List;
import java.util.Map;


import static de.dlh.lht.ti.utils.ProgressMapperTestHelper.createTrueProgress;
import static de.dlh.lht.ti.utils.TestConstants.LONG_ID_1;
import static de.dlh.lht.ti.utils.TestConstants.LONG_ID_2;
import static de.dlh.lht.ti.utils.TestConstants.TEST_BIG_DECIMAL;
import static de.dlh.lht.ti.utils.TestConstants.TEST_INT_1;
import static de.dlh.lht.ti.utils.TestConstants.TEST_STRING;
import static de.dlh.lht.ti.utils.TestConstants.TEST_YEAR;

public class SubcontractPricingTestHelper {

    private SubcontractPricingTestHelper() {
        throw new AssertionError("Cannot create instances of this class");
    }

    public static SubcontractMetadataRaw createSubcontractMetadataRaw() {
        return new SubcontractMetadataRaw()
                .workscope(TEST_STRING)
                .quantity(TEST_INT_1)
                .weightedQuantity(TEST_BIG_DECIMAL.toEngineeringString())
                .value(TEST_BIG_DECIMAL.toEngineeringString())
                .currency(Currency.USD.toString())
                .year(TEST_YEAR)
                .name(TEST_STRING);
    }

    public static SubcontractMetadataEntity createSubcontractMetadataEntity() {
        var entity = new SubcontractMetadataEntity();
        entity.setQuantity(TEST_INT_1);
        entity.setWeightedQuantity(TEST_BIG_DECIMAL);
        entity.setValue(TEST_BIG_DECIMAL);
        entity.setCurrency(Currency.USD);
        entity.setYear(TEST_YEAR);
        entity.setName(TEST_STRING);
        entity.setSubcontractPricingItemId(LONG_ID_1);
        entity.setWorkscopeId(LONG_ID_1);
        return entity;
    }

    public static List<SubcontractPricingItemRaw> createSubcontractPricingItemRawList() {
        var partRaw = new PartRaw().name(TEST_STRING).type(PartType.A_PART);
        var subcontractMetadataRawList = List.of(createSubcontractMetadataRaw());
        return List.of(new SubcontractPricingItemRaw()
                .part(partRaw)
                .subcontractMetadata(subcontractMetadataRawList));
    }

    public static SubcontractPricingItemEntity createNullInputValueSubcontractPricingItemEntity() {
        var subcontractPricingItem = SubcontractPricingItemEntity.builder()
                .clusterId(LONG_ID_1)
                .quotationEngineId(LONG_ID_1)
                .build();
        subcontractPricingItem.setId(LONG_ID_1);
        return subcontractPricingItem;
    }

    public static SubcontractPricingItemEntity createSubcontractPricingItemEntity() {
        var subcontractPricingItem = SubcontractPricingItemEntity.builder()
                .margin(TEST_BIG_DECIMAL)
                .cap(TEST_INT_1)
                .clusterId(LONG_ID_1)
                .quotationEngineId(LONG_ID_1)
                .build();
        subcontractPricingItem.setId(LONG_ID_1);
        return subcontractPricingItem;
    }

    public static List<SubcontractPricingItemEntity> createSubcontractPricingItemEntityList() {
        return List.of(createSubcontractPricingItemEntity());
    }

    public static Map<Long, SubcontractPricingItemEntity> createSubcontractPricingItemByIdMap(
            List<SubcontractPricingItemEntity> subcontractPricingItemEntityList) {
        return Map.of(subcontractPricingItemEntityList.get(0).getId(), subcontractPricingItemEntityList.get(0));
    }

    public static List<SubcontractDto> createSubcontractDtoList() {
        return List.of(
                new SubcontractDto(LONG_ID_1, TEST_BIG_DECIMAL, TEST_INT_1, LONG_ID_1, TEST_STRING, TEST_INT_1),
                new SubcontractDto(LONG_ID_2, TEST_BIG_DECIMAL, TEST_INT_1, LONG_ID_1, TEST_STRING, TEST_INT_1));
    }

    public static List<Subcontract> createSubcontractList() {
        return List.of(
                new Subcontract()
                        .id(LONG_ID_1)
                        .margin(TEST_BIG_DECIMAL.floatValue())
                        .cap(TEST_BIG_DECIMAL.intValue())
                        .name(TEST_STRING)
                        .order(TEST_INT_1),
                new Subcontract()
                        .id(LONG_ID_2)
                        .margin(TEST_BIG_DECIMAL.floatValue())
                        .cap(TEST_BIG_DECIMAL.intValue())
                        .name(TEST_STRING)
                        .order(TEST_INT_1));
    }

    public static SubcontractValue createSubcontractValue(Float margin, Integer cap) {
        return new SubcontractValue()
                .margin(margin)
                .cap(cap);
    }

    public static SubcontractPricing createSubcontractPricing() {
        return new SubcontractPricing()
                .globalValues(createSubcontractValue(TEST_BIG_DECIMAL.floatValue(), TEST_INT_1))
                .subcontracts(createSubcontractList())
                .progress(createTrueProgress())
                .canCurrentUserEdit(true);
    }

    public static SubcontractPricing createEmptySubcontractsSubcontractPricing() {
        return new SubcontractPricing()
                .globalValues(createSubcontractValue(null, null))
                .subcontracts(List.of())
                .progress(createTrueProgress())
                .canCurrentUserEdit(true);
    }

    public static SubcontractInput createSubcontractInput() {
        return new SubcontractInput()
                .id(LONG_ID_1)
                .cap(TEST_INT_1)
                .margin(TEST_BIG_DECIMAL.floatValue());
    }

    public static SubcontractPricingRequest createNullSubcontractInputsSubcontractPricingRequest() {
        return new SubcontractPricingRequest()
                .subcontractInputs(null);
    }

    public static SubcontractPricingRequest createEmptySubcontractInputsSubcontractPricingRequest() {
        return new SubcontractPricingRequest()
                .subcontractInputs(List.of());
    }

    public static SubcontractPricingRequest createInvalidCapSubcontractInputsSubcontractPricingRequest() {
        return new SubcontractPricingRequest()
                .subcontractInputs(
                        List.of(new SubcontractInput()
                                .id(LONG_ID_1)
                                .cap(-TEST_INT_1)
                                .margin(TEST_BIG_DECIMAL.floatValue())));
    }

    public static SubcontractPricingRequest createInvalidMarginSubcontractInputsSubcontractPricingRequest() {
        return new SubcontractPricingRequest()
                .subcontractInputs(
                        List.of(new SubcontractInput()
                                .id(LONG_ID_1)
                                .cap(TEST_INT_1)
                                .margin(-TEST_BIG_DECIMAL.floatValue())));
    }

    public static SubcontractPricingRequest createSubcontractPricingRequest() {
        return new SubcontractPricingRequest()
                .subcontractInputs(
                        List.of(new SubcontractInput()
                                .id(LONG_ID_1)
                                .cap(TEST_INT_1)
                                .margin(TEST_BIG_DECIMAL.floatValue())));
    }
}
