package de.dlh.lht.ti.utils;

import de.dlh.lht.ti.entity.NavigationItemEntity;
import de.dlh.lht.ti.entity.NavigationStepEntity;
import de.dlh.lht.ti.entity.ProgressStepEntity;
import de.dlh.lht.ti.enums.NavigationSteps;
import de.dlh.lht.ti.enums.QuotationProgress;
import java.util.Map;


import static de.dlh.lht.ti.utils.TestConstants.LONG_ID_1;

public class NavigationItemServiceImplTestHelper {

    private NavigationItemServiceImplTestHelper() {
        throw new AssertionError("Cannot create instances of this class");
    }

    public static Map<NavigationSteps, NavigationStepEntity> createNavigationStepEntityMap() {
        return Map.of(
                NavigationSteps.COVER, new NavigationStepEntity(NavigationSteps.COVER),
                NavigationSteps.MATERIAL_PRICING, new NavigationStepEntity(NavigationSteps.COVER),
                NavigationSteps.LABOUR_PRICING, new NavigationStepEntity(NavigationSteps.LABOUR_PRICING),
                NavigationSteps.SUBCONTRACT_PRICING, new NavigationStepEntity(NavigationSteps.SUBCONTRACT_PRICING),
                NavigationSteps.PRICING_ESCALATION, new NavigationStepEntity(NavigationSteps.PRICING_ESCALATION),
                NavigationSteps.WORKSCOPE_SUMMARY, new NavigationStepEntity(NavigationSteps.WORKSCOPE_SUMMARY));
    }

    public static Map<QuotationProgress, ProgressStepEntity> createProgressStepEntityMap() {
        return Map.of(
                QuotationProgress.COVER, new ProgressStepEntity(QuotationProgress.COVER),
                QuotationProgress.HANDLING_CHARGES, new ProgressStepEntity(QuotationProgress.HANDLING_CHARGES),
                QuotationProgress.Z2_RATINGS, new ProgressStepEntity(QuotationProgress.Z2_RATINGS),
                QuotationProgress.LABOUR_RATE_AND_EPAR, new ProgressStepEntity(QuotationProgress.LABOUR_RATE_AND_EPAR),
                QuotationProgress.RFP_ENGINE, new ProgressStepEntity(QuotationProgress.RFP_ENGINE),
                QuotationProgress.RFP_MODULE, new ProgressStepEntity(QuotationProgress.RFP_MODULE),
                QuotationProgress.SUBCONTRACT_PRICING, new ProgressStepEntity(QuotationProgress.SUBCONTRACT_PRICING),
                QuotationProgress.PRICING_ESCALATION, new ProgressStepEntity(QuotationProgress.PRICING_ESCALATION),
                QuotationProgress.WORKSCOPE_SUMMARY, new ProgressStepEntity(QuotationProgress.WORKSCOPE_SUMMARY));
    }

    public static NavigationItemEntity createNavigationItemEntity() {
        var navigationStepEntity = new NavigationStepEntity(NavigationSteps.COVER);
        var progressStepEntity = new ProgressStepEntity(QuotationProgress.COVER);
        return new NavigationItemEntity(null, LONG_ID_1, navigationStepEntity, progressStepEntity);
    }
}
