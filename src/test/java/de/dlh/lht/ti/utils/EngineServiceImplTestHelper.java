package de.dlh.lht.ti.utils;

import de.dlh.lht.ti.dto.importer.EngineDto;
import de.dlh.lht.ti.entity.EngineEntity;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


import static de.dlh.lht.ti.utils.TestConstants.LONG_ID_1;
import static de.dlh.lht.ti.utils.TestConstants.TEST_STRING;

public class EngineServiceImplTestHelper {

    private EngineServiceImplTestHelper() {
        throw new AssertionError("Cannot create instances of this class");
    }

    public static EngineEntity createEngineEntity() {
        var engine = new EngineEntity(TEST_STRING);
        engine.setId(LONG_ID_1);
        return engine;
    }

    public static EngineDto createEngineDto() {
        return new EngineDto(TEST_STRING);
    }

    public static List<EngineEntity> createEngineEntityList() {
        return List.of(createEngineEntity());
    }

    public static List<EngineDto> createEngineDtoList() {
        return List.of(createEngineDto(), createEngineDto());
    }

    public static Map<EngineDto, EngineEntity> createEngineDtoEngineEntityMap() {
        var map = new HashMap<EngineDto, EngineEntity>();
        map.put(createEngineDto(), createEngineEntity());
        map.put(createEngineDto(), createEngineEntity());
        return map;
    }
}
