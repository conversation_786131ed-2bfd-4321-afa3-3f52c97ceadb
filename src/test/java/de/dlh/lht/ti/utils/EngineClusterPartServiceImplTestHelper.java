package de.dlh.lht.ti.utils;

import de.dlh.lht.ti.dto.importer.EngineClusterPartDto;
import java.util.List;
import java.util.Map;


import static de.dlh.lht.ti.utils.TestConstants.LONG_ID_1;
import static de.dlh.lht.ti.utils.TestConstants.LONG_ID_2;

public class EngineClusterPartServiceImplTestHelper {

    private EngineClusterPartServiceImplTestHelper() {
        throw new AssertionError("Cannot create instances of this class");
    }

    public static List<EngineClusterPartDto> createEngineClusterPartDtoList() {
        return List.of(
                new EngineClusterPartDto(LONG_ID_1, LONG_ID_1, LONG_ID_1),
                new EngineClusterPartDto(LONG_ID_2, LONG_ID_2, LONG_ID_2));
    }

    public static Map<Long, Map<Long, Long>> createExpectedResultMap() {
        return Map.of(
                LONG_ID_1, Map.of(LONG_ID_1, LONG_ID_1),
                LONG_ID_2, Map.of(LONG_ID_2, LONG_ID_2));
    }
}
