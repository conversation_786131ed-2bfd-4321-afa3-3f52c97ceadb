package de.dlh.lht.ti.utils;

import de.dlh.lht.ti.dto.importer.PartDto;
import de.dlh.lht.ti.entity.PartEntity;
import de.dlh.lht.ti.enums.PartType;
import de.dlh.lht.ti.importer.model.PartRaw;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


import static de.dlh.lht.ti.utils.TestConstants.LONG_ID_1;
import static de.dlh.lht.ti.utils.TestConstants.LONG_ID_2;
import static de.dlh.lht.ti.utils.TestConstants.TEST_STRING;

public class PartServiceImplTestHelper {

    private PartServiceImplTestHelper() {
        throw new AssertionError("Cannot create instances of this class");
    }

    public static PartEntity createPartEntity() {
        var part = new PartEntity();
        part.setId(LONG_ID_1);
        part.setName(TEST_STRING);
        part.setType(PartType.A_PART);
        return part;
    }

    public static PartDto createPartDto() {
        return new PartDto(TEST_STRING, PartType.A_PART);
    }

    public static PartRaw createPartRaw() {
        return new PartRaw()
                .name(TEST_STRING)
                .type(de.dlh.lht.ti.importer.model.PartType.A_PART);
    }

    public static List<PartEntity> createPartEntityList() {
        return List.of(createPartEntity());
    }

    public static List<PartDto> createPartDtoList() {
        return List.of(createPartDto(), createPartDto());
    }

    public static Map<PartDto, Long> createPartDtoPartIdMap() {
        var map = new HashMap<PartDto, Long>();
        map.put(createPartDto(), LONG_ID_1);
        map.put(createPartDto(), LONG_ID_2);
        return map;
    }
}
