package de.dlh.lht.ti;

import static java.lang.annotation.ElementType.TYPE;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

import java.lang.annotation.Retention;
import java.lang.annotation.Target;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.ActiveProfiles;

@Target(TYPE)
@Retention(RUNTIME)
@WithMockUser(
    username = "IronMan",
    authorities = {"AUTHENTICATED", "APP_SAMPLE_USER", "APP_SAMPLE_ADMIN"})
@ActiveProfiles("test")
@AutoConfigureMockMvc
public @interface AuthenticatedMvcTest {}
