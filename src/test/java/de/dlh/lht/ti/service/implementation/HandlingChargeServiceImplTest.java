package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.entity.HandlingChargeEntity;
import de.dlh.lht.ti.enums.QuotationProgress;
import de.dlh.lht.ti.mapper.HandlingChargeMapper;
import de.dlh.lht.ti.model.HandlingCharges;
import de.dlh.lht.ti.model.HandlingChargesRequest;
import de.dlh.lht.ti.repository.HandlingChargeRepository;
import de.dlh.lht.ti.service.contract.ClusterService;
import de.dlh.lht.ti.service.contract.MaterialPricingItemService;
import de.dlh.lht.ti.service.contract.NavigationItemService;
import de.dlh.lht.ti.service.contract.QuotationService;
import de.dlh.lht.ti.service.contract.WorkscopeService;
import java.util.concurrent.atomic.AtomicReference;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;


import static de.dlh.lht.ti.utils.HandlingChargeServiceImplHelper.createHandlingChargeDtoList;
import static de.dlh.lht.ti.utils.TestConstants.LONG_ID_1;
import static de.dlh.lht.ti.utils.TestConstants.TEST_YEAR;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class HandlingChargeServiceImplTest {
    @Mock
    private AtomicReference<HandlingChargeEntity> handlingChargeEntityAtomicReferenceMock;
    @Mock
    private HandlingChargeEntity handlingChargeEntityMock;

    @Mock
    private HandlingChargeMapper handlingChargeMapperMock;

    @Mock
    private HandlingChargeRepository handlingChargeRepositoryMock;

    @Mock
    private ClusterService clusterServiceMock;
    @Mock
    private MaterialPricingItemService materialPricingItemServiceMock;
    @Mock
    private NavigationItemService navigationItemServiceMock;
    @Mock
    private WorkscopeService workscopeServiceMock;
    @Mock
    private QuotationService quotationServiceMock;

    @InjectMocks
    private HandlingChargeServiceImpl handlingChargeService;

//    @Test
//    void getHandlingCharges_shouldCreateEntities_whenDoNotExist() {
//        var handlingCharge = createHandlingChargeEntity();
//        var handlingCharges = new HandlingCharges();
//        handlingChargeEntityAtomicReferenceMock.set(handlingCharge);
//
//        when(workscopeServiceMock.getDeepestWorkscopeIdByQuotationId(anyLong())).thenReturn(LONG_ID_1);
//        when(quotationServiceMock.getQuotationBaseYear(anyLong())).thenReturn(TEST_YEAR);
//        when(handlingChargeRepositoryMock.findAllByQuotationId(anyLong(), anyLong(), anyString())).thenReturn(Collections.emptyList());
//        when(clusterServiceMock.getClusterIdByName(anyString())).thenReturn(LONG_ID_1);
//        when(handlingChargeMapperMock.handlingChargesDtoListToHandlingCharges(anyList())).thenReturn(handlingCharges);
//
//        handlingChargeService.getHandlingCharges(LONG_ID_1, true);
//
//        verify(workscopeServiceMock).getDeepestWorkscopeIdByQuotationId(anyLong());
//        verify(quotationServiceMock).getQuotationBaseYear(anyLong());
//        verify(handlingChargeRepositoryMock, times(2)).findAllByQuotationId(anyLong(), anyLong(), anyString());
//        verify(navigationItemServiceMock).getQuotationProgressByQuotationId(anyLong());
//        verify(materialPricingItemServiceMock).getAllMaterialPricingItemsByQuotationId(anyLong());
//        verify(handlingChargeRepositoryMock).saveAll(anyList());
//    }

    @Test
    void getHandlingCharges_shouldReturnHandingCharges_whenExists() {
        var handlingCharges = new HandlingCharges();
        var handlingChargeDtoList = createHandlingChargeDtoList();

        when(workscopeServiceMock.getDeepestWorkscopeIdByQuotationId(anyLong())).thenReturn(LONG_ID_1);
        when(quotationServiceMock.getQuotationBaseYear(anyLong())).thenReturn(TEST_YEAR);
        when(handlingChargeRepositoryMock.findAllByQuotationId(anyLong(), anyLong(), anyString())).thenReturn(handlingChargeDtoList);
        when(handlingChargeMapperMock.handlingChargesDtoListToHandlingCharges(anyList())).thenReturn(handlingCharges);

        handlingChargeService.getHandlingCharges(LONG_ID_1, true);

        verify(workscopeServiceMock).getDeepestWorkscopeIdByQuotationId(anyLong());
        verify(quotationServiceMock).getQuotationBaseYear(anyLong());
        verify(handlingChargeRepositoryMock).findAllByQuotationId(anyLong(), anyLong(), anyString());
        verify(handlingChargeMapperMock).handlingChargesDtoListToHandlingCharges(anyList());
    }

    @Test
    void saveHandlingCharges_shouldSaveHandingCharges() {
        var handlingCharges = new HandlingCharges();
        var handlingChargeDtoList = createHandlingChargeDtoList();

        when(workscopeServiceMock.getDeepestWorkscopeIdByQuotationId(anyLong())).thenReturn(LONG_ID_1);
        when(quotationServiceMock.getQuotationBaseYear(anyLong())).thenReturn(TEST_YEAR);
        when(handlingChargeRepositoryMock.findAllByQuotationId(anyLong(), anyLong(), anyString())).thenReturn(handlingChargeDtoList);
        when(handlingChargeMapperMock.handlingChargesDtoListToHandlingCharges(anyList())).thenReturn(handlingCharges);

        handlingChargeService.saveHandlingCharges(LONG_ID_1, new HandlingChargesRequest());

        verify(workscopeServiceMock).getDeepestWorkscopeIdByQuotationId(anyLong());
        verify(quotationServiceMock).getQuotationBaseYear(anyLong());
        verify(handlingChargeRepositoryMock).findAllByQuotationId(anyLong(), anyLong(), anyString());
        verify(handlingChargeMapperMock).handlingChargesRequestToHandlingChargeDtoList(any(HandlingChargesRequest.class), anyList());
        verify(handlingChargeRepositoryMock).saveAll(anyList());
        verify(navigationItemServiceMock).updateProgress(anyLong(), any(QuotationProgress.class), anyBoolean());
        verify(navigationItemServiceMock).getQuotationProgressByQuotationId(anyLong());
        verify(handlingChargeMapperMock).handlingChargesDtoListToHandlingCharges(anyList());
    }
}
