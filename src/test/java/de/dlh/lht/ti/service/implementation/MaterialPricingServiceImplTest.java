package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.exception.SaveMaterialPricingNotAllowedException;
import de.dlh.lht.ti.model.HandlingChargesRequest;
import de.dlh.lht.ti.service.contract.HandlingChangeService;
import de.dlh.lht.ti.service.contract.UserPermissionService;
import de.dlh.lht.ti.service.contract.UserService;
import de.dlh.lht.ti.service.contract.Z2RatingService;
import java.util.HashMap;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;


import static de.dlh.lht.ti.utils.ErrorMessages.SAVE_HANDLING_CHARGES_NOT_ALLOWED_ERROR_MESSAGE;
import static de.dlh.lht.ti.utils.ErrorMessages.SAVE_Z2_RATING_NOT_ALLOWED_ERROR_MESSAGE;
import static de.dlh.lht.ti.utils.TestConstants.LONG_ID_1;
import static de.dlh.lht.ti.utils.TestConstants.TEST_STRING;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class MaterialPricingServiceImplTest {

    @Mock
    private HandlingChangeService handlingChangeServiceMock;
    @Mock
    private UserService userServiceMock;
    @Mock
    private Z2RatingService z2RatingServiceMock;
    @Mock
    private UserPermissionService userPermissionServiceMock;

    @InjectMocks
    private MaterialPricingServiceImpl materialPricingService;

    @Test
    void getHandlingCharges_shouldCallGetHandlingCharges() {
        materialPricingService.getHandlingCharges(LONG_ID_1);

        verify(handlingChangeServiceMock, times(1)).getHandlingCharges(anyLong(), anyBoolean());
    }

    @Test
    void saveHandlingCharges_shouldCallSaveHandlingCharges() {
        var handlingChargesRequest = new HandlingChargesRequest();

        when(userServiceMock.getCurrentOwnerUNumberByQuotationId(anyLong())).thenReturn(TEST_STRING);
        when(userPermissionServiceMock.isUserOwnerOrAdmin(any())).thenReturn(true);

        materialPricingService.saveHandlingCharges(LONG_ID_1, handlingChargesRequest);

        verify(handlingChangeServiceMock).saveHandlingCharges(anyLong(), any(HandlingChargesRequest.class));
    }

    @Test
    void getZ2Ratings_shouldCallGetZ2Ratings() {
        materialPricingService.getZ2Ratings(LONG_ID_1);

        verify(z2RatingServiceMock, times(1)).getZ2Ratings(anyLong(), anyBoolean());
    }

    @Test
    void saveZ2Ratings_shouldCallSaveZ2Ratings() {
        //arrange
        var z2RatingsInput = new HashMap<Long, Float>();
        when(userServiceMock.getCurrentOwnerUNumberByQuotationId(anyLong())).thenReturn(TEST_STRING);
        when(userPermissionServiceMock.isUserOwnerOrAdmin(any())).thenReturn(true);
        //act
        materialPricingService.saveZ2Ratings(LONG_ID_1, z2RatingsInput);
        //assert
        verify(z2RatingServiceMock, times(1)).saveZ2Ratings(anyLong(), anyMap());
    }

    @Test
    void saveZ2Ratings_shouldThrow_whenUserIsNotOwner() {
        //arrange
        var z2RatingsInput = new HashMap<Long, Float>();
        when(userServiceMock.getCurrentOwnerUNumberByQuotationId(anyLong())).thenReturn(TEST_STRING);
        when(userPermissionServiceMock.isUserOwnerOrAdmin(anyString())).thenReturn(false);

        //act and assert
        var exception = assertThrows(SaveMaterialPricingNotAllowedException.class,
                () -> materialPricingService.saveZ2Ratings(LONG_ID_1, z2RatingsInput));
        assertEquals(String.format(SAVE_Z2_RATING_NOT_ALLOWED_ERROR_MESSAGE, TEST_STRING), exception.getMessage());
    }

    @Test
    void saveHandlingCharges_shouldThrow_whenUserIsNotOwner() {
        //arrange
        var handlingChargesRequest = new HandlingChargesRequest();
        when(userServiceMock.getCurrentOwnerUNumberByQuotationId(anyLong())).thenReturn(TEST_STRING);
        when(userPermissionServiceMock.isUserOwnerOrAdmin(anyString())).thenReturn(false);

        //act and assert
        var exception = assertThrows(SaveMaterialPricingNotAllowedException.class,
                () -> materialPricingService.saveHandlingCharges(LONG_ID_1, handlingChargesRequest));
        assertEquals(String.format(SAVE_HANDLING_CHARGES_NOT_ALLOWED_ERROR_MESSAGE, TEST_STRING), exception.getMessage());
    }

}
