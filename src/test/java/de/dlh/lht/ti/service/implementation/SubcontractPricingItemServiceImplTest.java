package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.importer.model.PartRaw;
import de.dlh.lht.ti.repository.SubcontractPricingItemRepository;
import de.dlh.lht.ti.service.contract.PartService;
import java.util.ArrayList;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;


import static de.dlh.lht.ti.utils.MaterialPricingTestHelper.createClusterIdByEngineIdByPartIdMap;
import static de.dlh.lht.ti.utils.PartServiceImplTestHelper.createPartDtoPartIdMap;
import static de.dlh.lht.ti.utils.SubcontractPricingTestHelper.createSubcontractPricingItemByIdMap;
import static de.dlh.lht.ti.utils.SubcontractPricingTestHelper.createSubcontractPricingItemEntityList;
import static de.dlh.lht.ti.utils.SubcontractPricingTestHelper.createSubcontractPricingItemRawList;
import static de.dlh.lht.ti.utils.TestConstants.LONG_ID_1;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class SubcontractPricingItemServiceImplTest {

    @Mock
    private SubcontractPricingItemRepository subcontractPricingItemRepositoryMock;

    @Mock
    private PartService partServiceMock;

    @InjectMocks
    private SubcontractPricingItemServiceImpl subcontractPricingItemService;

    @Test
    void createSubcontractPricingItems_shouldReturnEmptyArray_onNullRawSubcontractPricingItems() {
        var partDtoPartIdMap = createPartDtoPartIdMap();
        var clusterIdByEngineIdByPartIdMap = createClusterIdByEngineIdByPartIdMap();

        var result = subcontractPricingItemService.createSubcontractPricingItems(
                null,
                LONG_ID_1,
                LONG_ID_1,
                partDtoPartIdMap,
                clusterIdByEngineIdByPartIdMap);

        assertEquals(new ArrayList<>(), result);

        verify(partServiceMock, never()).findPartIdByNameAndType(any(PartRaw.class), anyMap());
        verify(subcontractPricingItemRepositoryMock, never()).saveAll(any());
    }

    @Test
    void createSubcontractPricingItems_shouldReturnEmptyArray_onEmptyRawSubcontractPricingItems() {
        var partDtoPartIdMap = createPartDtoPartIdMap();
        var clusterIdByEngineIdByPartIdMap = createClusterIdByEngineIdByPartIdMap();

        var result = subcontractPricingItemService.createSubcontractPricingItems(
                new ArrayList<>(),
                LONG_ID_1,
                LONG_ID_1,
                partDtoPartIdMap,
                clusterIdByEngineIdByPartIdMap);

        assertEquals(new ArrayList<>(), result);

        verify(partServiceMock, never()).findPartIdByNameAndType(any(PartRaw.class), anyMap());
        verify(subcontractPricingItemRepositoryMock, never()).saveAll(any());
    }

    @Test
    void createSubcontractPricingItems_shouldSaveAll() {
        var subcontractPricingItemRawList = createSubcontractPricingItemRawList();
        var partDtoPartIdMap = createPartDtoPartIdMap();
        var clusterIdByEngineIdByPartIdMap = createClusterIdByEngineIdByPartIdMap();

        when(partServiceMock.findPartIdByNameAndType(any(PartRaw.class), anyMap())).thenReturn(LONG_ID_1);

        var result = subcontractPricingItemService.createSubcontractPricingItems(
                subcontractPricingItemRawList,
                LONG_ID_1,
                LONG_ID_1,
                partDtoPartIdMap,
                clusterIdByEngineIdByPartIdMap);

        assertEquals(new ArrayList<>(), result);

        verify(partServiceMock).findPartIdByNameAndType(any(PartRaw.class), anyMap());
        verify(subcontractPricingItemRepositoryMock).saveAll(any());
    }

    @Test
    void getSubcontractPricingItemsByIdMapByQuotationId_shouldReturnAMapOfEntitiesByIds() {
        var subcontractPricingItems = createSubcontractPricingItemEntityList();
        var expectedResult = createSubcontractPricingItemByIdMap(subcontractPricingItems);

        when(subcontractPricingItemRepositoryMock.findAllByQuotationId(anyLong())).thenReturn(subcontractPricingItems);

        var result = subcontractPricingItemService.getSubcontractPricingItemsByIdMapByQuotationId(LONG_ID_1);

        assertEquals(expectedResult, result);

        verify(subcontractPricingItemRepositoryMock).findAllByQuotationId(anyLong());
    }

    @Test
    void getAllSubcontractDtosByQuotationId_shouldCallRepository() {
        subcontractPricingItemService.getAllSubcontractDtosByQuotationId(LONG_ID_1);

        verify(subcontractPricingItemRepositoryMock).findAllSubcontractDtosByQuotationId(LONG_ID_1);
    }

    @Test
    void updateSubcontractPricingItems_shouldCallRepository() {
        var subcontractPricingItems = createSubcontractPricingItemEntityList();

        subcontractPricingItemService.updateSubcontractPricingItems(subcontractPricingItems);

        verify(subcontractPricingItemRepositoryMock).saveAll(subcontractPricingItems);
    }
}
