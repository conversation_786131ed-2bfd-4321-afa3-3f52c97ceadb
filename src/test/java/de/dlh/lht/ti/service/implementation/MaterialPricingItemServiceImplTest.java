package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.entity.MaterialPricingItemEntity;
import de.dlh.lht.ti.repository.MaterialPricingItemRepository;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;


import static de.dlh.lht.ti.utils.TestConstants.LONG_ID_1;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class MaterialPricingItemServiceImplTest {

    @Captor
    private ArgumentCaptor<MaterialPricingItemEntity> materialPricingItemEntityArgumentCaptor;

    @Mock
    private MaterialPricingItemRepository materialPricingItemRepositoryMock;

    @InjectMocks
    private MaterialPricingItemServiceImpl materialPricingItemService;

    @Test
    void createMaterialPricingItemEntity_shouldSucceed() {
        materialPricingItemService.createMaterialPricingItemEntity(LONG_ID_1, LONG_ID_1, LONG_ID_1, null);

        verify(materialPricingItemRepositoryMock).save(materialPricingItemEntityArgumentCaptor.capture());

        var newMaterialPricingItem = materialPricingItemEntityArgumentCaptor.getValue();
        assertEquals(LONG_ID_1, newMaterialPricingItem.getClusterId());
        assertEquals(LONG_ID_1, newMaterialPricingItem.getPartId());
        assertEquals(LONG_ID_1, newMaterialPricingItem.getQuotationEngineId());
    }
}
