package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.entity.WorkscopeEntity;
import de.dlh.lht.ti.mapper.WorkscopeMapper;
import de.dlh.lht.ti.repository.WorkscopeRepository;
import de.dlh.lht.ti.service.contract.EngineService;
import de.dlh.lht.ti.utils.WorkscopeServiceImplTestHelper;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;


import static de.dlh.lht.ti.utils.TestConstants.LONG_ID_1;
import static de.dlh.lht.ti.utils.TestConstants.LONG_ID_2;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class WorkscopeServiceImplTest {

    @Mock
    private WorkscopeMapper workscopeMapper;

    @Mock
    private WorkscopeRepository workscopeRepository;

    @Mock
    private EngineService engineService;

    @InjectMocks
    private WorkscopeServiceImpl workscopeService;

    @Test
    void createWorkscope_ShouldSaveAndReturnWorkscopeEntity() {
        // Arrange
        var workscopeDto = WorkscopeServiceImplTestHelper.createWorkscopeDto();
        var workscopeEntity = WorkscopeServiceImplTestHelper.createWorkscopeEntity(LONG_ID_1);
        when(workscopeMapper.workscopeDtoToWorkscopeEntity(workscopeDto)).thenReturn(workscopeEntity);
        when(workscopeRepository.save(workscopeEntity)).thenReturn(workscopeEntity);

        // Act
        WorkscopeEntity result = workscopeService.createWorkscope(workscopeDto);

        // Assert
        assertEquals(workscopeEntity, result);
        verify(workscopeMapper).workscopeDtoToWorkscopeEntity(workscopeDto);
        verify(workscopeRepository).save(workscopeEntity);
    }

    @Test
    void getWorkscopesByQuotationId_ShouldReturnWorkscopeEntities() {
        // Arrange
        Long quotationId = LONG_ID_1;
        var workscopeEntities = WorkscopeServiceImplTestHelper.createWorkscopeEntityList();
        when(workscopeRepository.findWorkscopesByQuotationId(quotationId)).thenReturn(workscopeEntities);

        // Act
        var result = workscopeService.getWorkscopesByQuotationId(quotationId);

        // Assert
        assertEquals(workscopeEntities, result);
        verify(workscopeRepository).findWorkscopesByQuotationId(quotationId);
    }

    @Test
    void getWorkscopeIdByWorkscopeName_ExistingName_ShouldReturnWorkscopeId() {
        // Arrange
        String workscopeName = "L3";
        String engineName = "V2500";
        Long workscopeId = LONG_ID_2;
        when(workscopeRepository.findWorkscopeIdByName(workscopeName)).thenReturn(Optional.of(workscopeId));
        when(engineService.getEngineNameByQuotationId(anyLong())).thenReturn(engineName);

        // Act
        Long result = workscopeService.getDeepestWorkscopeIdByQuotationId(LONG_ID_1);

        // Assert
        assertEquals(workscopeId, result);
        verify(workscopeRepository).findWorkscopeIdByName(workscopeName);
    }

    @Test
    void workscopeRawListToWorkscopeDtoList_ShouldReturnWorkscopeDtoList() {
        // Arrange
        var rawWorkscopes = List.of(
                WorkscopeServiceImplTestHelper.createWorkscopeRaw(),
                WorkscopeServiceImplTestHelper.createWorkscopeRaw()
        );
        var expectedDtoList = WorkscopeServiceImplTestHelper.createWorkscopeDtoList();
        when(workscopeMapper.workscopeRawListToWorkscopeDtoList(rawWorkscopes)).thenReturn(expectedDtoList);
        // Act
        var result = workscopeService.workscopeRawListToWorkscopeDtoList(rawWorkscopes);

        // Assert
        assertEquals(expectedDtoList, result);
        verify(workscopeMapper).workscopeRawListToWorkscopeDtoList(rawWorkscopes);
    }
}
