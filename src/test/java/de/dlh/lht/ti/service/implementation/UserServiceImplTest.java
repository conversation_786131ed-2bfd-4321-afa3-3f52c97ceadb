package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.auth.UserPrincipal;
import de.dlh.lht.ti.auth.contract.UserPrincipalProvider;
import de.dlh.lht.ti.auth.roles.Role;
import de.dlh.lht.ti.mapper.UserMapper;
import de.dlh.lht.ti.repository.UserRepository;
import de.dlh.lht.ti.service.contract.UserPermissionService;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;


import static de.dlh.lht.ti.utils.TestConstants.TEST_STRING;
import static de.dlh.lht.ti.utils.UserServiceImplTestHelper.createDomainPermission;
import static de.dlh.lht.ti.utils.UserServiceImplTestHelper.createUserDetails;
import static de.dlh.lht.ti.utils.UserServiceImplTestHelper.createUserEntity;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class UserServiceImplTest {

    @Mock
    private UserMapper userMapper;

    @Mock
    private UserRepository userRepository;

    @Mock
    private UserPrincipalProvider userPrincipalProvider;

    @Mock
    private UserPermissionService userPermissionService;

    @InjectMocks
    private UserServiceImpl userService;

    @Test
    void testGetAllUsers_WhenGetUsersIsCalledOnUserWithViewOnlyRole() {
        //arrange
        when(userPrincipalProvider.getRoles()).thenReturn(List.of(Role.VIEW_ONLY_USER));
        //act
        userService.getAllUsers();
        //assert
        verify(userRepository, never()).findAll();
    }

    @Test
    void testGetAllUsers_WhenGetUsersIsCalledOnUserWithCalculationUserRole() {
        //arrange
        when(userPrincipalProvider.getRoles()).thenReturn(List.of(Role.CALCULATION_USER));
        //act
        userService.getAllUsers();
        //assert
        verify(userRepository, times(1)).findAll();
    }

    @Test
    void testGetAllUsers_WhenGetUsersIsCalledOnUserWithAdminRole() {
        //arrange
        when(userPrincipalProvider.getRoles()).thenReturn(List.of(Role.ADMIN_USER));
        //act
        userService.getAllUsers();
        //assert
        verify(userRepository, times(1)).findAll();
    }

    @Test
    void getUserDetails_returnsSuccessfully() {
        var userEntity = createUserEntity();
        var domainPermission = createDomainPermission();
        var expectedResult = createUserDetails();

        when(userPrincipalProvider.getUserPrincipal()).thenReturn(new UserPrincipal(TEST_STRING, TEST_STRING, null));
        when(userRepository.findByuNumber(TEST_STRING)).thenReturn(Optional.of(userEntity));
        when(userRepository.save(any())).thenReturn(userEntity);
        when(userPrincipalProvider.getPermissions()).thenReturn(List.of(domainPermission));

        var result = userService.getUserDetails();

        verify(userPrincipalProvider, times(1)).getUserPrincipal();
        verify(userRepository, times(1)).findByuNumber(TEST_STRING);
        Assertions.assertEquals(expectedResult, result);
    }

    @Test
    void getUserDetails_createsUserWhenDoesntExistsWithCalculationUserRole() {
        var userEntity = createUserEntity();
        var domainPermission = createDomainPermission();
        var expectedResult = createUserDetails();

        when(userPrincipalProvider.getUserPrincipal()).thenReturn(new UserPrincipal(TEST_STRING, TEST_STRING, null));
        when(userRepository.findByuNumber(any())).thenReturn(Optional.empty());
        when(userRepository.save(any())).thenReturn(userEntity);
        when(userPrincipalProvider.getPermissions()).thenReturn(List.of(domainPermission));

        var result = userService.getUserDetails();

        verify(userPrincipalProvider, times(1)).getUserPrincipal();
        verify(userRepository, times(1)).findByuNumber(TEST_STRING);
        verify(userRepository, times(1)).save(any());
        Assertions.assertEquals(expectedResult, result);
    }

    @Test
    void getUserDetails_doNotCreatesUserWhenDoesntExistsWithCalculationUserRole() {
        var userEntity = createUserEntity();
        var domainPermission = createDomainPermission();
        var expectedResult = createUserDetails();

        when(userPrincipalProvider.getUserPrincipal()).thenReturn(new UserPrincipal(TEST_STRING, TEST_STRING, null));
        when(userRepository.findByuNumber(any())).thenReturn(Optional.empty());
        when(userRepository.save(any())).thenReturn(userEntity);
        when(userPrincipalProvider.getPermissions()).thenReturn(List.of(domainPermission));

        var result = userService.getUserDetails();

        verify(userPrincipalProvider, times(1)).getUserPrincipal();
        verify(userRepository, times(1)).findByuNumber(TEST_STRING);
        verify(userRepository, times(1)).save(any());
        Assertions.assertEquals(expectedResult, result);
    }
}