package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.entity.PartMetadataEntity;
import de.dlh.lht.ti.repository.PartMetadataRepository;
import java.util.ArrayList;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;


import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class PartMetadataServiceImplTest {

    @Mock
    private PartMetadataRepository partMetadataRepositoryMock;

    @InjectMocks
    private PartMetadataServiceImpl partMetadataService;

    @Test
    void createPartMetadata_shouldSucceed() {
        var partMetadata = new ArrayList<PartMetadataEntity>();
        partMetadata.add(new PartMetadataEntity());

        partMetadataService.createPartMetadata(partMetadata);

        verify(partMetadataRepositoryMock, times(1)).saveAll(partMetadata);
    }
}
