package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.repository.ProgressStepRepository;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;


import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class ProgressStepServiceImplTest {

    @Mock
    ProgressStepRepository progressStepRepositoryMock;

    @InjectMocks
    ProgressStepServiceImpl progressStepService;

    @Test
    void getAllProgressSteps_shouldCallTheRepo() {
        progressStepService.getAllProgressSteps();

        verify(progressStepRepositoryMock).findAll();
    }
}
