package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.dto.LabourPricingItemDto;
import de.dlh.lht.ti.importer.model.LabourPricingItemRaw;
import de.dlh.lht.ti.importer.model.TaskRaw;
import de.dlh.lht.ti.importer.model.TaskType;
import de.dlh.lht.ti.mapper.LabourPricingItemMapper;
import de.dlh.lht.ti.repository.LabourPricingItemRepository;
import de.dlh.lht.ti.service.contract.ClusterService;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;


import static de.dlh.lht.ti.utils.TestConstants.LONG_ID_1;
import static de.dlh.lht.ti.utils.TestConstants.LONG_ID_2;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class LabourPricingItemServiceImplTest {

    @Mock
    private LabourPricingItemRepository labourPricingItemRepository;

    @Mock
    private LabourPricingItemMapper labourPricingItemMapper;

    @Mock
    private ClusterService clusterService;

    @InjectMocks
    private LabourPricingItemServiceImpl labourPricingItemService;

    @Test
    void testGetLabourPricingItemsDtos() {
        // arrange
        Long quotationEngineId = LONG_ID_1;
        var expectedItems = new ArrayList<LabourPricingItemDto>();
        when(labourPricingItemRepository.findAllLabourPricingItemsByQuotationEngineId(quotationEngineId))
                .thenReturn(expectedItems);

        // act
        labourPricingItemService.getLabourPricingItemsDtos(quotationEngineId);

        // assert
        verify(labourPricingItemRepository)
                .findAllLabourPricingItemsByQuotationEngineId(quotationEngineId);
    }

    @Test
    void testCreateLabourPricingItems() {
        // arrange
        var labourPricingItemRaw = new LabourPricingItemRaw().task(new TaskRaw().type(TaskType.RFP_ENGINE));
        var newItems = new ArrayList<>(List.of(labourPricingItemRaw));

        // act
        labourPricingItemService.createLabourPricingItems(newItems, LONG_ID_1, LONG_ID_2);

        // assert
        verify(labourPricingItemRepository).saveAll(anyList());
    }

    @Test
    void getAllRfpModuleLabourPricingItemIdsByQuotationId_shouldCallRepository() {
        labourPricingItemService.getAllRfpModuleLabourPricingItemIdsByQuotationId(LONG_ID_1);

        verify(labourPricingItemRepository).findAllRfpModuleLabourPricingItemIdsByQuotationId(anyLong());
    }
}
