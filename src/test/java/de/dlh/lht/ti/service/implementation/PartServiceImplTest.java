package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.entity.PartEntity;
import de.dlh.lht.ti.importer.model.PartRaw;
import de.dlh.lht.ti.mapper.PartMapper;
import de.dlh.lht.ti.repository.PartRepository;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;


import static de.dlh.lht.ti.utils.PartServiceImplTestHelper.createPartEntityList;
import static de.dlh.lht.ti.utils.PartServiceImplTestHelper.createPartRaw;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class PartServiceImplTest {

    @Mock
    private PartMapper partMapperMock;

    @Mock
    private PartRepository partRepositoryMock;

    @InjectMocks
    private PartServiceImpl partService;

    @Test
    void partRawToPartDto_shouldCallMapper() {
        var partRaw = createPartRaw();

        partService.partRawToPartDto(partRaw);

        verify(partMapperMock).partRawToPartDto(any(PartRaw.class));
    }

    @Test
    void getPartDtoPartEntityMap_shouldGetAllThePartsAndMapThemIntoMap() {
        var parts = createPartEntityList();

        when(partRepositoryMock.findAll()).thenReturn(parts);

        partService.getPartIdByPartDtoMap();

        verify(partRepositoryMock).findAll();
        verify(partMapperMock).partEntityToPartDto(any(PartEntity.class));
    }
}
