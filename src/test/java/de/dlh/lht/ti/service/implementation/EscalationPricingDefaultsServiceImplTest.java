package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.exception.EscalationDefaultValueMissingException;
import de.dlh.lht.ti.mapper.EscalationMapper;
import de.dlh.lht.ti.repository.EscalationsMaterialPricesDefaultsEntityRepository;
import de.dlh.lht.ti.utils.SubcontractEscalationsDefaults;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static de.dlh.lht.ti.utils.EscalationConstants.*;
import static de.dlh.lht.ti.utils.EscalationPricingServiceImplTestHelper.*;
import static de.dlh.lht.ti.utils.TestConstants.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class EscalationPricingDefaultsServiceImplTest {

    @Mock
    private EscalationMapper escalationMapperMock;

    @Mock
    private SubcontractEscalationsDefaults subcontractEscalationsDefaultsMock;

    @Mock
    private EscalationsMaterialPricesDefaultsEntityRepository escalationsMaterialPricesDefaultsEntityRepositoryMock;

    @InjectMocks
    private EscalationPricingDefaultsServiceImpl escalationPricingDefaultsService;

    @Test
    void mapEntityToDtoWithMaterialPricesDefaultValue_ShouldReturn() {
        var expectedEscalationPricingEntity = createEscalationPricingEntity();
        var escalationPricingDto = createEscalationPricingDto();

        when(escalationsMaterialPricesDefaultsEntityRepositoryMock.findByYearAndEngineId(anyString(), anyLong())).thenReturn(TEST_BIG_DECIMAL);
        when(escalationMapperMock.escalationPricingEntityToEscalationPricingDto(expectedEscalationPricingEntity)).thenReturn(escalationPricingDto);

        var result = escalationPricingDefaultsService.mapEntityToDtoWithMaterialPricesDefaultValue(expectedEscalationPricingEntity, LONG_ID_1);

        assertNotNull(result);
        verify(escalationMapperMock).escalationPricingEntityToEscalationPricingDto(expectedEscalationPricingEntity);
        assertEquals(TEST_BIG_DECIMAL, result.getHcMaterialPricesDefault());
    }

    @Test
    void getEscalationsMaterialPricesDefault_shouldReturn() {
        when(escalationsMaterialPricesDefaultsEntityRepositoryMock.findByYearAndEngineId(TEST_YEAR, LONG_ID_1)).thenReturn(TEST_BIG_DECIMAL);

        var result = escalationPricingDefaultsService.getEscalationsMaterialPricesDefault(TEST_YEAR, LONG_ID_1);

        assertNotNull(result);
        verify(escalationsMaterialPricesDefaultsEntityRepositoryMock).findByYearAndEngineId(TEST_YEAR, LONG_ID_1);
        assertEquals(TEST_BIG_DECIMAL, result);
    }

    @Test
    void getEscalationsSubcontractPricesDefault_withExistingYear_shouldReturnDefaultPercentage() {
        var result = escalationPricingDefaultsService.getEscalationsSubcontractPricesDefault(TEST_YEAR_2023);

        assertNotNull(result);
        assertEquals(SUBCONTRACT_ESCALATION_FOR_2023, result);
    }

    @Test
    void getEscalationsSubcontractPricesDefault_withNonExistingYear_shouldThrowEscalationDefaultValueMissingException() {
        assertThrows(EscalationDefaultValueMissingException.class, () -> escalationPricingDefaultsService.getEscalationsSubcontractPricesDefault(TEST_YEAR_2021));
    }

    @Test
    void setDefaultValues_shouldSetDefaultValuesForEachEscalationPricingDto() {
        var escalationPriceDtoList = createEscalationPricingDtoList();

        escalationPricingDefaultsService.setDefaultValues(escalationPriceDtoList, true);

        assertEquals(SUBCONTRACT_ESCALATION_FOR_2022, escalationPriceDtoList.get(0).getHcSubcontractPricesDefault());
        assertEquals(LABOUR_PRICE_ESCALATION_DEFAULT, escalationPriceDtoList.get(0).getLabourPricesDefault());
        assertEquals(EPAR_PRICE_ESCALATION_DEFAULT, escalationPriceDtoList.get(0).getEparPricesDefault());
        assertEquals(RFP_LABOUR_PRICE_ESCALATION_DEFAULT, escalationPriceDtoList.get(0).getRfpLabourDefault());
    }

}
