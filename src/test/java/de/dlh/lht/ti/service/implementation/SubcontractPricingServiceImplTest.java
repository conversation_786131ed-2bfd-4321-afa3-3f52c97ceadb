package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.entity.SubcontractPricingItemEntity;
import de.dlh.lht.ti.enums.QuotationProgress;
import de.dlh.lht.ti.exception.SaveSubcontractPricingNotAllowedException;
import de.dlh.lht.ti.exception.ValidationException;
import de.dlh.lht.ti.mapper.SubcontractPricingMapper;
import de.dlh.lht.ti.model.SubcontractInput;
import de.dlh.lht.ti.service.contract.NavigationItemService;
import de.dlh.lht.ti.service.contract.SubcontractPricingItemService;
import de.dlh.lht.ti.service.contract.UserPermissionService;
import de.dlh.lht.ti.service.contract.UserService;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;


import static de.dlh.lht.ti.utils.ErrorMessages.SAVE_SUBCONTRACT_PRICING_NOT_ALLOWED_ERROR_MESSAGE;
import static de.dlh.lht.ti.utils.ErrorMessages.SUBCONTRACT_PRICING_VALIDATION_ERROR_MESSAGE;
import static de.dlh.lht.ti.utils.ProgressMapperTestHelper.createTrueProgress;
import static de.dlh.lht.ti.utils.SubcontractPricingTestHelper.createEmptySubcontractInputsSubcontractPricingRequest;
import static de.dlh.lht.ti.utils.SubcontractPricingTestHelper.createEmptySubcontractsSubcontractPricing;
import static de.dlh.lht.ti.utils.SubcontractPricingTestHelper.createInvalidCapSubcontractInputsSubcontractPricingRequest;
import static de.dlh.lht.ti.utils.SubcontractPricingTestHelper.createInvalidMarginSubcontractInputsSubcontractPricingRequest;
import static de.dlh.lht.ti.utils.SubcontractPricingTestHelper.createNullSubcontractInputsSubcontractPricingRequest;
import static de.dlh.lht.ti.utils.SubcontractPricingTestHelper.createSubcontractDtoList;
import static de.dlh.lht.ti.utils.SubcontractPricingTestHelper.createSubcontractList;
import static de.dlh.lht.ti.utils.SubcontractPricingTestHelper.createSubcontractPricing;
import static de.dlh.lht.ti.utils.SubcontractPricingTestHelper.createSubcontractPricingItemByIdMap;
import static de.dlh.lht.ti.utils.SubcontractPricingTestHelper.createSubcontractPricingItemEntityList;
import static de.dlh.lht.ti.utils.SubcontractPricingTestHelper.createSubcontractPricingRequest;
import static de.dlh.lht.ti.utils.TestConstants.LONG_ID_1;
import static de.dlh.lht.ti.utils.TestConstants.TEST_UNUMBER;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class SubcontractPricingServiceImplTest {

    @Mock
    private SubcontractPricingMapper subcontractPricingMapperMock;

    @Mock
    private NavigationItemService navigationItemServiceMock;
    @Mock
    private SubcontractPricingItemService subcontractPricingItemServiceMock;
    @Mock
    private UserPermissionService userPermissionServiceMock;
    @Mock
    private UserService userServiceMock;

    @InjectMocks
    private SubcontractPricingServiceImpl subcontractPricingService;

    @Test
    void getSubcontractPricing_shouldReturnSubcontractPricing() {
        var progress = createTrueProgress();
        var subcontractDtos = createSubcontractDtoList();
        var subcontracts = createSubcontractList();
        var expectedResult = createSubcontractPricing();

        when(userServiceMock.getCurrentOwnerUNumberByQuotationId(anyLong())).thenReturn(TEST_UNUMBER);
        when(userPermissionServiceMock.isUserOwnerOrAdmin(anyString())).thenReturn(true);
        when(navigationItemServiceMock.getQuotationProgressByQuotationId(anyLong())).thenReturn(progress);
        when(subcontractPricingItemServiceMock.getAllSubcontractDtosByQuotationId(anyLong())).thenReturn(subcontractDtos);
        when(subcontractPricingMapperMock.subcontractDtoListToSubcontractList(anyList())).thenReturn(subcontracts);

        var result = subcontractPricingService.getSubcontractPricing(LONG_ID_1);

        assertEquals(expectedResult, result);

        verify(userServiceMock).getCurrentOwnerUNumberByQuotationId(anyLong());
        verify(userPermissionServiceMock).isUserOwnerOrAdmin(anyString());
        verify(navigationItemServiceMock).getQuotationProgressByQuotationId(anyLong());
        verify(subcontractPricingItemServiceMock).getAllSubcontractDtosByQuotationId(anyLong());
        verify(subcontractPricingMapperMock).subcontractDtoListToSubcontractList(anyList());
    }

    @Test
    void getSubcontractPricing_shouldReturnSubcontractPricingWithEmptySubcontract_onNullSubcontractDtoList() {
        var progress = createTrueProgress();
        var expectedResult = createEmptySubcontractsSubcontractPricing();

        when(userServiceMock.getCurrentOwnerUNumberByQuotationId(anyLong())).thenReturn(TEST_UNUMBER);
        when(userPermissionServiceMock.isUserOwnerOrAdmin(anyString())).thenReturn(true);
        when(navigationItemServiceMock.getQuotationProgressByQuotationId(anyLong())).thenReturn(progress);
        when(subcontractPricingItemServiceMock.getAllSubcontractDtosByQuotationId(anyLong())).thenReturn(null);

        var result = subcontractPricingService.getSubcontractPricing(LONG_ID_1);

        assertEquals(expectedResult, result);

        verify(userServiceMock).getCurrentOwnerUNumberByQuotationId(anyLong());
        verify(userPermissionServiceMock).isUserOwnerOrAdmin(anyString());
        verify(navigationItemServiceMock).getQuotationProgressByQuotationId(anyLong());
        verify(subcontractPricingItemServiceMock).getAllSubcontractDtosByQuotationId(anyLong());
        verify(subcontractPricingMapperMock, never()).subcontractDtoListToSubcontractList(anyList());
    }

    @Test
    void getSubcontractPricing_shouldReturnSubcontractPricingWithEmptySubcontract_onEmptySubcontractDtoList() {
        var progress = createTrueProgress();
        var expectedResult = createEmptySubcontractsSubcontractPricing();

        when(userServiceMock.getCurrentOwnerUNumberByQuotationId(anyLong())).thenReturn(TEST_UNUMBER);
        when(userPermissionServiceMock.isUserOwnerOrAdmin(anyString())).thenReturn(true);
        when(navigationItemServiceMock.getQuotationProgressByQuotationId(anyLong())).thenReturn(progress);
        when(subcontractPricingItemServiceMock.getAllSubcontractDtosByQuotationId(anyLong())).thenReturn(List.of());

        var result = subcontractPricingService.getSubcontractPricing(LONG_ID_1);

        assertEquals(expectedResult, result);

        verify(userServiceMock).getCurrentOwnerUNumberByQuotationId(anyLong());
        verify(userPermissionServiceMock).isUserOwnerOrAdmin(anyString());
        verify(navigationItemServiceMock).getQuotationProgressByQuotationId(anyLong());
        verify(subcontractPricingItemServiceMock).getAllSubcontractDtosByQuotationId(anyLong());
        verify(subcontractPricingMapperMock, never()).subcontractDtoListToSubcontractList(anyList());
    }

    @Test
    void saveSubcontractPricing_shouldThrow_onNullSubcontractPricingRequest() {
        var exception = assertThrows(ValidationException.class,
                () -> subcontractPricingService.saveSubcontractPricing(LONG_ID_1, null));

        assertEquals(SUBCONTRACT_PRICING_VALIDATION_ERROR_MESSAGE, exception.getMessage());
    }

    @Test
    void saveSubcontractPricing_shouldThrow_onNullSubcontractInputs() {
        var subcontractPricingRequest = createNullSubcontractInputsSubcontractPricingRequest();
        var exception = assertThrows(ValidationException.class,
                () -> subcontractPricingService.saveSubcontractPricing(LONG_ID_1, subcontractPricingRequest));

        assertEquals(SUBCONTRACT_PRICING_VALIDATION_ERROR_MESSAGE, exception.getMessage());
    }

    @Test
    void saveSubcontractPricing_shouldThrow_onInvalidCapSubcontractInputs() {
        var subcontractPricingRequest = createInvalidCapSubcontractInputsSubcontractPricingRequest();
        var exception = assertThrows(ValidationException.class,
                () -> subcontractPricingService.saveSubcontractPricing(LONG_ID_1, subcontractPricingRequest));

        assertEquals(SUBCONTRACT_PRICING_VALIDATION_ERROR_MESSAGE, exception.getMessage());
    }

    @Test
    void saveSubcontractPricing_shouldThrow_onInvalidMarginSubcontractInputs() {
        var subcontractPricingRequest = createInvalidMarginSubcontractInputsSubcontractPricingRequest();
        var exception = assertThrows(ValidationException.class,
                () -> subcontractPricingService.saveSubcontractPricing(LONG_ID_1, subcontractPricingRequest));

        assertEquals(SUBCONTRACT_PRICING_VALIDATION_ERROR_MESSAGE, exception.getMessage());
    }

    @Test
    void saveSubcontractPricing_shouldThrow_onUserNotAllowedToSave() {
        var subcontractPricingRequest = createSubcontractPricingRequest();

        when(userServiceMock.getCurrentOwnerUNumberByQuotationId(anyLong())).thenReturn(TEST_UNUMBER);
        when(userPermissionServiceMock.isUserOwnerOrAdmin(anyString())).thenReturn(false);

        var exception = assertThrows(SaveSubcontractPricingNotAllowedException.class,
                () -> subcontractPricingService.saveSubcontractPricing(LONG_ID_1, subcontractPricingRequest));

        assertEquals(String.format(SAVE_SUBCONTRACT_PRICING_NOT_ALLOWED_ERROR_MESSAGE, TEST_UNUMBER), exception.getMessage());
    }

    @Test
    void saveSubcontractPricing_shouldNotUpdate_onEmptySubcontractInputs() {
        var subcontractPricingRequest = createEmptySubcontractInputsSubcontractPricingRequest();
        var subcontractDto = createSubcontractDtoList();
        var subcontracts = createSubcontractList();
        var progress = createTrueProgress();

        when(userServiceMock.getCurrentOwnerUNumberByQuotationId(anyLong())).thenReturn(TEST_UNUMBER);
        when(userPermissionServiceMock.isUserOwnerOrAdmin(anyString())).thenReturn(true);
        when(subcontractPricingItemServiceMock.getAllSubcontractDtosByQuotationId(anyLong())).thenReturn(subcontractDto);
        when(subcontractPricingMapperMock.subcontractDtoListToSubcontractList(anyList())).thenReturn(subcontracts);
        when(navigationItemServiceMock.getQuotationProgressByQuotationId(anyLong())).thenReturn(progress);

        subcontractPricingService.saveSubcontractPricing(LONG_ID_1, subcontractPricingRequest);

        verify(userServiceMock).getCurrentOwnerUNumberByQuotationId(anyLong());
        verify(userPermissionServiceMock).isUserOwnerOrAdmin(anyString());
        verify(subcontractPricingItemServiceMock).getAllSubcontractDtosByQuotationId(anyLong());
        verify(subcontractPricingMapperMock).subcontractDtoListToSubcontractList(anyList());
        verify(subcontractPricingItemServiceMock, never()).getSubcontractPricingItemsByIdMapByQuotationId(anyLong());
        verify(subcontractPricingMapperMock, never()).subcontractInputToSubcontractPricingItemEntity(
                any(SubcontractInput.class),
                any(SubcontractPricingItemEntity.class));
        verify(subcontractPricingItemServiceMock, never()).updateSubcontractPricingItems(anyList());
        verify(navigationItemServiceMock).updateProgress(anyLong(), any(QuotationProgress.class), anyBoolean());
        verify(navigationItemServiceMock).getQuotationProgressByQuotationId(anyLong());
    }

    @Test
    void saveSubcontractPricing_shouldUpdate_onValidSubcontractInputs() {
        var subcontractPricingRequest = createSubcontractPricingRequest();
        var subcontractPricingItemByIdMap = createSubcontractPricingItemByIdMap(createSubcontractPricingItemEntityList());
        var subcontractDto = createSubcontractDtoList();
        var subcontracts = createSubcontractList();
        var progress = createTrueProgress();

        when(userServiceMock.getCurrentOwnerUNumberByQuotationId(anyLong())).thenReturn(TEST_UNUMBER);
        when(userPermissionServiceMock.isUserOwnerOrAdmin(anyString())).thenReturn(true);
        when(subcontractPricingItemServiceMock.getSubcontractPricingItemsByIdMapByQuotationId(anyLong()))
                .thenReturn(subcontractPricingItemByIdMap);
        when(subcontractPricingItemServiceMock.getAllSubcontractDtosByQuotationId(anyLong())).thenReturn(subcontractDto);
        when(subcontractPricingMapperMock.subcontractDtoListToSubcontractList(anyList())).thenReturn(subcontracts);
        when(navigationItemServiceMock.getQuotationProgressByQuotationId(anyLong())).thenReturn(progress);

        subcontractPricingService.saveSubcontractPricing(LONG_ID_1, subcontractPricingRequest);

        verify(userServiceMock).getCurrentOwnerUNumberByQuotationId(anyLong());
        verify(userPermissionServiceMock).isUserOwnerOrAdmin(anyString());
        verify(subcontractPricingItemServiceMock).getSubcontractPricingItemsByIdMapByQuotationId(anyLong());
        verify(subcontractPricingMapperMock).subcontractInputToSubcontractPricingItemEntity(
                any(SubcontractInput.class),
                any(SubcontractPricingItemEntity.class));
        verify(subcontractPricingItemServiceMock).updateSubcontractPricingItems(anyList());
        verify(subcontractPricingItemServiceMock).getAllSubcontractDtosByQuotationId(anyLong());
        verify(subcontractPricingMapperMock).subcontractDtoListToSubcontractList(anyList());
        verify(navigationItemServiceMock).updateProgress(anyLong(), any(QuotationProgress.class), anyBoolean());
        verify(navigationItemServiceMock).getQuotationProgressByQuotationId(anyLong());
    }
}
