package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.entity.EscalationPricingEntity;
import de.dlh.lht.ti.exception.SaveEscalationPricingNotAllowedException;
import de.dlh.lht.ti.exception.ValidationException;
import de.dlh.lht.ti.mapper.EscalationMapper;
import de.dlh.lht.ti.model.EscalationsPricing;
import de.dlh.lht.ti.model.Progress;
import de.dlh.lht.ti.repository.EscalationPricingRepository;
import de.dlh.lht.ti.service.contract.*;
import de.dlh.lht.ti.utils.EngineServiceImplTestHelper;
import de.dlh.lht.ti.utils.QuotationServiceImplTestHelper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;

import static de.dlh.lht.ti.utils.EscalationPricingServiceImplTestHelper.*;
import static de.dlh.lht.ti.utils.EscalationPricingServiceImplTestHelper.createEscalationPricingDto;
import static de.dlh.lht.ti.utils.TestConstants.LONG_ID_1;
import static de.dlh.lht.ti.utils.TestConstants.TEST_STRING;
import static org.junit.jupiter.api.Assertions.assertEquals;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;


@ExtendWith(MockitoExtension.class)
class EscalationPricingServiceImplTest {
    @Mock
    private EscalationPricingRepository escalationPricingRepositoryMock;

    @Mock
    private EscalationMapper escalationMapperMock;

    @Mock
    private EngineService engineServiceMock;

    @Mock
    private EscalationPricingDefaultsService escalationPricingDefaultsServiceMock;

    @Mock
    private NavigationItemService navigationItemServiceMock;

    @Mock
    private UserPermissionService userPermissionServiceMock;

    @Mock
    private UserService userServiceMock;

    @Mock
    private QuotationService quotationServiceMock;

    @InjectMocks
    private EscalationPricingServiceImpl escalationPricingService;

    @Test
    void getEscalationPricing_whenEscalationPricingExists_shouldRetrieveAndSetDefaultValues() {
        // Arrange
        var escalationPriceDtoList = createEscalationPricingDtoList();
        var expectedEscalationList = createEscalationList();
        var expectedProgress = new Progress();
        var expectedResult = new EscalationsPricing()
                .progress(expectedProgress)
                .isRfpContractSelected(true)
                .canCurrentUserEdit(true)
                .escalationsPricing(expectedEscalationList);
        when(quotationServiceMock.isRfpContractSelected(anyLong())).thenReturn(true);
        when(userServiceMock.getCurrentOwnerUNumberByQuotationId(LONG_ID_1)).thenReturn(TEST_STRING);
        when(userPermissionServiceMock.isUserOwnerOrAdmin(anyString())).thenReturn(true);
        when(navigationItemServiceMock.getQuotationProgressByQuotationId(anyLong())).thenReturn(expectedProgress);
        when(escalationPricingRepositoryMock.findAllWithMaterialDefaultsByQuotationId(LONG_ID_1, true)).thenReturn(escalationPriceDtoList);
        when(escalationMapperMock.escalationPricingDtoListToEscalationList(escalationPriceDtoList)).thenReturn(expectedEscalationList);

        // Act
        var result = escalationPricingService.getEscalationPricing(LONG_ID_1);

        // Assert
        assertEquals(expectedResult, result);

        verify(userServiceMock).getCurrentOwnerUNumberByQuotationId(LONG_ID_1);
        verify(userPermissionServiceMock).isUserOwnerOrAdmin(anyString());
        verify(navigationItemServiceMock).getQuotationProgressByQuotationId(LONG_ID_1);
        verify(escalationPricingRepositoryMock).findAllWithMaterialDefaultsByQuotationId(LONG_ID_1, true);
        verify(escalationMapperMock).escalationPricingDtoListToEscalationList(escalationPriceDtoList);
    }

    @Test
    void getEscalationPricing_whenEscalationPricingDoesNotExists_shouldCreateDefaultEscalationPricing() {
        // Arrange
        var expectedEscalationPricingEntityList = createEscalationPricingEntityList();
        var expectedEscalationList = createEscalationList();
        var expectedProgress = new Progress();
        var expectedResult = new EscalationsPricing()
                .progress(expectedProgress)
                .isRfpContractSelected(true)
                .canCurrentUserEdit(true)
                .escalationsPricing(expectedEscalationList);

        when(quotationServiceMock.isRfpContractSelected(anyLong())).thenReturn(true);
        when(quotationServiceMock.getQuotationEntityById(anyLong())).thenReturn(QuotationServiceImplTestHelper.createQuotationEntity());
        when(engineServiceMock.getEngineByQuotationId(anyLong())).thenReturn(EngineServiceImplTestHelper.createEngineEntity());
        when(userServiceMock.getCurrentOwnerUNumberByQuotationId(LONG_ID_1)).thenReturn(TEST_STRING);
        when(userPermissionServiceMock.isUserOwnerOrAdmin(anyString())).thenReturn(true);
        when(navigationItemServiceMock.getQuotationProgressByQuotationId(anyLong())).thenReturn(expectedProgress);
        when(escalationPricingRepositoryMock.findAllWithMaterialDefaultsByQuotationId(LONG_ID_1, true)).thenReturn(Collections.emptyList());
        when(escalationPricingRepositoryMock.saveAll(anyList())).thenReturn(expectedEscalationPricingEntityList);
        when(escalationPricingDefaultsServiceMock.mapEntityToDtoWithMaterialPricesDefaultValue(any(EscalationPricingEntity.class), anyLong())).thenReturn(createEscalationPricingDto());
        when(escalationMapperMock.escalationPricingDtoListToEscalationList(anyList())).thenReturn(expectedEscalationList);

        // Act
        var result = escalationPricingService.getEscalationPricing(LONG_ID_1);

        // Assert
        assertEquals(expectedResult, result);

        verify(userServiceMock).getCurrentOwnerUNumberByQuotationId(LONG_ID_1);
        verify(userPermissionServiceMock).isUserOwnerOrAdmin(anyString());
        verify(navigationItemServiceMock).getQuotationProgressByQuotationId(LONG_ID_1);
        verify(escalationPricingRepositoryMock).findAllWithMaterialDefaultsByQuotationId(LONG_ID_1, true);
        verify(escalationPricingRepositoryMock).saveAll(anyList());
    }

    @Test
    void saveEscalationPricing_whenUserNotOwnerOrAdmin_shouldThrowException() {
        // Arrange
        var escalationPricingRequest = createEscalationPricingRequest();

        when(quotationServiceMock.isRfpContractSelected(anyLong())).thenReturn(true);
        when(userServiceMock.getCurrentOwnerUNumberByQuotationId(LONG_ID_1)).thenReturn(TEST_STRING);
        when(userPermissionServiceMock.isUserOwnerOrAdmin(anyString())).thenReturn(false);

        // Act and Assert
        assertThrows(SaveEscalationPricingNotAllowedException.class, () -> escalationPricingService.saveEscalationPricing(LONG_ID_1, escalationPricingRequest));

        verify(userServiceMock).getCurrentOwnerUNumberByQuotationId(LONG_ID_1);
        verify(userPermissionServiceMock).isUserOwnerOrAdmin(anyString());
    }

    @Test
    void saveEscalationPricing_whenUserCanEditAndInputsAreValid_shouldSaveAndReturnEscalationPricing() {
        // Arrange
        var escalationPriceDtoList = createEscalationPricingDtoList();
        var escalationPricingRequest = createEscalationPricingRequest();
        var expectedEscalationList = createEscalationList();
        var expectedProgress = new Progress();
        var expectedResult = new EscalationsPricing()
                .progress(expectedProgress)
                .isRfpContractSelected(true)
                .canCurrentUserEdit(true)
                .escalationsPricing(expectedEscalationList);

        when(quotationServiceMock.isRfpContractSelected(anyLong())).thenReturn(true);
        when(userServiceMock.getCurrentOwnerUNumberByQuotationId(LONG_ID_1)).thenReturn(TEST_STRING);
        when(userPermissionServiceMock.isUserOwnerOrAdmin(anyString())).thenReturn(true);
        when(navigationItemServiceMock.getQuotationProgressByQuotationId(anyLong())).thenReturn(expectedProgress);
        when(escalationPricingRepositoryMock.findAllWithMaterialDefaultsByQuotationId(LONG_ID_1, true)).thenReturn(escalationPriceDtoList);
        when(escalationPricingRepositoryMock.findAllByQuotationId(LONG_ID_1)).thenReturn(createEscalationPricingEntityList());
        when(navigationItemServiceMock.getQuotationProgressByQuotationId(anyLong())).thenReturn(expectedProgress);
        when(escalationMapperMock.escalationPricingDtoListToEscalationList(anyList())).thenReturn(expectedEscalationList);

        // Act
        var result = escalationPricingService.saveEscalationPricing(LONG_ID_1, escalationPricingRequest);

        // Assert
        assertEquals(expectedResult, result);

        verify(userServiceMock).getCurrentOwnerUNumberByQuotationId(LONG_ID_1);
        verify(userPermissionServiceMock).isUserOwnerOrAdmin(anyString());
        verify(escalationPricingRepositoryMock).findAllByQuotationId(LONG_ID_1);
        verify(navigationItemServiceMock).getQuotationProgressByQuotationId(anyLong());
        verify(escalationPricingRepositoryMock).saveAll(anyList());
    }

    @Test
    void saveEscalationPricing_whenInputsAreNotNegative_shouldThrow(){
        var escalationPricingRequest = createInvalidNegativeEscalationPricingRequest();

        assertThrows(ValidationException.class, () -> escalationPricingService.saveEscalationPricing(LONG_ID_1, escalationPricingRequest));
    }

    @Test
    void saveEscalationPricing_whenInputsAreOverAHundred_shouldThrow(){
        var escalationPricingRequest = createInvalidEscalationPricingRequest();

        assertThrows(ValidationException.class, () -> escalationPricingService.saveEscalationPricing(LONG_ID_1, escalationPricingRequest));
    }

}

