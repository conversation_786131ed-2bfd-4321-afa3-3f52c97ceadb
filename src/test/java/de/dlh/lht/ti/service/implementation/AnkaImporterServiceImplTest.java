package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.service.contract.CustomerService;
import de.dlh.lht.ti.service.contract.EngineClusterPartService;
import de.dlh.lht.ti.service.contract.EngineService;
import de.dlh.lht.ti.service.contract.LabourPricingItemService;
import de.dlh.lht.ti.service.contract.MaterialPricingItemService;
import de.dlh.lht.ti.service.contract.PartMetadataService;
import de.dlh.lht.ti.service.contract.PartService;
import de.dlh.lht.ti.service.contract.ProjectService;
import de.dlh.lht.ti.service.contract.QuotationEngineService;
import de.dlh.lht.ti.service.contract.QuotationService;
import de.dlh.lht.ti.service.contract.TaskMetadataService;
import de.dlh.lht.ti.service.contract.UserService;
import de.dlh.lht.ti.service.contract.WorkscopeService;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class AnkaImporterServiceImplTest {

    @Mock
    private CustomerService customerServiceMock;
    @Mock
    private EngineService engineServiceMock;
    @Mock
    private EngineClusterPartService engineClusterPartServiceMock;
    @Mock
    private LabourPricingItemService labourPricingItemServiceMock;
    @Mock
    private MaterialPricingItemService materialPricingItemServiceMock;
    @Mock
    private PartService partServiceMock;
    @Mock
    private PartMetadataService partMetadataServiceMock;
    @Mock
    private ProjectService projectServiceMock;
    @Mock
    private QuotationService quotationServiceMock;
    @Mock
    private QuotationEngineService quotationEngineService;
    @Mock
    private SubcontractMetadataServiceImpl subcontractMetadataServiceMock;
    @Mock
    private SubcontractPricingItemServiceImpl subcontractPricingItemServiceMock;
    @Mock
    private TaskMetadataService taskMetadataServiceMock;
    @Mock
    private UserService userServiceMock;
    @Mock
    private WorkscopeService workscopeService;
}
