package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.auth.UserPrincipal;
import de.dlh.lht.ti.auth.contract.UserPrincipalProvider;
import de.dlh.lht.ti.auth.roles.Role;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;


import static de.dlh.lht.ti.utils.TestConstants.TEST_STRING;
import static de.dlh.lht.ti.utils.TestConstants.TEST_STRING_2;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class UserPermissionImplTest {
    @Mock
    private UserPrincipalProvider userPrincipalProviderMock;

    @InjectMocks
    private UserPermissionServiceImpl userPermissionService;

    @Test
    void isUserOwnerOrAdmin_WhenOwner() {
        when(userPrincipalProviderMock.getUserPrincipal()).thenReturn(new UserPrincipal(TEST_STRING, TEST_STRING, TEST_STRING));

        var result = userPermissionService.isUserOwnerOrAdmin(TEST_STRING);

        Assertions.assertTrue(result);
    }

    @Test
    void isUserOwnerOrAdmin_WhenUserIsAdmin() {
        when(userPrincipalProviderMock.getRoles()).thenReturn(List.of(Role.ADMIN_USER));
        when(userPrincipalProviderMock.getUserPrincipal()).thenReturn(new UserPrincipal(TEST_STRING_2, TEST_STRING_2, TEST_STRING));

        var result = userPermissionService.isUserOwnerOrAdmin(TEST_STRING);

        Assertions.assertTrue(result);
    }

    @Test
    void isUserOwnerOrAdmin_WhenNone() {
        when(userPrincipalProviderMock.getUserPrincipal()).thenReturn(new UserPrincipal(TEST_STRING_2, TEST_STRING_2, TEST_STRING));
        when(userPrincipalProviderMock.getRoles()).thenReturn(List.of(Role.VIEW_ONLY_USER));

        var result = userPermissionService.isUserOwnerOrAdmin(TEST_STRING);

        Assertions.assertFalse(result);
    }


}
