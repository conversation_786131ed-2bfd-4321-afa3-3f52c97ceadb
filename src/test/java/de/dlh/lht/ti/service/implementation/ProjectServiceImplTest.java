package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.entity.ProjectEntity;
import de.dlh.lht.ti.exception.ChangeOwnerNotAllowedException;
import de.dlh.lht.ti.exception.EntityNotFoundException;
import de.dlh.lht.ti.exception.OfferNumberValidationException;
import de.dlh.lht.ti.repository.ProjectRepository;
import de.dlh.lht.ti.service.contract.UserPermissionService;
import de.dlh.lht.ti.service.contract.UserService;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;


import static de.dlh.lht.ti.utils.ErrorMessages.PROJECT_ENTITY_WITH_OFFER_NUMBER_NOT_FOUND_ERROR_MESSAGE;
import static de.dlh.lht.ti.utils.ProjectServiceImplTestHelper.createProjectEntity;
import static de.dlh.lht.ti.utils.ProjectServiceImplTestHelper.createProjectEntityList;
import static de.dlh.lht.ti.utils.TestConstants.LONG_ID_1;
import static de.dlh.lht.ti.utils.TestConstants.TEST_OFFER_NUMBER;
import static de.dlh.lht.ti.utils.UserServiceImplTestHelper.createUserEntity;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ProjectServiceImplTest {

    @Captor
    private ArgumentCaptor<ProjectEntity> projectEntityArgumentCaptor;

    @Mock
    private ProjectRepository projectRepositoryMock;

    @Mock
    private UserService userServiceMock;

    @Mock
    private UserPermissionService userPermissionServiceMock;
    @InjectMocks
    private ProjectServiceImpl projectService;

    @Test
    void changeOwner_throwExceptionWhenOfferNumberIsInvalid() {
        assertThrows(OfferNumberValidationException.class,
                () -> projectService.changeOwner("test", LONG_ID_1));
    }

    @Test
    void changeOwner_shouldChangeProjectsCurrentOwner() {
        var user = createUserEntity();
        var project = createProjectEntity();

        when(projectRepositoryMock.findByOfferNumber(anyString())).thenReturn(Optional.of(project));
        when(userPermissionServiceMock.isUserOwnerOrAdmin(anyString())).thenReturn(true);
        when(userServiceMock.getUserById(anyLong())).thenReturn(user);

        projectService.changeOwner(TEST_OFFER_NUMBER, LONG_ID_1);

        verify(projectRepositoryMock).findByOfferNumber(anyString());
        verify(projectRepositoryMock).save(projectEntityArgumentCaptor.capture());

        var savedProject = projectEntityArgumentCaptor.getValue();

        assertEquals(user.getId(), savedProject.getCurrentOwner().getId());
    }

    @Test
    void changeOwner_shouldThrow_whenProjectWithOfferNumberNotFound() {
        when(projectRepositoryMock.findByOfferNumber(anyString())).thenReturn(Optional.empty());

        var exception = assertThrows(EntityNotFoundException.class,
                () -> projectService.changeOwner(TEST_OFFER_NUMBER, LONG_ID_1));

        assertEquals(String.format(PROJECT_ENTITY_WITH_OFFER_NUMBER_NOT_FOUND_ERROR_MESSAGE, TEST_OFFER_NUMBER), exception.getMessage());

        verify(projectRepositoryMock).findByOfferNumber(anyString());
        verify(projectRepositoryMock, never()).save(any(ProjectEntity.class));
    }

    @Test
    void changeOwner_shouldThrow_whenUserIsNotOwner() {
        when(projectRepositoryMock.findByOfferNumber(anyString())).thenReturn(Optional.of(createProjectEntity()));
        when(userPermissionServiceMock.isUserOwnerOrAdmin(anyString())).thenReturn(false);

        assertThrows(ChangeOwnerNotAllowedException.class,
                () -> projectService.changeOwner(TEST_OFFER_NUMBER, LONG_ID_1));

        verify(projectRepositoryMock).findByOfferNumber(anyString());
        verify(projectRepositoryMock, never()).save(any(ProjectEntity.class));
        verify(userServiceMock, never()).getUserById(anyLong());
    }

    @Test
    void createProject_shouldSucceed() {
        var user = createUserEntity();
        projectService.createProject(TEST_OFFER_NUMBER, user);

        verify(projectRepositoryMock).save(projectEntityArgumentCaptor.capture());

        var saveProject = projectEntityArgumentCaptor.getValue();

        assertEquals(TEST_OFFER_NUMBER, saveProject.getOfferNumber());
        assertEquals(LONG_ID_1, saveProject.getOriginalOwner().getId());
        assertEquals(LONG_ID_1, saveProject.getCurrentOwner().getId());
    }

    @Test
    void getProjectDtoProjectEntityMap_shouldGetAllTheProjectsAndMapThemIntoMap() {
        var projects = createProjectEntityList();

        when(projectRepositoryMock.findAll()).thenReturn(projects);

        projectService.getProjectEntityByOfferNumberMap();

        verify(projectRepositoryMock).findAll();
    }
}
