package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.dto.RfpEngineMetadataDto;
import de.dlh.lht.ti.dto.RfpQuotationDto;
import de.dlh.lht.ti.enums.Currency;
import de.dlh.lht.ti.enums.TaskType;
import de.dlh.lht.ti.mapper.RfpEngineItemMapper;
import de.dlh.lht.ti.model.RfpItem;
import de.dlh.lht.ti.repository.RfpEngineItemRepository;
import de.dlh.lht.ti.service.contract.EparPriceService;
import de.dlh.lht.ti.service.contract.QuotationService;
import de.dlh.lht.ti.service.contract.TaskMetadataService;
import de.dlh.lht.ti.service.contract.TestrunItemService;
import de.dlh.lht.ti.service.contract.WorkscopeService;
import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;


import static de.dlh.lht.ti.utils.TestConstants.LONG_ID_1;
import static de.dlh.lht.ti.utils.TestConstants.LONG_ID_2;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class RfpEngineServiceImplTest {

    @Mock
    private RfpEngineItemMapper rfpEngineItemMapper;

    @Mock
    private EparPriceService eparPriceService;
    @Mock
    private RfpEngineItemRepository rfpEngineItemRepository;
    @Mock
    private TaskMetadataService taskMetadataService;
    @Mock
    private TestrunItemService testrunItemServiceMock;
    @Mock
    private QuotationService quotationService;
    @Mock
    private WorkscopeService workscopeService;

    @InjectMocks
    private RfpEngineServiceImpl labourRfpEngineService;

    @Test
    void getRfpEngineData_returnsListOfRfpEngineItems() {
        // Arrange
        var quotationId = LONG_ID_1;
        var usdExchangeRate = BigDecimal.valueOf(1.2);
        var quotationDto = new RfpQuotationDto(LONG_ID_1, "engineTestVersion", false, false, ZonedDateTime.now(), usdExchangeRate);
        when(quotationService.getRfpQuotationDtoById(quotationId)).thenReturn(quotationDto);

        var workscopeId = LONG_ID_2;
        when(workscopeService.getDeepestWorkscopeIdByQuotationId(quotationId)).thenReturn(workscopeId);

        var year = "2023";
        var taskMetadata = List.of(new RfpEngineMetadataDto(
                LONG_ID_1,
                "test",
                "test",
                BigDecimal.valueOf(1.2),
                1,
                BigDecimal.valueOf(1.2),
                BigDecimal.valueOf(1.2),
                Currency.EUR,
                false));
        when(taskMetadataService.findAllByQuotationIdWorkscopeIdYearAndTaskType(
                quotationId,
                TaskType.RFP_ENGINE,
                workscopeId,
                year)).thenReturn(taskMetadata);

        when(testrunItemServiceMock.getRfpItemByQuotationIdYearAndWorkscopeId(
                anyLong(),
                anyString(),
                anyLong(),
                any(BigDecimal.class)))
                .thenReturn(new RfpItem());

        // Act
        var result = labourRfpEngineService.getRfpEngineData(quotationId);

        // Assert
        assertNotNull(result);
        verify(quotationService).getRfpQuotationDtoById(quotationId);
        verify(workscopeService).getDeepestWorkscopeIdByQuotationId(quotationId);
        verify(taskMetadataService).findAllByQuotationIdWorkscopeIdYearAndTaskType(
                quotationId,
                TaskType.RFP_ENGINE,
                workscopeId,
                year);
    }
}