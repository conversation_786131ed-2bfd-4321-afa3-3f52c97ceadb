package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.repository.EngineClusterPartRepository;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;


import static de.dlh.lht.ti.utils.EngineClusterPartServiceImplTestHelper.createEngineClusterPartDtoList;
import static de.dlh.lht.ti.utils.EngineClusterPartServiceImplTestHelper.createExpectedResultMap;
import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(MockitoExtension.class)
public class EngineClusterPartServiceImplTest {

    @Mock
    EngineClusterPartRepository engineClusterPartRepositoryMock;

    @InjectMocks
    private EngineClusterPartServiceImpl engineClusterPartService;

    @Test
    void getClusterIdByPartIdByEngineIdMap_shouldReturnCorrectMap() {
        var engineClusterPartDtos = createEngineClusterPartDtoList();
        var expectedResultMap = createExpectedResultMap();

        Mockito.when(engineClusterPartRepositoryMock.findAllEngineClusterPartDtos()).thenReturn(engineClusterPartDtos);

        var result = engineClusterPartService.getClusterIdByPartIdByEngineIdMap();

        assertEquals(expectedResultMap, result);
    }
}
