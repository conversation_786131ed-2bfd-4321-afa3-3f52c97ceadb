package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.dto.ClusterTaskDto;
import de.dlh.lht.ti.entity.ClusterEntity;
import de.dlh.lht.ti.repository.ClusterRepository;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;


import static de.dlh.lht.ti.utils.TestConstants.LONG_ID_1;
import static de.dlh.lht.ti.utils.TestConstants.LONG_ID_2;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ClusterServiceImplTest {

    @Mock
    private ClusterRepository clusterRepository;

    @InjectMocks
    private ClusterServiceImpl clusterService;

    @Test
    void testGetClusterTasksPerEngine() {
        // arrange
        var engineId = LONG_ID_1;
        var taskNameFirst = "Task 1";
        var taskNameSecond = "Task 2";
        var task1 = new ClusterTaskDto(LONG_ID_1, taskNameFirst);
        var task2 = new ClusterTaskDto(LONG_ID_2, taskNameSecond);
        var clusterTasks = List.of(task1, task2);

        when(clusterRepository.findAllClusterTasksByEngineId(engineId)).thenReturn(clusterTasks);

        // act
        var result = clusterService.getClusterTaskDtoByEngineNameMap(engineId);

        // assert
        assertEquals(2, result.size());
        assertEquals(task1, result.get(taskNameFirst));
        assertEquals(task2, result.get(taskNameSecond));
    }

    @Test
    void testCreateDynamicQuotationTasks() {
        // arrange
        var rawTasks = List.of("Task 1", "Task 2", "Task 3");
        var existingTasks = List.of(
                new ClusterEntity("Task 2"),
                new ClusterEntity("Task 3")
        );
        var newTasks = List.of(new ClusterEntity("Task 1"));

        // Mocking repository behavior
        when(clusterRepository.findAllByNameIn(anyList())).thenReturn(existingTasks);
        when(clusterRepository.saveAll(anyList())).thenReturn(newTasks);

        //act
        clusterService.createClusters(rawTasks);

        // assert
        verify(clusterRepository).findAllByNameIn(anyList());
        verify(clusterRepository).saveAll(anyList());
    }
}