package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.service.contract.CustomerService;
import de.dlh.lht.ti.service.contract.EngineService;
import de.dlh.lht.ti.service.contract.QuotationService;
import de.dlh.lht.ti.service.contract.UserService;
import java.util.Collections;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;


import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class FiltersServiceTest {

    @Mock
    private CustomerService customerServiceMock;

    @Mock
    private EngineService engineServiceMock;

    @Mock
    private QuotationService quotationServiceMock;

    @Mock
    private UserService userServiceMock;

    @InjectMocks
    private FiltersServiceImpl filtersService;

    @Test
    void getQuotationFilters_NotNull() {
        //arrange
        when(engineServiceMock.getEnginesAsQuotationFilters()).thenReturn(Collections.emptyList());
        when(customerServiceMock.getCustomersAsQuotationFilters()).thenReturn(Collections.emptyList());
        when(userServiceMock.getAllQuotationOwnerUsers()).thenReturn(Collections.emptyList());
        //act
        var quotationFilters = filtersService.getQuotationFilters();
        //assert
        assertNotNull(quotationFilters);
    }
}
