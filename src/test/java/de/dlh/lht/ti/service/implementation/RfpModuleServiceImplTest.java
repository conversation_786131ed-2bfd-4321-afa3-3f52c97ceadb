package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.entity.RfpModuleEntity;
import de.dlh.lht.ti.exception.ValidationException;
import de.dlh.lht.ti.mapper.RfpModuleMapper;
import de.dlh.lht.ti.model.RfpItemInput;
import de.dlh.lht.ti.model.RfpModuleData;
import de.dlh.lht.ti.repository.RfpModuleRepository;
import de.dlh.lht.ti.service.contract.CleaningAndInspectionService;
import de.dlh.lht.ti.service.contract.EparPriceService;
import de.dlh.lht.ti.service.contract.LabourPricingItemService;
import de.dlh.lht.ti.service.contract.QuotationService;
import de.dlh.lht.ti.service.contract.WorkscopeService;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;


import static de.dlh.lht.ti.utils.ErrorMessages.RFP_MODULE_VALIDATION_ERROR_MESSAGE;
import static de.dlh.lht.ti.utils.LabourPricingServiceImplTestHelper.createInvalidRfpRequest;
import static de.dlh.lht.ti.utils.LabourPricingServiceImplTestHelper.createRfpRequest;
import static de.dlh.lht.ti.utils.QuotationServiceImplTestHelper.createRfpQuotationDto;
import static de.dlh.lht.ti.utils.RfpModuleServiceImplTestHelper.createRfpModuleDataDtoList;
import static de.dlh.lht.ti.utils.RfpModuleServiceImplTestHelper.createRfpModuleEntityList;
import static de.dlh.lht.ti.utils.TestConstants.LONG_ID_1;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class RfpModuleServiceImplTest {

    @Mock
    private RfpModuleMapper rfpModuleMapperMock;

    @Mock
    private RfpModuleRepository rfpModuleRepositoryMock;

    @Mock
    private CleaningAndInspectionService cleaningAndInspectionServiceMock;
    @Mock
    private EparPriceService eparPriceServiceMock;
    @Mock
    private LabourPricingItemService labourPricingItemServiceMock;
    @Mock
    private QuotationService quotationServiceMock;
    @Mock
    private WorkscopeService workscopeServiceMock;

    @InjectMocks
    private RfpModuleServiceImpl rfpModuleService;

    @Test
    void getRfpModuleData_shouldCreateRfpModuleEntitiesAndReturnRfpModuleData_whenEntitiesDoNotExist() {
        var rfpQuotationDto = createRfpQuotationDto();
        var ids = List.of(1L, 2L);

        when(quotationServiceMock.getRfpQuotationDtoById(anyLong())).thenReturn(rfpQuotationDto);
        when(rfpModuleRepositoryMock.findAllDtosByQuotationIdAndBaseYear(anyLong(), anyString())).thenReturn(new ArrayList<>());
        when(labourPricingItemServiceMock.getAllRfpModuleLabourPricingItemIdsByQuotationId(anyLong())).thenReturn(ids);
        when(workscopeServiceMock.getSystemWorkscopeIdsByQuotationId(anyLong())).thenReturn(ids);
        when(rfpModuleMapperMock.rfpModuleDataDtoListToRfpModule(anyList(), any(BigDecimal.class)))
                .thenReturn(new RfpModuleData());

        rfpModuleService.getRfpModuleData(LONG_ID_1);

        verify(quotationServiceMock).getRfpQuotationDtoById(anyLong());
        verify(rfpModuleRepositoryMock, times(2)).findAllDtosByQuotationIdAndBaseYear(anyLong(), anyString());
        verify(labourPricingItemServiceMock).getAllRfpModuleLabourPricingItemIdsByQuotationId(anyLong());
        verify(workscopeServiceMock).getSystemWorkscopeIdsByQuotationId(anyLong());
        verify(rfpModuleRepositoryMock).saveAll(anyList());
    }

    @Test
    void getRfpModuleData_shouldReturnRfpModuleData_whenEntitiesExist() {
        var rfpQuotationDto = createRfpQuotationDto();
        var rfpModuleDtoList = createRfpModuleDataDtoList();

        when(quotationServiceMock.getRfpQuotationDtoById(anyLong())).thenReturn(rfpQuotationDto);
        when(rfpModuleRepositoryMock.findAllDtosByQuotationIdAndBaseYear(anyLong(), anyString())).thenReturn(rfpModuleDtoList);
        when(rfpModuleMapperMock.rfpModuleDataDtoListToRfpModule(anyList(), any(BigDecimal.class)))
                .thenReturn(new RfpModuleData());

        rfpModuleService.getRfpModuleData(LONG_ID_1);

        verify(quotationServiceMock).getRfpQuotationDtoById(anyLong());
        verify(rfpModuleRepositoryMock).findAllDtosByQuotationIdAndBaseYear(anyLong(), anyString());
        verify(labourPricingItemServiceMock, never()).getAllRfpModuleLabourPricingItemIdsByQuotationId(anyLong());
        verify(workscopeServiceMock, never()).getSystemWorkscopeIdsByQuotationId(anyLong());
        verify(rfpModuleRepositoryMock, never()).saveAll(anyList());
    }

    @Test
    void saveRfpModuleData_shouldSaveRfpModuleEntities_onValidInput() {
        var rfpRequest = createRfpRequest();
        var rfpQuotationDto = createRfpQuotationDto();
        var rfpModuleDtoList = createRfpModuleDataDtoList();
        var rfpModuleEntityList = createRfpModuleEntityList();

        when(rfpModuleRepositoryMock.findAllByQuotationId(anyLong())).thenReturn(rfpModuleEntityList);
        when(quotationServiceMock.getRfpQuotationDtoById(anyLong())).thenReturn(rfpQuotationDto);
        when(rfpModuleRepositoryMock.findAllDtosByQuotationIdAndBaseYear(anyLong(), anyString())).thenReturn(rfpModuleDtoList);
        when(rfpModuleMapperMock.rfpModuleDataDtoListToRfpModule(anyList(), any(BigDecimal.class)))
                .thenReturn(new RfpModuleData());

        rfpModuleService.saveRfpModuleData(LONG_ID_1, rfpRequest);

        verify(rfpModuleRepositoryMock).findAllByQuotationId(anyLong());
        verify(rfpModuleMapperMock, times(2)).rfpItemInputToRfpModuleEntity(any(RfpItemInput.class), any(RfpModuleEntity.class));
        verify(rfpModuleRepositoryMock).saveAll(anyList());
        verify(quotationServiceMock).getRfpQuotationDtoById(anyLong());
        verify(rfpModuleRepositoryMock).findAllDtosByQuotationIdAndBaseYear(anyLong(), anyString());
        verify(labourPricingItemServiceMock, never()).getAllRfpModuleLabourPricingItemIdsByQuotationId(anyLong());
        verify(workscopeServiceMock, never()).getSystemWorkscopeIdsByQuotationId(anyLong());
    }

    @Test
    void saveRfpModuleData_shouldThrow_onInvalidInput() {
        var rfpRequest = createInvalidRfpRequest();

        var result = assertThrows(ValidationException.class, () -> rfpModuleService.saveRfpModuleData(LONG_ID_1, rfpRequest));

        assertEquals(RFP_MODULE_VALIDATION_ERROR_MESSAGE, result.getMessage());
    }
}
