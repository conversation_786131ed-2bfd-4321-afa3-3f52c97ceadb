package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.entity.NavigationItemEntity;
import de.dlh.lht.ti.enums.QuotationProgress;
import de.dlh.lht.ti.exception.EntityNotFoundException;
import de.dlh.lht.ti.mapper.ProgressMapper;
import de.dlh.lht.ti.repository.NavigationItemRepository;
import de.dlh.lht.ti.service.contract.NavigationStepService;
import de.dlh.lht.ti.service.contract.ProgressStepService;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;


import static de.dlh.lht.ti.utils.ErrorMessages.NAVIGATION_ITEM_ENTITY_WITH_QUOTATION_ID_AND_PROGRESS_STEP_NOT_FOUND_ERROR_MESSAGE;
import static de.dlh.lht.ti.utils.NavigationItemServiceImplTestHelper.createNavigationItemEntity;
import static de.dlh.lht.ti.utils.NavigationItemServiceImplTestHelper.createNavigationStepEntityMap;
import static de.dlh.lht.ti.utils.NavigationItemServiceImplTestHelper.createProgressStepEntityMap;
import static de.dlh.lht.ti.utils.TestConstants.LONG_ID_1;
import static de.dlh.lht.ti.utils.TestConstants.PROGRESS_STEPS_COUNT;
import static de.dlh.lht.ti.utils.TestConstants.PROGRESS_STEPS_COUNT_WITHOUT_RFP;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class NavigationItemServiceImplTest {

    @Captor
    private ArgumentCaptor<NavigationItemEntity> navigationItemEntityArgumentCaptor;

    @Captor
    private ArgumentCaptor<List<NavigationItemEntity>> navigationItemEntityListArgumentCaptor;

    @Mock
    private ProgressMapper progressMapperMock;

    @Mock
    private NavigationItemRepository navigationItemRepositoryMock;

    @Mock
    private NavigationStepService navigationStepServiceMock;
    @Mock
    private ProgressStepService progressStepServiceMock;

    @InjectMocks
    private NavigationItemServiceImpl navigationItemService;

    @Test
    void createNavigationItems_shouldCreateWithRFP() {
        var navigationStepEntityMap = createNavigationStepEntityMap();
        var progressStepEntityMap = createProgressStepEntityMap();

        when(navigationStepServiceMock.getAllNavigationSteps()).thenReturn(navigationStepEntityMap);
        when(progressStepServiceMock.getAllProgressSteps()).thenReturn(progressStepEntityMap);

        navigationItemService.createNavigationItems(LONG_ID_1, true);

        verify(navigationItemRepositoryMock).saveAll(navigationItemEntityListArgumentCaptor.capture());

        assertEquals(PROGRESS_STEPS_COUNT, navigationItemEntityListArgumentCaptor.getValue().size());
    }

    @Test
    void createNavigationItems_shouldCreateWithoutRFP() {
        var navigationStepEntityMap = createNavigationStepEntityMap();
        var progressStepEntityMap = createProgressStepEntityMap();

        when(navigationStepServiceMock.getAllNavigationSteps()).thenReturn(navigationStepEntityMap);
        when(progressStepServiceMock.getAllProgressSteps()).thenReturn(progressStepEntityMap);

        navigationItemService.createNavigationItems(LONG_ID_1, false);

        verify(navigationItemRepositoryMock).saveAll(navigationItemEntityListArgumentCaptor.capture());

        assertEquals(PROGRESS_STEPS_COUNT_WITHOUT_RFP, navigationItemEntityListArgumentCaptor.getValue().size());
    }

    @Test
    void getQuotationProgressByQuotationId_shouldReturnQuotationProgress() {
        navigationItemService.getQuotationProgressByQuotationId(LONG_ID_1);

        verify(navigationItemRepositoryMock).findAllByQuotationId(anyLong());
        verify(progressMapperMock).navigationItemEntityListToProgress(anyList());
    }

    @Test
    void updateProgress_shouldUpdateNavigationItem() {
        var navigationItem = createNavigationItemEntity();

        when(navigationItemRepositoryMock.findByQuotationIdAndProgressStepName(anyLong(), any(QuotationProgress.class)))
                .thenReturn(Optional.of(navigationItem));

        navigationItemService.updateProgress(LONG_ID_1, QuotationProgress.COVER, true);

        verify(navigationItemRepositoryMock).findByQuotationIdAndProgressStepName(anyLong(), any(QuotationProgress.class));
        verify(navigationItemRepositoryMock).save(navigationItemEntityArgumentCaptor.capture());

        assertTrue(navigationItemEntityArgumentCaptor.getValue().getIsValid());
    }

    @Test
    void updateProgress_shouldThrow_onNoNavigationItemFound() {

        when(navigationItemRepositoryMock.findByQuotationIdAndProgressStepName(anyLong(), any(QuotationProgress.class)))
                .thenReturn(Optional.empty());

        var exception = assertThrows(
                EntityNotFoundException.class,
                () -> navigationItemService.updateProgress(LONG_ID_1, QuotationProgress.COVER, true));

        verify(navigationItemRepositoryMock).findByQuotationIdAndProgressStepName(anyLong(), any(QuotationProgress.class));
        verify(navigationItemRepositoryMock, never()).save(any(NavigationItemEntity.class));

        assertEquals(String.format(
                        NAVIGATION_ITEM_ENTITY_WITH_QUOTATION_ID_AND_PROGRESS_STEP_NOT_FOUND_ERROR_MESSAGE,
                        LONG_ID_1,
                        QuotationProgress.COVER),
                exception.getMessage());
    }
}
