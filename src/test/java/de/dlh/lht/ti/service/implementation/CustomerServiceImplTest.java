package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.dto.importer.CustomerDto;
import de.dlh.lht.ti.entity.CustomerEntity;
import de.dlh.lht.ti.importer.model.CustomerRaw;
import de.dlh.lht.ti.mapper.CustomerMapper;
import de.dlh.lht.ti.repository.CustomerRepository;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;


import static de.dlh.lht.ti.utils.CustomerServiceImplTestHelper.createCustomerDto;
import static de.dlh.lht.ti.utils.CustomerServiceImplTestHelper.createCustomerEntity;
import static de.dlh.lht.ti.utils.CustomerServiceImplTestHelper.createCustomerEntityList;
import static de.dlh.lht.ti.utils.CustomerServiceImplTestHelper.createCustomerRaw;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CustomerServiceImplTest {

    @Captor
    private ArgumentCaptor<CustomerEntity> customerEntityArgumentCaptor;

    @Mock
    private CustomerMapper customerMapperMock;

    @Mock
    private CustomerRepository customerRepositoryMock;

    @InjectMocks
    private CustomerServiceImpl customerService;

    @Test
    void createCustomerEntity_shouldSaveACustomers() {
        var customerDto = createCustomerDto();
        var customer = createCustomerEntity();

        when(customerMapperMock.customerDtoToCustomerEntity(any(CustomerDto.class))).thenReturn(customer);

        customerService.createCustomerEntity(customerDto);

        verify(customerMapperMock).customerDtoToCustomerEntity(any(CustomerDto.class));
        verify(customerRepositoryMock).save(customerEntityArgumentCaptor.capture());

        var newCustomer = customerEntityArgumentCaptor.getValue();

        assertEquals(customerDto.getThreeLetterCode(), newCustomer.getThreeLetterCode());
        assertEquals(customerDto.getType(), newCustomer.getType());
    }

    @Test
    void getCustomerEntityByCustomerDtoMap_shouldGetAllTheCustomersAndMapThemIntoMap() {
        var customers = createCustomerEntityList();

        when(customerRepositoryMock.findAll()).thenReturn(customers);

        customerService.getCustomerEntityByCustomerDtoMap();

        verify(customerRepositoryMock).findAll();
        verify(customerMapperMock).customerEntityToCustomerDto(any(CustomerEntity.class));
    }

    @Test
    void customerRawToCustomerDto_shouldCallMapper() {
        var customerRaw = createCustomerRaw();

        customerService.customerRawToCustomerDto(customerRaw);

        verify(customerMapperMock).customerRawToCustomerDto(any(CustomerRaw.class));
    }

    @Test
    void getCustomersAsQuotationFilters_shouldReturnAllCustomerNames() {
        customerService.getCustomersAsQuotationFilters();

        verify(customerRepositoryMock).findAllCustomerEntitiesNames();
    }
}
