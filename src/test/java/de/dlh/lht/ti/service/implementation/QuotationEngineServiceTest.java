package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.dto.QuotationsQueryParametersDto;
import de.dlh.lht.ti.entity.EngineEntity;
import de.dlh.lht.ti.entity.QuotationEngineEntity;
import de.dlh.lht.ti.entity.QuotationEntity;
import de.dlh.lht.ti.enums.QuotationSort;
import de.dlh.lht.ti.exception.EntityNotFoundException;
import de.dlh.lht.ti.mapper.QuotationEngineMapper;
import de.dlh.lht.ti.model.QuotationDetails;
import de.dlh.lht.ti.repository.QuotationEngineRepository;
import de.dlh.lht.ti.service.contract.NavigationItemService;
import de.dlh.lht.ti.service.contract.UserPermissionService;
import de.dlh.lht.ti.service.contract.WorkscopeService;
import java.util.Optional;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;


import static de.dlh.lht.ti.utils.EngineServiceImplTestHelper.createEngineEntity;
import static de.dlh.lht.ti.utils.ErrorMessages.QUOTATION_ENTITY_WITH_ID_NOT_FOUND_ERROR_MESSAGE;
import static de.dlh.lht.ti.utils.ProgressMapperTestHelper.createTrueProgress;
import static de.dlh.lht.ti.utils.QuotationEngineServiceImplTestHelper.createQuotationDetailsDto;
import static de.dlh.lht.ti.utils.QuotationEngineServiceImplTestHelper.createQuotationEngineEntities;
import static de.dlh.lht.ti.utils.QuotationEngineServiceImplTestHelper.createQuotationLightList;
import static de.dlh.lht.ti.utils.QuotationServiceImplTestHelper.createQuotationEntity;
import static de.dlh.lht.ti.utils.QuotationUtil.DEFAULT_QUOTATION_LIST_SORT;
import static de.dlh.lht.ti.utils.TestConstants.LONG_ID_1;
import static de.dlh.lht.ti.utils.TestConstants.LONG_ID_2;
import static de.dlh.lht.ti.utils.WorkscopeServiceImplTestHelper.createWorkscopeEntityList;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class QuotationEngineServiceTest {

    @Mock
    QuotationEngineMapper quotationEngineMapperMock;

    @Mock
    QuotationEngineRepository quotationEngineRepositoryMock;

    @Mock
    NavigationItemService navigationItemServiceMock;
    @Mock
    UserPermissionService userPermissionServiceMock;
    @Mock
    WorkscopeService workscopeServiceMock;

    @InjectMocks
    QuotationEngineServiceImpl quotationEngineService;

    @Test
    void createQuotationEngineEntity_shouldSaveQuotationEngine() {
        var quotation = createQuotationEntity();
        var engine = createEngineEntity();
        var workscopes = createWorkscopeEntityList();

        quotationEngineService.createQuotationEngineEntity(quotation, engine, workscopes);

        verify(quotationEngineRepositoryMock, times(1)).save(any(QuotationEngineEntity.class));
    }

    @Test
    void getQuotationDetails_shouldReturnQuotationDetails() {
        var quotationDetailsDto = createQuotationDetailsDto();
        var progress = createTrueProgress();
        var quotationDetails = new QuotationDetails().progress(progress);
        var workscopes = createWorkscopeEntityList();

        when(quotationEngineRepositoryMock.findQuotationDetailsDto(anyLong())).thenReturn(Optional.of(quotationDetailsDto));
        when(quotationEngineMapperMock.quotationEngineEntityToQuotationDetails(any(QuotationEntity.class), any(EngineEntity.class), anyList()))
                .thenReturn(quotationDetails);
        when(workscopeServiceMock.getWorkscopesByQuotationId(anyLong())).thenReturn(workscopes);
        when(navigationItemServiceMock.getQuotationProgressByQuotationId(anyLong())).thenReturn(progress);
        when(userPermissionServiceMock.isUserOwnerOrAdmin(anyString())).thenReturn(true);

        var result = quotationEngineService.getQuotationDetails(LONG_ID_1);

        assertEquals(result, quotationDetails);
        assertEquals(result.getProgress(), progress);
    }

    @Test
    void getQuotationDetails_shouldThrow_onNoQuotationEngineWithSuchId() {
        var exception = Assertions.assertThrows(EntityNotFoundException.class,
                () -> quotationEngineService.getQuotationDetails(LONG_ID_1));

        Assertions.assertEquals(String.format(QUOTATION_ENTITY_WITH_ID_NOT_FOUND_ERROR_MESSAGE, LONG_ID_1), exception.getMessage());
    }

    @Test
    void getEmptyQuotationList_WhenQuotationQueryParametersAreNull() {
        //act
        var result = quotationEngineService.getQuotationsPage(null);
        //assert
        assertTrue(result.getQuotations().isEmpty());
    }

    @Test
    void getFullQuotationList_WhenQuotationQueryParametersAreEmpty() {
        //arrange
        var queryParameters = new QuotationsQueryParametersDto();
        var quotationEngineEntities = createQuotationEngineEntities(LONG_ID_1, LONG_ID_2);
        var pageRequest = PageRequest.of(0, 1);
        var mockedPageResult = new PageImpl<>(quotationEngineEntities, pageRequest, 1);
        var mockedQuotationLightList = createQuotationLightList(LONG_ID_1, LONG_ID_2);
        when(quotationEngineRepositoryMock
                .findAllWithAppliedFilter(any(), any(), any(), any(), any(), any(), any(), any(), any())
        ).thenReturn(mockedPageResult);
        when(quotationEngineMapperMock.quotationEngineEntityListToQuotationLightList(anyList(), any())).thenReturn(
                mockedQuotationLightList);
        //act
        var result = quotationEngineService.getQuotationsPage(queryParameters);
        //assert
        assertEquals(mockedQuotationLightList.size(), result.getQuotations().size());
        for (int i = 0; i < result.getQuotations().size(); i++) {
            assertEquals(mockedQuotationLightList.get(i).getId(), result.getQuotations().get(i).getId());
        }
    }

    @Test
    void getQuotationLightList_testAppliedSortDependingOfApiSortRequest() {
        //arrange
        var queryQuotationParameters = new QuotationsQueryParametersDto();
        queryQuotationParameters.setSortBy(QuotationSort.ENGINE_TYPE);
        var sortByEngine = Sort.by(Sort.Order.asc("engine.name"), Sort.Order.asc("id"));
        when(quotationEngineRepositoryMock
                .findAllWithAppliedFilter(any(), any(), any(), any(), any(), any(), any(), any(), any())
        ).thenReturn(Page.empty());
        //act
        quotationEngineService.getQuotationsPage(queryQuotationParameters);
        //assert
        var argumentCapture = ArgumentCaptor.forClass(Pageable.class);
        verify(quotationEngineRepositoryMock, times(1)).findAllWithAppliedFilter(
                argumentCapture.capture(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any()
        );
        assertEquals(sortByEngine, argumentCapture.getValue().getSort());
    }

    @Test
    void getQuotationLightList_testAppliedDefaultSortWhenApiSortIsMissing() {
        //arrange
        var queryQuotationParameters = new QuotationsQueryParametersDto();
        when(quotationEngineRepositoryMock
                .findAllWithAppliedFilter(any(), any(), any(), any(), any(), any(), any(), any(), any())
        ).thenReturn(Page.empty());
        //act
        quotationEngineService.getQuotationsPage(queryQuotationParameters);
        //assert
        var argumentCapture = ArgumentCaptor.forClass(Pageable.class);
        verify(quotationEngineRepositoryMock, times(1)).findAllWithAppliedFilter(
                argumentCapture.capture(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any()
        );
        assertEquals(DEFAULT_QUOTATION_LIST_SORT, argumentCapture.getValue().getSort());
    }
}
