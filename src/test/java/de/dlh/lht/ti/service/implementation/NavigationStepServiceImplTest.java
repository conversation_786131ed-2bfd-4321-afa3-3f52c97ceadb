package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.repository.NavigationStepRepository;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;


import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class NavigationStepServiceImplTest {

    @Mock
    NavigationStepRepository navigationStepRepositoryMock;

    @InjectMocks
    NavigationStepServiceImpl navigationStepService;

    @Test
    void getAllNavigationSteps_shouldCallTheRepo() {
        navigationStepService.getAllNavigationSteps();

        verify(navigationStepRepositoryMock).findAll();
    }
}
