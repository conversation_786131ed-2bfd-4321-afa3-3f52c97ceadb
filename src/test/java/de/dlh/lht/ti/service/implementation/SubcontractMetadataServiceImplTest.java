package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.importer.model.PartRaw;
import de.dlh.lht.ti.mapper.SubcontractMetadataMapper;
import de.dlh.lht.ti.repository.SubcontractMetadataRepository;
import de.dlh.lht.ti.service.contract.PartService;
import java.util.ArrayList;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;


import static de.dlh.lht.ti.utils.MaterialPricingTestHelper.createClusterIdByEngineIdByPartIdMap;
import static de.dlh.lht.ti.utils.PartServiceImplTestHelper.createPartDtoPartIdMap;
import static de.dlh.lht.ti.utils.SubcontractPricingTestHelper.createSubcontractPricingItemEntityList;
import static de.dlh.lht.ti.utils.SubcontractPricingTestHelper.createSubcontractPricingItemRawList;
import static de.dlh.lht.ti.utils.TestConstants.LONG_ID_1;
import static de.dlh.lht.ti.utils.WorkscopeServiceImplTestHelper.createWorkscopeNameByIdMap;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class SubcontractMetadataServiceImplTest {

    @Mock
    private SubcontractMetadataMapper subcontractMetadataMapperMock;

    @Mock
    private SubcontractMetadataRepository subcontractMetadataRepositoryMock;

    @Mock
    private PartService partServiceMock;

    @InjectMocks
    private SubcontractMetadataServiceImpl subcontractMetadataService;

    @Test
    void createSubcontractMetadata_shouldReturn_onNullRawSubcontractPricingItems() {
        var subcontractPricingItemEntityList = createSubcontractPricingItemEntityList();
        var partDtoPartIdMap = createPartDtoPartIdMap();
        var clusterIdByEngineIdByPartIdMap = createClusterIdByEngineIdByPartIdMap();
        var workscopeNameByIdMap = createWorkscopeNameByIdMap();

        subcontractMetadataService.createSubcontractMetadata(
                null,
                subcontractPricingItemEntityList,
                LONG_ID_1,
                partDtoPartIdMap,
                clusterIdByEngineIdByPartIdMap,
                workscopeNameByIdMap);

        verify(partServiceMock, never()).findPartIdByNameAndType(any(PartRaw.class), anyMap());
        verify(subcontractMetadataRepositoryMock, never()).saveAll(any());
    }

    @Test
    void createSubcontractPricingItems_shouldReturnEmptyArray_onEmptyRawSubcontractPricingItems() {
        var subcontractPricingItemEntityList = createSubcontractPricingItemEntityList();
        var partDtoPartIdMap = createPartDtoPartIdMap();
        var clusterIdByEngineIdByPartIdMap = createClusterIdByEngineIdByPartIdMap();
        var workscopeNameByIdMap = createWorkscopeNameByIdMap();

        subcontractMetadataService.createSubcontractMetadata(
                new ArrayList<>(),
                subcontractPricingItemEntityList,
                LONG_ID_1,
                partDtoPartIdMap,
                clusterIdByEngineIdByPartIdMap,
                workscopeNameByIdMap);

        verify(partServiceMock, never()).findPartIdByNameAndType(any(PartRaw.class), anyMap());
        verify(subcontractMetadataRepositoryMock, never()).saveAll(any());
    }

    @Test
    void createSubcontractMetadata_shouldReturn_onNullSubcontractPricingItems() {
        var subcontractPricingItemRawList = createSubcontractPricingItemRawList();
        var partDtoPartIdMap = createPartDtoPartIdMap();
        var clusterIdByEngineIdByPartIdMap = createClusterIdByEngineIdByPartIdMap();
        var workscopeNameByIdMap = createWorkscopeNameByIdMap();

        subcontractMetadataService.createSubcontractMetadata(
                subcontractPricingItemRawList,
                null,
                LONG_ID_1,
                partDtoPartIdMap,
                clusterIdByEngineIdByPartIdMap,
                workscopeNameByIdMap);

        verify(partServiceMock, never()).findPartIdByNameAndType(any(PartRaw.class), anyMap());
        verify(subcontractMetadataRepositoryMock, never()).saveAll(any());
    }

    @Test
    void createSubcontractPricingItems_shouldReturnEmptyArray_onEmptySubcontractPricingItems() {
        var subcontractPricingItemRawList = createSubcontractPricingItemRawList();
        var partDtoPartIdMap = createPartDtoPartIdMap();
        var clusterIdByEngineIdByPartIdMap = createClusterIdByEngineIdByPartIdMap();
        var workscopeNameByIdMap = createWorkscopeNameByIdMap();

        subcontractMetadataService.createSubcontractMetadata(
                subcontractPricingItemRawList,
                new ArrayList<>(),
                LONG_ID_1,
                partDtoPartIdMap,
                clusterIdByEngineIdByPartIdMap,
                workscopeNameByIdMap);

        verify(partServiceMock, never()).findPartIdByNameAndType(any(PartRaw.class), anyMap());
        verify(subcontractMetadataRepositoryMock, never()).saveAll(any());
    }

    @Test
    void createSubcontractPricingItems_shouldSaveAll() {
        var subcontractPricingItemRawList = createSubcontractPricingItemRawList();
        var subcontractPricingItemEntityList = createSubcontractPricingItemEntityList();
        var partDtoPartIdMap = createPartDtoPartIdMap();
        var clusterIdByEngineIdByPartIdMap = createClusterIdByEngineIdByPartIdMap();
        var workscopeNameByIdMap = createWorkscopeNameByIdMap();

        when(partServiceMock.findPartIdByNameAndType(any(PartRaw.class), anyMap())).thenReturn(LONG_ID_1);

        subcontractMetadataService.createSubcontractMetadata(
                subcontractPricingItemRawList,
                subcontractPricingItemEntityList,
                LONG_ID_1,
                partDtoPartIdMap,
                clusterIdByEngineIdByPartIdMap,
                workscopeNameByIdMap);

        verify(partServiceMock).findPartIdByNameAndType(any(PartRaw.class), anyMap());
        verify(subcontractMetadataRepositoryMock).saveAll(any());
    }
}
