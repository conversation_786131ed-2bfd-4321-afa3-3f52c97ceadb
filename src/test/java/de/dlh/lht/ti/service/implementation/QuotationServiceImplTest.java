package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.dto.RfpQuotationDto;
import de.dlh.lht.ti.entity.QuotationEntity;
import de.dlh.lht.ti.enums.QuotationStatus;
import de.dlh.lht.ti.exception.EntityNotFoundException;
import de.dlh.lht.ti.exception.QuotationAlreadyBegunException;
import de.dlh.lht.ti.repository.QuotationRepository;
import de.dlh.lht.ti.service.contract.NavigationItemService;
import de.dlh.lht.ti.service.contract.QuotationEngineService;
import de.dlh.lht.ti.service.contract.UserPermissionService;
import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;


import static de.dlh.lht.ti.utils.ErrorMessages.QUOTATION_ENTITY_WITH_ID_NOT_FOUND_ERROR_MESSAGE;
import static de.dlh.lht.ti.utils.ErrorMessages.QUOTATION_WITH_ID_ALREADY_BEGUN_ERROR_MESSAGE;
import static de.dlh.lht.ti.utils.QuotationServiceImplTestHelper.createBegunQuotationEntity;
import static de.dlh.lht.ti.utils.QuotationServiceImplTestHelper.createContractTypesDto;
import static de.dlh.lht.ti.utils.QuotationServiceImplTestHelper.createQuotationEntity;
import static de.dlh.lht.ti.utils.TestConstants.LONG_ID_1;
import static de.dlh.lht.ti.utils.TestConstants.ZONED_DATE_TIME;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class QuotationServiceImplTest {

    @Captor
    private ArgumentCaptor<QuotationEntity> quotationEntityArgumentCaptor;

    @Mock
    private QuotationRepository quotationRepositoryMock;

    @Mock
    private NavigationItemService navigationItemServiceMock;

    @Mock
    private QuotationEngineService quotationEngineServiceMock;

    @Mock
    private UserPermissionService userPermissionServiceMock;

    @InjectMocks
    private QuotationServiceImpl quotationService;

    @Test
    void getQuotationDetail_shouldCallQuotationEngineService() {

        quotationService.getQuotationDetails(LONG_ID_1);

        verify(quotationEngineServiceMock).getQuotationDetails(LONG_ID_1);
    }

    @Test
    void beginQuotation_shouldSaveContractTypesUpdateStatusAndLastUpdated_whenQuotationNotBegun() {
        var quotation = createQuotationEntity();
        var contractTypesDto = createContractTypesDto();

        when(quotationRepositoryMock.findById(LONG_ID_1)).thenReturn(Optional.of(quotation));
        when(userPermissionServiceMock.isUserOwnerOrAdmin(anyString())).thenReturn(true);

        quotationService.beginQuotation(LONG_ID_1, contractTypesDto);

        verify(quotationRepositoryMock).findById(LONG_ID_1);
        verify(quotationRepositoryMock).save(quotationEntityArgumentCaptor.capture());
        verify(navigationItemServiceMock).createNavigationItems(anyLong(), anyBoolean());

        var savedQuotation = quotationEntityArgumentCaptor.getValue();

        assertEquals(contractTypesDto.isRoutineFixedPrices(), savedQuotation.isRoutineFixedPrices());
        assertEquals(QuotationStatus.IN_PROGRESS.ordinal(), savedQuotation.getStatus());
    }

    @Test
    void beginQuotation_shouldThrow_whenQuotationNotBegun() {
        var begunQuotation = createBegunQuotationEntity();
        var contractTypesDto = createContractTypesDto();

        when(quotationRepositoryMock.findById(LONG_ID_1)).thenReturn(Optional.of(begunQuotation));
        when(userPermissionServiceMock.isUserOwnerOrAdmin(anyString())).thenReturn(true);

        var exception = assertThrows(QuotationAlreadyBegunException.class,
                () -> quotationService.beginQuotation(LONG_ID_1, contractTypesDto));

        assertEquals(String.format(QUOTATION_WITH_ID_ALREADY_BEGUN_ERROR_MESSAGE, LONG_ID_1), exception.getMessage());

        verify(quotationRepositoryMock).findById(LONG_ID_1);
        verify(quotationRepositoryMock, never()).save(any(QuotationEntity.class));
    }

    @Test
    void getRfpEngineQuotationDtoById() {
        //arrange
        when(quotationRepositoryMock.findRfpQuotationDtoById(anyLong()))
                .thenReturn(Optional.of(
                        new RfpQuotationDto(
                                LONG_ID_1,
                                "engineTestVersion",
                                false,
                                false,
                                ZonedDateTime.now(),
                                BigDecimal.TEN
                        )));

        //act
        quotationService.getRfpQuotationDtoById(LONG_ID_1);

        verify(quotationRepositoryMock).findRfpQuotationDtoById(LONG_ID_1);
    }

    @Test
    void getQuotationBaseYear_shouldReturnCorrectBaseYear() {
        when(quotationRepositoryMock.findContractStartById(anyLong())).thenReturn(Optional.of(ZONED_DATE_TIME));

        var result = quotationService.getQuotationBaseYear(LONG_ID_1);

        assertEquals(String.valueOf(ZONED_DATE_TIME.getYear()), result);

        verify(quotationRepositoryMock).findContractStartById(anyLong());
    }

    @Test
    void getQuotationBaseYear_shouldThrow_onNoQuotationFound() {
        when(quotationRepositoryMock.findContractStartById(anyLong())).thenReturn(Optional.empty());

        var exception = assertThrows(EntityNotFoundException.class,
                () -> quotationService.getQuotationBaseYear(LONG_ID_1));

        assertEquals(String.format(QUOTATION_ENTITY_WITH_ID_NOT_FOUND_ERROR_MESSAGE, LONG_ID_1), exception.getMessage());

        verify(quotationRepositoryMock).findContractStartById(anyLong());
    }
}