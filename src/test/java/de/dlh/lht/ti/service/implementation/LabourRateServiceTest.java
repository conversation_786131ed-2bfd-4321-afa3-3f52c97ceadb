package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.entity.LabourRateEntity;
import de.dlh.lht.ti.exception.ValidationException;
import de.dlh.lht.ti.mapper.LabourRateMapper;
import de.dlh.lht.ti.model.LabourRate;
import de.dlh.lht.ti.model.LabourRateInput;
import de.dlh.lht.ti.repository.LabourRateRepository;

import java.math.BigDecimal;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;


import static de.dlh.lht.ti.utils.TestConstants.LONG_ID_1;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class LabourRateServiceTest {

    @Mock
    private LabourRateMapper labourRateMapper;

    @Mock
    private LabourRateRepository labourRateRepository;

    @InjectMocks
    private LabourRateServiceImpl labourRateService;

    @Test
    void testGetLabourRates_ExistingLabourRateEntity_ReturnsLabourRate() {
        // Arrange
        var quotationId = LONG_ID_1;
        var labourRateEntity =
                new LabourRateEntity(quotationId, null, null, BigDecimal.ZERO);
        var labourRateInput = new LabourRateInput()
                .routineLabourRate(labourRateEntity.getRoutineLabourRate())
                .nonRoutineLabourRate(labourRateEntity.getNonRoutineLabourRate())
                .eparDiscount(labourRateEntity.getEparDiscount().doubleValue());
        var labourRate = new LabourRate();
        labourRate.labourRate(labourRateInput);
        when(labourRateRepository.getLabourRateEntityByQuotationId(quotationId))
                .thenReturn(Optional.of(labourRateEntity));
        when(labourRateMapper.toApiModel(labourRateEntity)).thenReturn(labourRateInput);

        // Act
        var result = labourRateService.getLabourRates(quotationId);

        // Assert
        assertNotNull(result);
        assertEquals(labourRateInput, result.getLabourRate());
        verify(labourRateRepository).getLabourRateEntityByQuotationId(quotationId);
        verify(labourRateMapper).toApiModel(labourRateEntity);
    }

    @Test
    void testGetLabourRates_NonExistingLabourRateEntity_ReturnsLabourRateWithDefaults() {
        // Arrange
        var quotationId = LONG_ID_1;
        var eparDiscount = BigDecimal.ZERO;
        var labourRateEntity =
                new LabourRateEntity(quotationId, null, null, eparDiscount);
        var labourRateInput = new LabourRateInput()
                .routineLabourRate(labourRateEntity.getRoutineLabourRate())
                .nonRoutineLabourRate(labourRateEntity.getNonRoutineLabourRate())
                .eparDiscount(labourRateEntity.getEparDiscount().doubleValue());
        when(labourRateRepository.getLabourRateEntityByQuotationId(quotationId)).thenReturn(Optional.empty());
        when(labourRateRepository.save(any(LabourRateEntity.class))).thenReturn(labourRateEntity);
        when(labourRateMapper.toApiModel(labourRateEntity)).thenReturn(labourRateInput);

        // Act
        var result = labourRateService.getLabourRates(quotationId);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getLabourRate());
        assertNull(result.getLabourRate().getRoutineLabourRate());
        assertNull(result.getLabourRate().getNonRoutineLabourRate());
        assertEquals(eparDiscount.doubleValue(), result.getLabourRate().getEparDiscount());
        verify(labourRateRepository).getLabourRateEntityByQuotationId(quotationId);
        verify(labourRateRepository).save(any(LabourRateEntity.class));
    }

    @Test
    void testSaveLabourRates_ValidInput_ReturnsSavedLabourRate()
            throws ValidationException {
        // Arrange
        var quotationId = LONG_ID_1;
        var eparDiscount = BigDecimal.ZERO;
        var labourRateEntity =
                new LabourRateEntity(quotationId, null, null, eparDiscount);
        var labourRateInput = new LabourRateInput()
                .routineLabourRate(labourRateEntity.getRoutineLabourRate())
                .nonRoutineLabourRate(labourRateEntity.getNonRoutineLabourRate())
                .eparDiscount(labourRateEntity.getEparDiscount().doubleValue());
        when(labourRateRepository.getLabourRateEntityByQuotationId(quotationId))
                .thenReturn(Optional.of(labourRateEntity));
        when(labourRateMapper.toApiModel(labourRateEntity)).thenReturn(labourRateInput);
        when(labourRateMapper.labourRateInputToLabourRateEntity(
                any(LabourRateInput.class),
                any(LabourRateEntity.class),
                anyLong())
        ).thenReturn(labourRateEntity);
        when(labourRateRepository.save(any(LabourRateEntity.class))).thenReturn(labourRateEntity);

        // Act
        var result = labourRateService.saveLabourRates(quotationId, labourRateInput);

        // Assert
        assertNotNull(result);
        assertEquals(labourRateInput, result.getLabourRate());
        verify(labourRateRepository).getLabourRateEntityByQuotationId(quotationId);
        verify(labourRateMapper).toApiModel(labourRateEntity);
        verify(labourRateRepository).save(any(LabourRateEntity.class));
    }

    @Test
    void testSaveLabourRates_InvalidInput_ThrowsValidationException() {
        // Arrange
        var labourRateInput = new LabourRateInput();
        labourRateInput.setRoutineLabourRate(-1); // Invalid routine labour rate

        // Act & Assert
        assertThrows(ValidationException.class, () -> labourRateService.saveLabourRates(LONG_ID_1, labourRateInput));
    }

    @Test
    void testHasValidLabourRate_ValidLabourRate_ReturnsTrue() {
        // Arrange
        var labourRate = new LabourRate();
        var labourRateInput = new LabourRateInput();
        labourRateInput.setRoutineLabourRate(10);
        labourRateInput.setNonRoutineLabourRate(20);
        labourRateInput.setEparDiscount(15.0);
        labourRate.setLabourRate(labourRateInput);

        // Act
        var result = labourRateService.hasValidLabourRate(labourRate);

        // Assert
        assertTrue(result);
    }

    @Test
    void testHasValidLabourRate_InvalidLabourRate_ReturnsFalse() {
        // Arrange
        var labourRate = new LabourRate();
        var labourRateInput = new LabourRateInput();
        labourRateInput.setRoutineLabourRate(0); // Invalid routine labour rate
        labourRate.setLabourRate(labourRateInput);

        // Act
        var result = labourRateService.hasValidLabourRate(labourRate);

        // Assert
        assertFalse(result);
    }
}
