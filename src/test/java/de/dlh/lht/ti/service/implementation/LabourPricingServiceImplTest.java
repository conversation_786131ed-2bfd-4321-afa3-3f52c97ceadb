package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.enums.QuotationProgress;
import de.dlh.lht.ti.exception.SaveLabourPricingNotAllowedException;
import de.dlh.lht.ti.model.LabourRate;
import de.dlh.lht.ti.model.LabourRateInput;
import de.dlh.lht.ti.model.Progress;
import de.dlh.lht.ti.model.RfpEngineData;
import de.dlh.lht.ti.model.RfpItem;
import de.dlh.lht.ti.model.RfpModuleData;
import de.dlh.lht.ti.model.RfpRequest;
import de.dlh.lht.ti.service.contract.LabourRateService;
import de.dlh.lht.ti.service.contract.NavigationItemService;
import de.dlh.lht.ti.service.contract.QuotationService;
import de.dlh.lht.ti.service.contract.RfpEngineService;
import de.dlh.lht.ti.service.contract.RfpModuleService;
import de.dlh.lht.ti.service.contract.UserPermissionService;
import de.dlh.lht.ti.service.contract.UserService;
import de.dlh.lht.ti.utils.ProgressMapperTestHelper;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;


import static de.dlh.lht.ti.utils.ErrorMessages.SAVE_RFP_MODULE_NOT_ALLOWED_ERROR_MESSAGE;
import static de.dlh.lht.ti.utils.LabourPricingServiceImplTestHelper.createRfpModuleData;
import static de.dlh.lht.ti.utils.LabourPricingServiceImplTestHelper.createRfpRequest;
import static de.dlh.lht.ti.utils.TestConstants.LONG_ID_1;
import static de.dlh.lht.ti.utils.TestConstants.TEST_UNUMBER;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class LabourPricingServiceImplTest {

    @Mock
    private LabourRateService labourRateServiceMock;
    @Mock
    private RfpEngineService rfpEngineServiceMock;
    @Mock
    private NavigationItemService navigationItemServiceMock;
    @Mock
    private QuotationService quotationServiceMock;
    @Mock
    private RfpModuleService rfpModuleServiceMock;
    @Mock
    private UserPermissionService userPermissionServiceMock;
    @Mock
    private UserService userServiceMock;

    @InjectMocks
    private LabourPricingServiceImpl labourPricingService;

    @Test
    public void testGetLabourRates() {
        //arrange
        var quotationId = LONG_ID_1;
        var progress = ProgressMapperTestHelper.createTrueProgress();
        var labourRate = new LabourRate();

        when(userServiceMock.getCurrentOwnerUNumberByQuotationId(quotationId)).thenReturn("user1");
        when(userPermissionServiceMock.isUserOwnerOrAdmin(anyString())).thenReturn(true);
        when(navigationItemServiceMock.getQuotationProgressByQuotationId(quotationId)).thenReturn(progress);
        when(labourRateServiceMock.getLabourRates(anyLong())).thenReturn(labourRate);

        //act
        var result = labourPricingService.getLabourRates(quotationId);

        //assert
        verify(userServiceMock, times(1)).getCurrentOwnerUNumberByQuotationId(quotationId);
        verify(userPermissionServiceMock, times(1)).isUserOwnerOrAdmin("user1");
        verify(navigationItemServiceMock, times(1)).getQuotationProgressByQuotationId(quotationId);

        assertEquals(result.getProgress(), progress);
        assertEquals(true, result.getCanCurrentUserEdit());
    }

    @Test
    public void testSaveLabourRates_withoutFinishingLabourRateStep() {
        //arrange
        var quotationId = LONG_ID_1;
        var labourRateInput = new LabourRateInput()
                .routineLabourRate(10)
                .routineLabourRate(null)
                .eparDiscount(0.0);

        when(userServiceMock.getCurrentOwnerUNumberByQuotationId(quotationId)).thenReturn("user1");
        when(userPermissionServiceMock.isUserOwnerOrAdmin(anyString())).thenReturn(true);
        when(labourRateServiceMock.saveLabourRates(anyLong(), any(LabourRateInput.class)))
                .thenReturn(new LabourRate().labourRate(labourRateInput));

        //act
        labourPricingService.saveLabourRates(quotationId, labourRateInput);

        //assert
        verify(userServiceMock, times(1)).getCurrentOwnerUNumberByQuotationId(quotationId);
        verify(userPermissionServiceMock, times(1)).isUserOwnerOrAdmin("user1");
        verify(navigationItemServiceMock, times(1)).updateProgress(quotationId, QuotationProgress.LABOUR_RATE_AND_EPAR, false);
        verify(navigationItemServiceMock, times(1)).getQuotationProgressByQuotationId(quotationId);
    }

    @Test
    public void testSaveLabourRates_withFinishingLabourRateStep() {
        //arrange
        var quotationId = LONG_ID_1;
        var labourRateInput = new LabourRateInput()
                .routineLabourRate(10)
                .nonRoutineLabourRate(10)
                .eparDiscount(10.0);

        when(userServiceMock.getCurrentOwnerUNumberByQuotationId(quotationId)).thenReturn("user1");
        when(userPermissionServiceMock.isUserOwnerOrAdmin(anyString())).thenReturn(true);
        when(labourRateServiceMock.saveLabourRates(anyLong(), any(LabourRateInput.class)))
                .thenReturn(new LabourRate().labourRate(labourRateInput));
        when(labourRateServiceMock.hasValidLabourRate(any(LabourRate.class))).thenReturn(true);

        //act
        labourPricingService.saveLabourRates(quotationId, labourRateInput);

        //assert
        verify(userServiceMock, times(1)).getCurrentOwnerUNumberByQuotationId(quotationId);
        verify(userPermissionServiceMock, times(1)).isUserOwnerOrAdmin("user1");
        verify(navigationItemServiceMock, times(1)).updateProgress(quotationId, QuotationProgress.LABOUR_RATE_AND_EPAR, true);
        verify(navigationItemServiceMock, times(1)).getQuotationProgressByQuotationId(quotationId);
    }

    @Test
    public void testSaveLabourRates_failWithUsersPermissions() {
        //arrange
        var quotationId = LONG_ID_1;
        var labourRateInput = new LabourRateInput()
                .routineLabourRate(10_000)
                .nonRoutineLabourRate(10_000)
                .eparDiscount(80.90);
        when(userServiceMock.getCurrentOwnerUNumberByQuotationId(quotationId)).thenReturn("user1");
        when(userPermissionServiceMock.isUserOwnerOrAdmin(anyString())).thenReturn(false);

        //act & assert
        assertThrows(
                SaveLabourPricingNotAllowedException.class,
                () -> labourPricingService.saveLabourRates(quotationId, labourRateInput)
        );

        verify(userServiceMock, times(1)).getCurrentOwnerUNumberByQuotationId(quotationId);
        verify(userPermissionServiceMock, times(1)).isUserOwnerOrAdmin("user1");
        verify(navigationItemServiceMock, never()).updateProgress(quotationId, QuotationProgress.LABOUR_RATE_AND_EPAR, true);
        verify(navigationItemServiceMock, never()).getQuotationProgressByQuotationId(quotationId);
    }

    @Test
    void getRfpEngineData_returnsRfpEngineData() {
        // Arrange
        Long quotationId = LONG_ID_1;
        var rfpEngineItem = new RfpItem();
        var rfpEngineItems = List.of(rfpEngineItem);
        when(userServiceMock.getCurrentOwnerUNumberByQuotationId(quotationId)).thenReturn("user1");
        when(userPermissionServiceMock.isUserOwnerOrAdmin(anyString())).thenReturn(true);
        when(rfpEngineServiceMock.getRfpEngineData(anyLong()))
                .thenReturn(new RfpEngineData().rfpEngineItems(rfpEngineItems));
        when(quotationServiceMock.isRfpContractSelected(anyLong())).thenReturn(true);

        // Act
        var result = labourPricingService.getRfpEngineData(quotationId);

        // Assert
        verify(rfpEngineServiceMock).getRfpEngineData(quotationId);
        assertEquals(true, result.getCanCurrentUserEdit());
        assertEquals(rfpEngineItems, result.getRfpEngineData().getRfpEngineItems());
    }

    @Test
    void getRfpModule_shouldReturnRfpModuleSuccessfully() {
        when(userServiceMock.getCurrentOwnerUNumberByQuotationId(anyLong())).thenReturn(TEST_UNUMBER);
        when(userPermissionServiceMock.isUserOwnerOrAdmin(anyString())).thenReturn(true);
        when(navigationItemServiceMock.getQuotationProgressByQuotationId(anyLong())).thenReturn(new Progress());
        when(rfpModuleServiceMock.getRfpModuleData(anyLong())).thenReturn(new RfpModuleData());
        when(quotationServiceMock.isRfpContractSelected(anyLong())).thenReturn(true);

        labourPricingService.getRfpModule(LONG_ID_1);

        verify(userServiceMock).getCurrentOwnerUNumberByQuotationId(anyLong());
        verify(userPermissionServiceMock).isUserOwnerOrAdmin(anyString());
        verify(navigationItemServiceMock).getQuotationProgressByQuotationId(anyLong());
        verify(rfpModuleServiceMock).getRfpModuleData(anyLong());
    }

    @Test
    void saveRfpModule_shouldSaveAndReturnTheNewData() {
        var rfpModuleData = createRfpModuleData();
        var rfpRequest = createRfpRequest();

        when(userServiceMock.getCurrentOwnerUNumberByQuotationId(anyLong())).thenReturn(TEST_UNUMBER);
        when(userPermissionServiceMock.isUserOwnerOrAdmin(anyString())).thenReturn(true);
        when(rfpModuleServiceMock.saveRfpModuleData(anyLong(), any(RfpRequest.class))).thenReturn(rfpModuleData);
        when(rfpModuleServiceMock.isRfpModuleDataValid(any(RfpModuleData.class))).thenReturn(true);
        when(quotationServiceMock.isRfpContractSelected(anyLong())).thenReturn(true);

        var result = labourPricingService.saveRfpModule(LONG_ID_1, rfpRequest);

        assertTrue(result.getCanCurrentUserEdit());
        assertEquals(rfpModuleData, result.getRfpModuleData());

        verify(userServiceMock).getCurrentOwnerUNumberByQuotationId(anyLong());
        verify(userPermissionServiceMock).isUserOwnerOrAdmin(anyString());
        verify(rfpModuleServiceMock).saveRfpModuleData(anyLong(), any(RfpRequest.class));
        verify(rfpModuleServiceMock).isRfpModuleDataValid(any(RfpModuleData.class));
        verify(navigationItemServiceMock).updateProgress(anyLong(), any(QuotationProgress.class), anyBoolean());
        verify(navigationItemServiceMock).getQuotationProgressByQuotationId(anyLong());
    }

    @Test
    void saveRfpModule_shouldSave1() {
        var rfpRequest = createRfpRequest();

        when(userServiceMock.getCurrentOwnerUNumberByQuotationId(anyLong())).thenReturn(TEST_UNUMBER);
        when(userPermissionServiceMock.isUserOwnerOrAdmin(anyString())).thenReturn(false);
        when(quotationServiceMock.isRfpContractSelected(anyLong())).thenReturn(true);

        var result = assertThrows(SaveLabourPricingNotAllowedException.class,
                () -> labourPricingService.saveRfpModule(LONG_ID_1, rfpRequest));

        assertEquals(String.format(SAVE_RFP_MODULE_NOT_ALLOWED_ERROR_MESSAGE, TEST_UNUMBER), result.getMessage());

        verify(userServiceMock).getCurrentOwnerUNumberByQuotationId(anyLong());
        verify(userPermissionServiceMock).isUserOwnerOrAdmin(anyString());
        verify(rfpModuleServiceMock, never()).saveRfpModuleData(anyLong(), any(RfpRequest.class));
        verify(rfpModuleServiceMock, never()).isRfpModuleDataValid(any(RfpModuleData.class));
        verify(navigationItemServiceMock, never()).updateProgress(anyLong(), any(QuotationProgress.class), anyBoolean());
        verify(navigationItemServiceMock, never()).getQuotationProgressByQuotationId(anyLong());
    }
}
