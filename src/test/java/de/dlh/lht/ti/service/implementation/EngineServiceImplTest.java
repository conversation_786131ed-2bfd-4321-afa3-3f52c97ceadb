package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.repository.EngineRepository;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;


import static de.dlh.lht.ti.utils.EngineServiceImplTestHelper.createEngineEntityList;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class EngineServiceImplTest {

    @Mock
    private EngineRepository engineRepositoryMock;

    @InjectMocks
    private EngineServiceImpl engineService;

    @Test
    void getEngineDtoEngineEntityMap_shouldGetAllTheEnginesAndMapThemIntoMap() {
        var engines = createEngineEntityList();

        when(engineRepositoryMock.findAll()).thenReturn(engines);

        engineService.getEngineEntityByEngineNameMap();

        verify(engineRepositoryMock).findAll();
    }

    @Test
    void getEnginesAsQuotationFilters_shouldReturnAllEngineNames() {
        engineService.getEnginesAsQuotationFilters();

        verify(engineRepositoryMock).findAllEngineEntitiesNames();
    }
}
