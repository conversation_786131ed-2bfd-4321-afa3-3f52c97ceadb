package de.dlh.lht.ti.service.implementation;

import de.dlh.lht.ti.entity.Z2RatingEntity;
import de.dlh.lht.ti.enums.QuotationProgress;
import de.dlh.lht.ti.mapper.Z2RatingMapper;
import de.dlh.lht.ti.model.Z2Ratings;
import de.dlh.lht.ti.repository.Z2RatingRepository;
import de.dlh.lht.ti.service.contract.MaterialPricingItemService;
import de.dlh.lht.ti.service.contract.NavigationItemService;
import de.dlh.lht.ti.service.contract.PartMetadataService;
import de.dlh.lht.ti.service.contract.WorkscopeService;
import de.dlh.lht.ti.utils.Z2RatingServiceHelper;
import java.util.HashMap;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;


import static de.dlh.lht.ti.utils.TestConstants.LONG_ID_1;
import static de.dlh.lht.ti.utils.TestConstants.LONG_ID_2;
import static de.dlh.lht.ti.utils.Z2RatingServiceHelper.createZ2RatingDtoList;
import static java.util.Collections.emptyList;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anySet;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class Z2RatingServiceImplTest {

    @Mock
    private Z2RatingMapper z2RatingMapperMock;

    @Mock
    private Z2RatingRepository z2RatingRepositoryMock;

    @Mock
    private NavigationItemService navigationItemServiceMock;

    @Mock
    private PartMetadataService partMetadataServiceMock;

    @Mock
    private WorkscopeService workscopeService;

    @Mock
    private MaterialPricingItemService materialPricingItemService;

    @InjectMocks
    private Z2RatingServiceImpl z2RatingService;

    @Test
    void getZ2Ratings_saveZ2RatingsOnAbsenceInDb() {
        //arrange
        when(z2RatingRepositoryMock.findAllZ2RatingsByQuotationIdAndWorkscopeId(anyLong(), anyLong()))
                .thenReturn(emptyList());
        when(z2RatingMapperMock.z2RatingDtoToZ2Ratings(anyList())).thenReturn(new Z2Ratings());
        when(z2RatingMapperMock.z2RatingDtoToZ2Ratings(anyList())).thenReturn(new Z2Ratings());
        //act
        z2RatingService.getZ2Ratings(LONG_ID_1, true);
        //assert
        verify(z2RatingRepositoryMock).findAllZ2RatingsByQuotationIdAndWorkscopeId(anyLong(), anyLong());
        verify(navigationItemServiceMock).getQuotationProgressByQuotationId(anyLong());
        verify(z2RatingMapperMock).z2RatingDtoToZ2Ratings(anyList());
    }

    @Test
    void getZ2Ratings_NoFurtherInteractionForCreatingAndSavingZ2Ratings() {
        //arrange
        var z2RatingDtoList = createZ2RatingDtoList();
        when(z2RatingRepositoryMock.findAllZ2RatingsByQuotationIdAndWorkscopeId(anyLong(), anyLong())).thenReturn(z2RatingDtoList);
        when(z2RatingMapperMock.z2RatingDtoToZ2Ratings(anyList())).thenReturn(new Z2Ratings());
        //act
        z2RatingService.getZ2Ratings(LONG_ID_1, true);
        //assert
        verify(z2RatingRepositoryMock).findAllZ2RatingsByQuotationIdAndWorkscopeId(anyLong(), anyLong());
        verify(navigationItemServiceMock).getQuotationProgressByQuotationId(anyLong());
        verify(z2RatingRepositoryMock, never()).saveAll(anyList());
        verify(z2RatingMapperMock).z2RatingDtoToZ2Ratings(anyList());
    }

    @Test
    void saveZ2Ratings_NoFurtherInteractionWhenNoUpdatedArePresentAndInputValidationIsMissing() {
        //arrange
        var z2RatingsInput = new HashMap<Long, Float>();
        when(z2RatingMapperMock.z2RatingDtoToZ2Ratings(anyList())).thenReturn(new Z2Ratings());
        //act
        z2RatingService.saveZ2Ratings(LONG_ID_1, z2RatingsInput);
        //assert
        verify(z2RatingRepositoryMock).findAllZ2RatingsByQuotationIdAndWorkscopeId(anyLong(), anyLong());
        verify(navigationItemServiceMock).getQuotationProgressByQuotationId(anyLong());
        verify(navigationItemServiceMock).updateProgress(anyLong(), any(QuotationProgress.class), anyBoolean());
    }

    @Test
    void saveZ2Ratings() {
        //arrange
        var z2RatingsInput = new HashMap<Long, Float>();
        var firstUserInput = 98.8f;
        var secondUserInput = 90.8f;
        z2RatingsInput.put(LONG_ID_1, firstUserInput);
        z2RatingsInput.put(LONG_ID_2, secondUserInput);

        when(z2RatingRepositoryMock.findAllByIdIn(anyLong(), anySet()))
                .thenReturn(Z2RatingServiceHelper.createZ2RatingEntities(LONG_ID_1, LONG_ID_2));
        when(z2RatingMapperMock.z2RatingDtoToZ2Ratings(anyList())).thenReturn(new Z2Ratings());
        //act
        z2RatingService.saveZ2Ratings(LONG_ID_1, z2RatingsInput);
        //assert
        var argumentCapture = ArgumentCaptor.forClass(List.class);
        verify(z2RatingRepositoryMock).findAllZ2RatingsByQuotationIdAndWorkscopeId(anyLong(), anyLong());
        verify(navigationItemServiceMock).getQuotationProgressByQuotationId(anyLong());
        //noinspection unchecked
        verify(z2RatingRepositoryMock).saveAll(argumentCapture.capture());
        verify(navigationItemServiceMock).updateProgress(anyLong(), any(QuotationProgress.class), anyBoolean());

        assertEquals(firstUserInput, ((Z2RatingEntity) argumentCapture.getValue().get(0)).getZ2Rating());
        assertEquals(secondUserInput, ((Z2RatingEntity) argumentCapture.getValue().get(1)).getZ2Rating());
    }
}
