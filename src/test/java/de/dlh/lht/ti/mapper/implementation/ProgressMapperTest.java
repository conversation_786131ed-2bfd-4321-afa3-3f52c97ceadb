package de.dlh.lht.ti.mapper.implementation;

import de.dlh.lht.ti.entity.NavigationItemEntity;
import de.dlh.lht.ti.mapper.ProgressMapperImpl;
import de.dlh.lht.ti.model.NavigationSteps;
import de.dlh.lht.ti.model.QuotationProgress;
import java.util.ArrayList;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;


import static de.dlh.lht.ti.utils.ProgressMapperTestHelper.createNavigationItemEntityList;
import static de.dlh.lht.ti.utils.ProgressMapperTestHelper.createNullNavigationItemsProgress;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

@ExtendWith(MockitoExtension.class)
class ProgressMapperTest {

    @InjectMocks
    private ProgressMapperImpl progressMapper;

    @Test
    void navigationItemEntityListToProgress_shouldMapAreAllProgressStepsValidToNull() {
        var navigationItems = createNavigationItemEntityList(null, false);

        var result = progressMapper.navigationItemEntityListToProgress(navigationItems);

        var navigationStep = result.getNavigationSteps().get(0);
        assertNull(navigationStep.getAreAllProgressStepsValid());
        assertEquals(NavigationSteps.COVER, navigationStep.getName());

        var firstProgressStep = navigationStep.getProgressSteps().get(0);
        assertEquals(QuotationProgress.COVER, firstProgressStep.getName());
        assertNull(firstProgressStep.getIsValid());

        var secondProgressStep = navigationStep.getProgressSteps().get(1);
        assertEquals(QuotationProgress.COVER, firstProgressStep.getName());
        assertFalse(secondProgressStep.getIsValid());
    }

    @Test
    void navigationItemEntityListToProgress_shouldMapAreAllProgressStepsValidToFalse() {
        var navigationItems = createNavigationItemEntityList(true, false);

        var result = progressMapper.navigationItemEntityListToProgress(navigationItems);

        var navigationStep = result.getNavigationSteps().get(0);
        assertFalse(navigationStep.getAreAllProgressStepsValid());
        assertEquals(NavigationSteps.COVER, navigationStep.getName());

        var firstProgressStep = navigationStep.getProgressSteps().get(0);
        assertEquals(QuotationProgress.COVER, firstProgressStep.getName());
        assertTrue(firstProgressStep.getIsValid());

        var secondProgressStep = navigationStep.getProgressSteps().get(1);
        assertEquals(QuotationProgress.COVER, firstProgressStep.getName());
        assertFalse(secondProgressStep.getIsValid());
    }

    @Test
    void navigationItemEntityListToProgress_shouldMapAreAllProgressStepsValidToTrue() {
        var navigationItems = createNavigationItemEntityList(true, true);

        var result = progressMapper.navigationItemEntityListToProgress(navigationItems);

        var navigationStep = result.getNavigationSteps().get(0);
        assertTrue(navigationStep.getAreAllProgressStepsValid());
        assertEquals(NavigationSteps.COVER, navigationStep.getName());

        var firstProgressStep = navigationStep.getProgressSteps().get(0);
        assertEquals(QuotationProgress.COVER, firstProgressStep.getName());
        assertTrue(firstProgressStep.getIsValid());

        var secondProgressStep = navigationStep.getProgressSteps().get(1);
        assertEquals(QuotationProgress.COVER, firstProgressStep.getName());
        assertTrue(secondProgressStep.getIsValid());
    }

    @Test
    void navigationItemEntityListToProgress_shouldReturnNullNavigationItemsProgress_onNullListGiven() {
        var nullNavigationItemsProgress = createNullNavigationItemsProgress();

        var result = progressMapper.navigationItemEntityListToProgress(null);

        assertEquals(nullNavigationItemsProgress, result);
    }

    @Test
    void navigationItemEntityListToProgress_shouldReturnNullNavigationItemsProgress_onEmptyListGiven() {
        var emptyList = new ArrayList<NavigationItemEntity>();
        var nullNavigationItemsProgress = createNullNavigationItemsProgress();

        var result = progressMapper.navigationItemEntityListToProgress(emptyList);

        assertEquals(nullNavigationItemsProgress, result);
    }
}
