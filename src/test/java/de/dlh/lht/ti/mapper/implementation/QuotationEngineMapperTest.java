package de.dlh.lht.ti.mapper.implementation;

import de.dlh.lht.ti.entity.WorkscopeEntity;
import de.dlh.lht.ti.mapper.QuotationEngineMapperImpl;
import de.dlh.lht.ti.mapper.WorkscopeMapper;
import de.dlh.lht.ti.model.QuotationWorkscope;
import de.dlh.lht.ti.utils.EngineServiceImplTestHelper;
import de.dlh.lht.ti.utils.QuotationServiceImplTestHelper;
import de.dlh.lht.ti.utils.WorkscopeServiceImplTestHelper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;


import static de.dlh.lht.ti.utils.Conversions.zonedDateTimeToLong;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class QuotationEngineMapperTest {

    @Mock
    private WorkscopeMapper workscopeMapperMock;

    @InjectMocks
    private QuotationEngineMapperImpl quotationEngineMapper;

    @Test
    void quotationEngineEntityToQuotationDetails() {
        var quotationEntity = QuotationServiceImplTestHelper.createQuotationEntity();
        var workscopes = WorkscopeServiceImplTestHelper.createWorkscopeEntityList();
        var engineEntity = EngineServiceImplTestHelper.createEngineEntity();
        when(workscopeMapperMock.workscopeEntityToQuotationWorkscope(any(WorkscopeEntity.class))).thenReturn(any(QuotationWorkscope.class));

        var result = quotationEngineMapper.quotationEngineEntityToQuotationDetails(quotationEntity, engineEntity, workscopes);

        assertEquals(quotationEntity.getId(), result.getId());
        assertEquals(quotationEntity.getVersion(), result.getVersion());
        assertEquals(quotationEntity.getPosition(), result.getPosition());
        assertEquals(quotationEntity.getScenario(), result.getScenario());
        assertEquals(quotationEntity.getProject().getOfferNumber(), result.getProject().getOfferNumber());
        assertEquals(quotationEntity.getProject().getCurrentOwner().getId(), result.getProject().getOwner().getId());
        assertEquals(quotationEntity.getProject().getCurrentOwner().getName(),
                result.getProject().getOwner().getName());
        assertEquals(zonedDateTimeToLong(quotationEntity.getContractStart()), result.getContractStart());
        assertEquals(zonedDateTimeToLong(quotationEntity.getContractEnd()), result.getContractEnd());
        assertEquals(quotationEntity.getCustomer().getThreeLetterCode(), result.getCustomer().getName());
        assertEquals(quotationEntity.getCustomer().getType().toString(), result.getCustomer().getType());
        assertEquals(engineEntity.getName(), result.getEngine().getName());
        assertEquals(zonedDateTimeToLong(quotationEntity.getStatusLastUpdated()), result.getLastUpdate());
        assertEquals(quotationEntity.getStatus(), result.getStatus().ordinal());
        assertEquals(quotationEntity.getUsdExchangeRate().doubleValue(), result.getUsdExchangeRate());
        assertEquals(quotationEntity.isTimeAndMaterial(), result.getTimeAndMaterial());
        assertEquals(quotationEntity.isRoutineFixedPrices(), result.getRoutineFixedPrices());
    }
}
