package de.dlh.lht.ti.mapper.implementation;

import de.dlh.lht.ti.mapper.SubcontractPricingMapperImpl;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;


import static de.dlh.lht.ti.utils.SubcontractPricingTestHelper.createSubcontractDtoList;
import static de.dlh.lht.ti.utils.SubcontractPricingTestHelper.createSubcontractInput;
import static de.dlh.lht.ti.utils.SubcontractPricingTestHelper.createSubcontractList;
import static de.dlh.lht.ti.utils.SubcontractPricingTestHelper.createSubcontractPricingItemEntity;
import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(MockitoExtension.class)
class SubcontractPricingMapperTest {

    @InjectMocks
    private SubcontractPricingMapperImpl subcontractPricingMapper;

    @Test
    void subcontractDtoListToSubcontractList() {
        var subcontractDtoList = createSubcontractDtoList();
        var expectedResult = createSubcontractList();

        var result = subcontractPricingMapper.subcontractDtoListToSubcontractList(subcontractDtoList);

        assertEquals(expectedResult, result);
    }

    @Test
    void subcontractInputToSubcontractPricingItemEntity() {
        var subcontractInput = createSubcontractInput();
        var subcontractPricingItem = createSubcontractPricingItemEntity();
        var expectedResult = createSubcontractPricingItemEntity();

        var result = subcontractPricingMapper.subcontractInputToSubcontractPricingItemEntity(subcontractInput, subcontractPricingItem);

        assertEquals(expectedResult.getId(), result.getId());
        assertEquals(expectedResult.getCap(), result.getCap());
        assertEquals(expectedResult.getMargin(), result.getMargin());
    }
}
