package de.dlh.lht.ti.mapper.implementation;

import de.dlh.lht.ti.mapper.PartMapperImpl;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;


import static de.dlh.lht.ti.utils.PartServiceImplTestHelper.createPartDto;
import static de.dlh.lht.ti.utils.PartServiceImplTestHelper.createPartEntity;
import static de.dlh.lht.ti.utils.PartServiceImplTestHelper.createPartRaw;
import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(MockitoExtension.class)
class PartMapperTest {

    @InjectMocks
    private PartMapperImpl partMapper;

    @Test
    void partRawToPartDto() {
        var partRaw = createPartRaw();
        var partDto = createPartDto();

        var result = partMapper.partRawToPartDto(partRaw);

        assertEquals(partDto, result);
    }

    @Test
    void partEntityToPartDto() {
        var part = createPartEntity();
        var partDto = createPartDto();

        var result = partMapper.partEntityToPartDto(part);

        assertEquals(partDto, result);
    }
}
