package de.dlh.lht.ti.mapper.implementation;

import de.dlh.lht.ti.mapper.SubcontractMetadataMapperImpl;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;


import static de.dlh.lht.ti.utils.SubcontractPricingTestHelper.createSubcontractMetadataEntity;
import static de.dlh.lht.ti.utils.SubcontractPricingTestHelper.createSubcontractMetadataRaw;
import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(MockitoExtension.class)
class SubcontractMetadataMapperTest {

    @InjectMocks
    private SubcontractMetadataMapperImpl subcontractMetadataMapper;

    @Test
    void subcontractMetadataRawToSubcontractMetadataEntity() {
        var subcontractMetadataRaw = createSubcontractMetadataRaw();
        var subcontractMetadataEntity = createSubcontractMetadataEntity();

        var result = subcontractMetadataMapper.subcontractMetadataRawToSubcontractMetadataEntity(subcontractMetadataRaw, 1L, 1L);

        assertEquals(subcontractMetadataEntity.getQuantity(), result.getQuantity());
        assertEquals(subcontractMetadataEntity.getWeightedQuantity(), result.getWeightedQuantity());
        assertEquals(subcontractMetadataEntity.getValue(), result.getValue());
        assertEquals(subcontractMetadataEntity.getCurrency(), result.getCurrency());
        assertEquals(subcontractMetadataEntity.getYear(), result.getYear());
        assertEquals(subcontractMetadataEntity.getName(), result.getName());
        assertEquals(subcontractMetadataEntity.getSubcontractPricingItemId(), result.getSubcontractPricingItemId());
        assertEquals(subcontractMetadataEntity.getWorkscopeId(), result.getWorkscopeId());
    }
}
