package de.dlh.lht.ti.mapper.implementation;

import de.dlh.lht.ti.mapper.CustomerMapperImpl;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;


import static de.dlh.lht.ti.utils.CustomerServiceImplTestHelper.createCustomerDto;
import static de.dlh.lht.ti.utils.CustomerServiceImplTestHelper.createCustomerEntity;
import static de.dlh.lht.ti.utils.CustomerServiceImplTestHelper.createCustomerRaw;
import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(MockitoExtension.class)
class CustomerMapperTest {

    @InjectMocks
    private CustomerMapperImpl customerMapper;

    @Test
    void customerRawToCustomerDto() {
        var customerRaw = createCustomerRaw();
        var customerDto = createCustomerDto();

        var result = customerMapper.customerRawToCustomerDto(customerRaw);

        assertEquals(customerDto, result);
    }

    @Test
    void customerDtoToCustomerEntity() {
        var customerDto = createCustomerDto();
        var customer = createCustomerEntity();

        var result = customerMapper.customerDtoToCustomerEntity(customerDto);

        assertEquals(customer.getThreeLetterCode(), result.getThreeLetterCode());
        assertEquals(customer.getType(), result.getType());
    }

    @Test
    void customerEntityToCustomerDto() {
        var customer = createCustomerEntity();
        var customerDto = createCustomerDto();

        var result = customerMapper.customerEntityToCustomerDto(customer);

        assertEquals(customerDto, result);
    }
}
