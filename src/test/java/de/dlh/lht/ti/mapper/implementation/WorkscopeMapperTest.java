package de.dlh.lht.ti.mapper.implementation;

import de.dlh.lht.ti.mapper.WorkscopeMapperImpl;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;


import static de.dlh.lht.ti.utils.TestConstants.LONG_ID_1;
import static de.dlh.lht.ti.utils.WorkscopeServiceImplTestHelper.createWorkscopeEntity;
import static de.dlh.lht.ti.utils.WorkscopeServiceImplTestHelper.createWorkscopeRaw;
import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(MockitoExtension.class)
class WorkscopeMapperTest {

    @InjectMocks
    private WorkscopeMapperImpl workscopeMapper;

    @Test
    void workscopeEntityToQuotationWorkscope() {
        var workscopeEntity = createWorkscopeEntity(LONG_ID_1);

        var result = workscopeMapper.workscopeEntityToQuotationWorkscope(workscopeEntity);

        assertEquals(workscopeEntity.getName(), result.getName());
        assertEquals(workscopeEntity.getWorkscopeClass().name(), result.getPropertyClass());
    }

    @Test
    void workscopeRawToWorkscopeDto() {
        var workscopeRaw = createWorkscopeRaw();

        var result = workscopeMapper.workscopeRawToWorkscopeDto(workscopeRaw);

        assertEquals(workscopeRaw.getName(), result.getName());
        assertEquals(workscopeRaw.getWorkscopeClass().name(), result.getWorkscopeClass().name());
    }
}
