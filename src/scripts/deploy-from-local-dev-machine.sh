#!/bin/bash -e

# select namespace
unset http_proxy
unset https_proxy
oc project lht-platform-sample-applications-nonprod

# build application
mvn clean package -DskipTests

# apply helm templates
helm upgrade --atomic --install spring-sample-api src/cicd/helm/spring-sample -f src/cicd/helm/spring-sample/values.develop.yaml

# s2i build
oc start-build spring-sample-api --from-file=$(ls target/spring-sample-*.jar) --follow --wait

# tag to develop
oc tag spring-sample-api:latest spring-sample-api:develop
